#include "rclcpp/rclcpp.hpp"
#include "tf2_ros/transform_listener.h"
#include "tf2_ros/buffer.h"
#include "geometry_msgs/msg/transform_stamped.hpp"
#include "tf2/LinearMath/Quaternion.h"
#include "tf2/utils.h"

class RB_builder_node : public rclcpp::Node
{
public:
    RB_builder_node() : Node("RB_builder_node")
    {
        RCLCPP_INFO(this->get_logger(), "RB_builder_node launched");
    }

    void get_tf()
    {
        buffer_ = std::make_shared<tf2_ros::Buffer>(this->get_clock());
        listener_ = std::make_shared<tf2_ros::TransformListener>(*buffer_, this, false);
        transformation = buffer_->lookupTransform("camera", "target", rclcpp::Time(0));
        auto rotation = transformation.transform.rotation;
        auto translation = transformation.transform.translation;
        try
        {
            tf2::getEulerYPR(rotation, yaw, pitch, roll);
            center[0] = translation.x;
            center[1] = translation.y;
            center[2] = translation.z;
        }
        catch (tf2::TransformException &ex)
        {
            RCLCPP_ERROR(logger, "TransformException: %s", ex.what());
            return;
        }

        for (int i = 0; i < 4; i++)
        {
            try
            {
                transformation = buffer_->lookupTransform("corner" + std::to_string(i), "target", rclcpp::Time(0));
                auto translation = transformation.transform.translation;
                corners[i][0] = translation.x;
                corners[i][1] = translation.y;
                corners[i][2] = translation.z;
            }
            catch (tf2::TransformException &ex)
            {
                RCLCPP_ERROR(logger, "TransformException: %s", ex.what());
                return;
            }
        }

        RCLCPP_INFO(logger, "rotation: %f, %f, %f", roll, pitch, yaw);
    }

    void create_object()
    {
    }

private:
    rclcpp::Logger logger = this->get_logger();
    std::shared_ptr<tf2_ros::TransformListener> listener_;
    std::shared_ptr<tf2_ros::Buffer> buffer_;
    geometry_msgs::msg::TransformStamped transformation;
    double roll, pitch, yaw;
    std::vector<double> center;
    std::vector<std::vector<double>> corners;
};

int main(int argc, char **argv)
{
    rclcpp::init(argc, argv);
    auto node = std::make_shared<RB_builder_node>();
    node->get_tf();
    node->create_object();
    rclcpp::spin(node);
    rclcpp::shutdown();
    return 0;
}