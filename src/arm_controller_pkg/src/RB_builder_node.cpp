#include "rclcpp/rclcpp.hpp"
#include "tf2_ros/transform_listener.h"
#include "tf2_ros/buffer.h"
#include "geometry_msgs/msg/transform_stamped.hpp"
#include "tf2/LinearMath/Quaternion.h"
#include "tf2/utils.h"
// MoveIt2 headers for collision object creation and planning scene interface
#include <moveit/planning_scene_interface/planning_scene_interface.h>
#include <moveit_msgs/msg/collision_object.hpp>
#include <shape_msgs/msg/solid_primitive.hpp>
#include <geometry_msgs/msg/pose.hpp>
// TF2 geometry messages for coordinate transformations
#if __has_include(<tf2_geometry_msgs/tf2_geometry_msgs.hpp>)
#include <tf2_geometry_msgs/tf2_geometry_msgs.hpp>
#else
#include <tf2_geometry_msgs/tf2_geometry_msgs.h>
#endif

class RB_builder_node : public rclcpp::Node
{
public:
    RB_builder_node() : Node("RB_builder_node")
    {
        RCLCPP_INFO(this->get_logger(), "RB_builder_node launched");

        // Initialize data structures for tf coordinate data
        center.resize(3);  // x, y, z coordinates
        corners.resize(4); // 4 corner points
        for(int i = 0; i < 4; i++) {
            corners[i].resize(3); // each corner has x, y, z coordinates
        }
        
        // Initialize data structures for change detection
        last_center_.resize(3);
        last_corners_.resize(4);
        for(int i = 0; i < 4; i++) {
            last_corners_[i].resize(3);
        }
        last_roll_ = last_pitch_ = last_yaw_ = 0.0;
        buffer_ = std::make_shared<tf2_ros::Buffer>(this->get_clock());
        buffer_->setUsingDedicatedThread(true);
        listener_ = std::make_shared<tf2_ros::TransformListener>(*buffer_, this, false);
        
        tf_received_ = false;
        object_created_ = false;
        last_update_time_ = this->now();
        
        // 使用更高频率的定时器以实现实时更新
        timer_ = this->create_wall_timer(
            std::chrono::milliseconds(100), 
            std::bind(&RB_builder_node::timer_callback, this));
    }

private:
    void timer_callback()
    {
        if (get_tf()) {
            if (has_significant_change()) {
                RCLCPP_INFO(logger, "Detected significant TF change, updating model...");
                create_object();
                last_update_time_ = this->now();
                tf_received_ = true;
                update_last_tf_data();
            }
        }
    }

    bool get_tf()
    {
        try
        {
            if (!buffer_->canTransform("camera", "target", tf2::TimePointZero, tf2::durationFromSec(0.1))) {
                RCLCPP_WARN_THROTTLE(logger, *this->get_clock(), 2000, "Transform from 'camera' to 'target' not available yet");
                return false;
            }

            transformation = buffer_->lookupTransform("camera", "target", tf2::TimePointZero);
            auto rotation = transformation.transform.rotation;
            auto translation = transformation.transform.translation;
            
            tf2::getEulerYPR(rotation, yaw, pitch, roll);
            center[0] = translation.x;
            center[1] = translation.y;
            center[2] = translation.z;

            RCLCPP_INFO(logger, "Got center transform: [%.3f, %.3f, %.3f], rotation: [%.3f, %.3f, %.3f]", 
                       center[0], center[1], center[2], roll, pitch, yaw);

            bool all_corners_available = true;
            for (int i = 0; i < 4; i++)
            {
                std::string corner_frame = "corner" + std::to_string(i);
                
                if (!buffer_->canTransform("camera", corner_frame, tf2::TimePointZero, tf2::durationFromSec(0.1))) {
                    RCLCPP_WARN_THROTTLE(logger, *this->get_clock(), 2000, "Transform from 'camera' to '%s' not available", corner_frame.c_str());
                    all_corners_available = false;
                    continue;
                }

                auto corner_transform = buffer_->lookupTransform("camera", corner_frame, tf2::TimePointZero);
                auto corner_translation = corner_transform.transform.translation;
                corners[i][0] = corner_translation.x;
                corners[i][1] = corner_translation.y;
                corners[i][2] = corner_translation.z;
                
                RCLCPP_DEBUG(logger, "Got corner %d: [%.3f, %.3f, %.3f]", i, corners[i][0], corners[i][1], corners[i][2]);
            }

            if (!all_corners_available) {
                RCLCPP_WARN_THROTTLE(logger, *this->get_clock(), 2000, "Not all corner transforms available yet");
                return false;
            }

            RCLCPP_INFO(logger, "Successfully got all transforms");
            return true;
        }
        catch (tf2::TransformException &ex)
        {
            RCLCPP_WARN_THROTTLE(logger, *this->get_clock(), 2000, "TransformException: %s", ex.what());
            return false;
        }
    }

    void create_object()
    {
        // Validate tf data before proceeding
        if(center.size() != 3 || corners.size() != 4) {
            RCLCPP_ERROR(logger, "Invalid tf data: center or corners not properly initialized");
            RCLCPP_ERROR(logger, "center.size()=%zu, corners.size()=%zu", center.size(), corners.size());
            return;
        }

        // Validate corners data structure
        for(int i = 0; i < 4; i++) {
            if(corners[i].size() != 3) {
                RCLCPP_ERROR(logger, "Invalid corners data: corner[%d].size()=%zu", i, corners[i].size());
                return;
            }
        }

        RCLCPP_INFO(logger, "TF data validation passed: center[3], corners[4][3]");

        if (object_created_) {
            remove_existing_object();
        }

        // Create CollisionObject for cube slot with 5 BOX primitives
        moveit_msgs::msg::CollisionObject collision_object;
        collision_object.id = "cube_slot";
        collision_object.header.frame_id = "world";  // 改为world坐标系，让MoveIt正确处理
        collision_object.header.stamp = this->get_clock()->now();
        collision_object.operation = collision_object.ADD;

        // Initialize 5 BOX primitives for the cube slot faces
        collision_object.primitives.resize(5);
        collision_object.primitive_poses.resize(5);

        // Configure each primitive as BOX type
        for(int i = 0; i < 5; i++) {
            collision_object.primitives[i].type = shape_msgs::msg::SolidPrimitive::BOX;
            collision_object.primitives[i].dimensions.resize(3);
        }

        // Calculate geometry parameters for cube slot faces
        std::vector<geometry_msgs::msg::Pose> box_poses(5);
        std::vector<std::vector<double>> box_dimensions(5);
        calculateBoxGeometry(box_poses, box_dimensions);

        // Validate calculated geometry parameters
        for(int i = 0; i < 5; i++) {
            if(box_dimensions[i].size() != 3) {
                RCLCPP_ERROR(logger, "Invalid box dimensions: box[%d].size()=%zu", i, box_dimensions[i].size());
                return;
            }
            if(box_dimensions[i][0] <= 0 || box_dimensions[i][1] <= 0 || box_dimensions[i][2] <= 0) {
                RCLCPP_ERROR(logger, "Invalid box dimensions: box[%d]=[%.3f,%.3f,%.3f]",
                           i, box_dimensions[i][0], box_dimensions[i][1], box_dimensions[i][2]);
                return;
            }
        }

        RCLCPP_INFO(logger, "Cube slot geometry validated successfully");

        // Set dimensions and poses for each BOX primitive
        for(int i = 0; i < 5; i++) {
            // Set BOX dimensions (x, y, z) - need to copy elements individually
            for(int j = 0; j < 3; j++) {
                collision_object.primitives[i].dimensions[j] = box_dimensions[i][j];
            }
            collision_object.primitive_poses[i] = box_poses[i];
        }

        RCLCPP_INFO(logger, "CollisionObject created with 5 BOX primitives for cube slot");
        RCLCPP_INFO(logger, "Frame ID: %s, Operation: ADD", collision_object.header.frame_id.c_str());
        RCLCPP_INFO(logger, "BOX dimensions configured: bottom[%.3f,%.3f,%.3f], sides[%.3f,%.3f,%.3f]",
                   box_dimensions[0][0], box_dimensions[0][1], box_dimensions[0][2],
                   box_dimensions[1][0], box_dimensions[1][1], box_dimensions[1][2]);

        // Create PlanningSceneInterface and add collision object to planning scene
        moveit::planning_interface::PlanningSceneInterface planning_scene_interface;
        planning_scene_interface.applyCollisionObject(collision_object);

        object_created_ = true;
        RCLCPP_INFO(logger, "Cube slot collision object updated in planning scene (real-time modeling)");
        RCLCPP_INFO(logger, "Slot dimensions: outer=288mm, inner=240mm, thickness=24mm");
    }

private:
    void remove_existing_object()
    {
        moveit_msgs::msg::CollisionObject remove_object;
        remove_object.id = "cube_slot";
        remove_object.header.frame_id = "camera";
        remove_object.operation = remove_object.REMOVE;
        
        moveit::planning_interface::PlanningSceneInterface planning_scene_interface;
        planning_scene_interface.applyCollisionObject(remove_object);
        
        RCLCPP_DEBUG(logger, "Removed existing cube_slot collision object");
    }

    bool has_significant_change()
    {
        const double threshold = 0.005;  // 5mm threshold for position changes
        const double angle_threshold = 0.05;  // ~3 degrees for rotation changes
        
        if (!tf_received_) {
            return true;
        }
        
        for (int i = 0; i < 3; i++) {
            if (std::abs(center[i] - last_center_[i]) > threshold) {
                return true;
            }
        }
        
        if (std::abs(roll - last_roll_) > angle_threshold ||
            std::abs(pitch - last_pitch_) > angle_threshold ||
            std::abs(yaw - last_yaw_) > angle_threshold) {
            return true;
        }
        
        for (int i = 0; i < 4; i++) {
            for (int j = 0; j < 3; j++) {
                if (std::abs(corners[i][j] - last_corners_[i][j]) > threshold) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    void update_last_tf_data()
    {
        last_center_ = center;
        last_corners_ = corners;
        last_roll_ = roll;
        last_pitch_ = pitch;
        last_yaw_ = yaw;
    }

    // Calculate geometry parameters for cube slot (5 BOX primitives)
    void calculateBoxGeometry(std::vector<geometry_msgs::msg::Pose>& poses,
                             std::vector<std::vector<double>>& dimensions)
    {
        // Constants for cube slot dimensions (convert mm to m)
        const double outer_size = 0.288;  // 288mm outer edge length
        const double inner_size = 0.240;  // 240mm inner edge length
        const double thickness = (outer_size - inner_size) / 2.0;  // 24mm = 0.024m wall thickness

        // Initialize output vectors for 5 faces (bottom + 4 sides)
        poses.resize(5);
        dimensions.resize(5);

        // Create rotation matrix from roll, pitch, yaw
        tf2::Quaternion q;
        q.setRPY(roll, pitch, yaw);

        // Base position from center coordinates
        double base_x = center[0];
        double base_y = center[1];
        double base_z = center[2];

        // Face 0: Bottom face (240x240x24mm)
        dimensions[0] = {inner_size, inner_size, thickness};
        poses[0].position.x = base_x;
        poses[0].position.y = base_y;
        poses[0].position.z = base_z - thickness/2.0;  // Below center
        poses[0].orientation.x = q.x();
        poses[0].orientation.y = q.y();
        poses[0].orientation.z = q.z();
        poses[0].orientation.w = q.w();

        // Face 1: Front side (288x24x24mm) - opening direction opposite to normal
        dimensions[1] = {outer_size, thickness, thickness};
        poses[1].position.x = base_x + (inner_size/2.0 + thickness/2.0);
        poses[1].position.y = base_y;
        poses[1].position.z = base_z + thickness/2.0;
        poses[1].orientation = poses[0].orientation;

        // Face 2: Back side (288x24x24mm)
        dimensions[2] = {outer_size, thickness, thickness};
        poses[2].position.x = base_x - (inner_size/2.0 + thickness/2.0);
        poses[2].position.y = base_y;
        poses[2].position.z = base_z + thickness/2.0;
        poses[2].orientation = poses[0].orientation;

        // Face 3: Left side (24x288x24mm)
        dimensions[3] = {thickness, outer_size, thickness};
        poses[3].position.x = base_x;
        poses[3].position.y = base_y + (inner_size/2.0 + thickness/2.0);
        poses[3].position.z = base_z + thickness/2.0;
        poses[3].orientation = poses[0].orientation;

        // Face 4: Right side (24x288x24mm)
        dimensions[4] = {thickness, outer_size, thickness};
        poses[4].position.x = base_x;
        poses[4].position.y = base_y - (inner_size/2.0 + thickness/2.0);
        poses[4].position.z = base_z + thickness/2.0;
        poses[4].orientation = poses[0].orientation;

        RCLCPP_INFO(logger, "Calculated cube slot geometry: outer=%.3fm, inner=%.3fm, thickness=%.3fm",
                   outer_size, inner_size, thickness);
    }

    rclcpp::TimerBase::SharedPtr timer_;
    rclcpp::Logger logger = this->get_logger();
    std::shared_ptr<tf2_ros::TransformListener> listener_;
    std::shared_ptr<tf2_ros::Buffer> buffer_;
    geometry_msgs::msg::TransformStamped transformation;
    double roll, pitch, yaw;
    std::vector<double> center;
    std::vector<std::vector<double>> corners;
    bool tf_received_;
    bool object_created_;
    
    rclcpp::Time last_update_time_;
    std::vector<double> last_center_;
    std::vector<std::vector<double>> last_corners_;
    double last_roll_, last_pitch_, last_yaw_;
};

int main(int argc, char **argv)
{
    rclcpp::init(argc, argv);
    auto node = std::make_shared<RB_builder_node>();
    
    RCLCPP_INFO(node->get_logger(), "Waiting for TF data from RB_detector_node...");
    rclcpp::spin(node);
    
    rclcpp::shutdown();
    return 0;
}