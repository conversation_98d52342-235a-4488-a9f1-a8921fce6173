#include "rclcpp/rclcpp.hpp"
#include "moveit/task_constructor/container.h"
#include "moveit/task_constructor/task.h"
#include "moveit/task_constructor/solvers.h"
#include "moveit/task_constructor/stages.h"
#include "moveit/planning_interface/planning_interface.h"
#include "moveit/planning_scene_interface/planning_scene_interface.h"
#include "geometry_msgs/msg/transform_stamped.hpp"
#include "tf2_geometry_msgs/tf2_geometry_msgs.hpp"
#include "tf2_ros/transform_listener.h"
#include "tf2/LinearMath/Quaternion.h"
#include "tf2/utils.h"
#include "tf2_ros/buffer.h"
class MTC
{
public:
    MTC (const rclcpp::Node::SharedPtr &node) 
    : node_(node){
        RCLCPP_INFO(node->get_logger(), "MTC launched");
        task_ = createTask();
    }

    void doTask(){
        task_ = createTask();
        try
        {
            task_.init(); // 初始化任务
        }
        catch (moveit::task_constructor::InitStageException &e)
        {
            RCLCPP_ERROR_STREAM(logger, e);
            return;
        }

        if (!task_.plan(5))
        {
            RCLCPP_ERROR_STREAM(logger, "Task planning failed");
            return;
        }
        task_.introspection().publishSolution(*task_.solutions().front());

        auto result = task_.execute(*task_.solutions().front());
        if (result.val != moveit_msgs::msg::MoveItErrorCodes::SUCCESS)
        {
            RCLCPP_ERROR_STREAM(logger, "Task execution failed");
            return;
        }

        return;
    }

    virtual moveit::task_constructor::Task createTask();
private:
    rclcpp::Node::SharedPtr node_;
    rclcpp::Logger logger = node_->get_logger();
    moveit::task_constructor::Task task_;

};