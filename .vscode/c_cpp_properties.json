{"configurations": [{"name": "Linux", "includePath": ["${workspaceFolder}/**", "/opt/ros/humble/include/**", "/usr/include/opencv4", "/opt/ros/humble/include/cv_bridge", "/home/<USER>/moveit2_ws/**", "/opt/ros/humble/include/rclcpp", "/opt/ros/humble/include/tf2_geometry_msgs", "/usr/include/eigen3", "/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/include", "/opt/ros/humble/include/unique_identifier_msgs", "/opt/ros/humble/include/tf2", "/opt/ros/humble/include/geometry_msgs"], "defines": [], "compilerPath": "/usr/bin/gcc", "cStandard": "c17", "cppStandard": "gnu++17", "intelliSenseMode": "linux-gcc-arm64"}], "version": 4}