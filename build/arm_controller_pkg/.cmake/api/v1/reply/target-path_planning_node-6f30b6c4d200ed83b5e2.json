{"artifacts": [{"path": "path_planning_node"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "install", "target_link_libraries", "ament_target_dependencies", "set_target_properties", "include", "find_package", "find_dependency", "set_property", "boost_find_component", "add_compile_options", "include_directories", "target_include_directories"], "files": ["CMakeLists.txt", "/opt/ros/humble/share/ament_cmake_target_dependencies/cmake/ament_target_dependencies.cmake", "/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/export_moveit_ros_planning_interfaceExport.cmake", "/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_targets-extras.cmake", "/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake", "/home/<USER>/moveit2_ws/install/moveit_ros_move_group/share/moveit_ros_move_group/cmake/export_moveit_ros_move_groupExport.cmake", "/home/<USER>/moveit2_ws/install/moveit_ros_move_group/share/moveit_ros_move_group/cmake/ament_cmake_export_targets-extras.cmake", "/home/<USER>/moveit2_ws/install/moveit_ros_move_group/share/moveit_ros_move_group/cmake/moveit_ros_move_groupConfig.cmake", "/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake", "/home/<USER>/moveit2_ws/install/moveit_core/share/moveit_core/cmake/export_moveit_coreExport.cmake", "/home/<USER>/moveit2_ws/install/moveit_core/share/moveit_core/cmake/ament_cmake_export_targets-extras.cmake", "/home/<USER>/moveit2_ws/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake", "/opt/ros/humble/share/rclcpp_lifecycle/cmake/rclcpp_lifecycleExport.cmake", "/opt/ros/humble/share/rclcpp_lifecycle/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rclcpp_lifecycle/cmake/rclcpp_lifecycleConfig.cmake", "/opt/ros/humble/share/generate_parameter_library/cmake/generate_parameter_library-extras.cmake", "/opt/ros/humble/share/generate_parameter_library/cmake/generate_parameter_libraryConfig.cmake", "/home/<USER>/moveit2_ws/install/moveit_core/share/moveit_core/cmake/ament_cmake_export_dependencies-extras.cmake", "/home/<USER>/moveit2_ws/install/moveit_ros_planning/share/moveit_ros_planning/cmake/export_moveit_ros_planningExport.cmake", "/home/<USER>/moveit2_ws/install/moveit_ros_planning/share/moveit_ros_planning/cmake/ament_cmake_export_targets-extras.cmake", "/home/<USER>/moveit2_ws/install/moveit_ros_planning/share/moveit_ros_planning/cmake/moveit_ros_planningConfig.cmake", "/usr/lib/aarch64-linux-gnu/cmake/fcl/fcl-targets.cmake", "/usr/lib/aarch64-linux-gnu/cmake/fcl/fcl-config.cmake", "/opt/ros/humble/share/octomap/octomap-targets.cmake", "/opt/ros/humble/share/octomap/octomap-config.cmake", "/usr/local/cmake/share/cmake-4.0/Modules/CMakeFindDependencyMacro.cmake", "/opt/ros/humble/share/geometric_shapes/cmake/export_geometric_shapesExport.cmake", "/opt/ros/humble/share/geometric_shapes/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/geometric_shapes/cmake/geometric_shapesConfig.cmake", "/opt/ros/humble/share/resource_retriever/cmake/resource_retrieverExport.cmake", "/opt/ros/humble/share/resource_retriever/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/resource_retriever/cmake/resource_retrieverConfig.cmake", "/opt/ros/humble/share/geometric_shapes/cmake/ament_cmake_export_dependencies-extras.cmake", "/usr/local/cmake/share/cmake-4.0/Modules/FindCURL.cmake", "/opt/ros/humble/share/libcurl_vendor/cmake/libcurl_vendor-extras.cmake", "/opt/ros/humble/share/libcurl_vendor/cmake/libcurl_vendorConfig.cmake", "/opt/ros/humble/share/resource_retriever/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/lib/aarch64-linux-gnu/urdfdom/cmake/urdfdomExport.cmake", "/opt/ros/humble/lib/aarch64-linux-gnu/urdfdom/cmake/urdfdom-config.cmake", "/opt/ros/humble/share/urdf/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/urdf/cmake/urdfConfig.cmake", "/home/<USER>/moveit2_ws/install/srdfdom/share/srdfdom/cmake/ament_cmake_export_dependencies-extras.cmake", "/home/<USER>/moveit2_ws/install/srdfdom/share/srdfdom/cmake/srdfdomConfig.cmake", "/opt/ros/humble/share/moveit_msgs/cmake/export_moveit_msgs__rosidl_typesupport_fastrtps_cExport.cmake", "/opt/ros/humble/share/moveit_msgs/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/moveit_msgs/cmake/moveit_msgsConfig.cmake", "/opt/ros/humble/share/moveit_msgs/cmake/moveit_msgs__rosidl_typesupport_introspection_cExport.cmake", "/opt/ros/humble/share/moveit_msgs/cmake/export_moveit_msgs__rosidl_typesupport_fastrtps_cppExport.cmake", "/opt/ros/humble/share/moveit_msgs/cmake/moveit_msgs__rosidl_typesupport_introspection_cppExport.cmake", "/opt/ros/humble/share/moveit_msgs/cmake/moveit_msgs__rosidl_typesupport_cppExport.cmake", "/opt/ros/humble/share/moveit_msgs/cmake/export_moveit_msgs__rosidl_generator_pyExport.cmake", "/opt/ros/humble/share/moveit_msgs/cmake/moveit_msgs__rosidl_typesupport_cExport.cmake", "/opt/ros/humble/share/moveit_msgs/cmake/export_moveit_msgs__rosidl_generator_cExport.cmake", "/usr/lib/aarch64-linux-gnu/cmake/boost_thread-1.74.0/boost_thread-config.cmake", "/usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake", "/usr/local/cmake/share/cmake-4.0/Modules/FindBoost.cmake", "/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ConfigExtras.cmake", "/home/<USER>/moveit2_ws/install/moveit_ros_warehouse/share/moveit_ros_warehouse/cmake/export_moveit_ros_warehouseExport.cmake", "/home/<USER>/moveit2_ws/install/moveit_ros_warehouse/share/moveit_ros_warehouse/cmake/ament_cmake_export_targets-extras.cmake", "/home/<USER>/moveit2_ws/install/moveit_ros_warehouse/share/moveit_ros_warehouse/cmake/moveit_ros_warehouseConfig.cmake", "/opt/ros/humble/share/tf2_ros/cmake/export_tf2_rosExport.cmake", "/opt/ros/humble/share/tf2_ros/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/tf2_ros/cmake/tf2_rosConfig.cmake", "/opt/ros/humble/share/tf2_geometry_msgs/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/tf2_geometry_msgs/cmake/tf2_geometry_msgsConfig.cmake", "/opt/ros/humble/share/rclcpp/cmake/rclcppExport.cmake", "/opt/ros/humble/share/rclcpp/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rclcpp/cmake/rclcppConfig.cmake", "/opt/ros/humble/share/rclcpp_action/cmake/rclcpp_actionExport.cmake", "/opt/ros/humble/share/rclcpp_action/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rclcpp_action/cmake/rclcpp_actionConfig.cmake", "/opt/ros/humble/share/tf2_ros/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/rcl_yaml_param_parserExport.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcl_yaml_param_parser/cmake/rcl_yaml_param_parserConfig.cmake", "/opt/ros/humble/share/rcl/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rcl/cmake/rclConfig.cmake", "/opt/ros/humble/share/libstatistics_collector/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/libstatistics_collector/cmake/libstatistics_collectorConfig.cmake", "/opt/ros/humble/share/rclcpp/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rcl/cmake/rclExport.cmake", "/opt/ros/humble/share/rcl/cmake/ament_cmake_export_targets-extras.cmake", "/usr/lib/aarch64-linux-gnu/cmake/fmt/fmt-targets.cmake", "/usr/lib/aarch64-linux-gnu/cmake/fmt/fmt-config.cmake", "/usr/lib/aarch64-linux-gnu/cmake/spdlog/spdlogConfig.cmake", "/opt/ros/humble/share/rcl_logging_spdlog/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rcl_logging_spdlog/cmake/rcl_logging_spdlogConfig.cmake", "/opt/ros/humble/share/tf2_msgs/cmake/export_tf2_msgs__rosidl_typesupport_fastrtps_cExport.cmake", "/opt/ros/humble/share/tf2_msgs/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/tf2_msgs/cmake/tf2_msgsConfig.cmake", "/opt/ros/humble/share/tf2_msgs/cmake/tf2_msgs__rosidl_typesupport_introspection_cExport.cmake", "/opt/ros/humble/share/tf2_msgs/cmake/export_tf2_msgs__rosidl_typesupport_fastrtps_cppExport.cmake", "/opt/ros/humble/share/tf2_msgs/cmake/tf2_msgs__rosidl_typesupport_introspection_cppExport.cmake", "/opt/ros/humble/share/tf2_msgs/cmake/tf2_msgs__rosidl_typesupport_cppExport.cmake", "/opt/ros/humble/share/tf2_msgs/cmake/export_tf2_msgs__rosidl_generator_pyExport.cmake", "/opt/ros/humble/share/tf2_msgs/cmake/tf2_msgs__rosidl_typesupport_cExport.cmake", "/opt/ros/humble/share/tf2_msgs/cmake/export_tf2_msgs__rosidl_generator_cExport.cmake", "/opt/ros/humble/share/tf2_geometry_msgs/cmake/export_tf2_geometry_msgsExport.cmake", "/opt/ros/humble/share/tf2_geometry_msgs/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/geometry_msgs/cmake/export_geometry_msgs__rosidl_typesupport_fastrtps_cExport.cmake", "/opt/ros/humble/share/geometry_msgs/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/geometry_msgs/cmake/geometry_msgsConfig.cmake", "/opt/ros/humble/share/tf2/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/tf2/cmake/tf2Config.cmake", "/opt/ros/humble/share/geometry_msgs/cmake/geometry_msgs__rosidl_typesupport_introspection_cExport.cmake", "/opt/ros/humble/share/geometry_msgs/cmake/export_geometry_msgs__rosidl_typesupport_fastrtps_cppExport.cmake", "/opt/ros/humble/share/geometry_msgs/cmake/geometry_msgs__rosidl_typesupport_introspection_cppExport.cmake", "/opt/ros/humble/share/geometry_msgs/cmake/geometry_msgs__rosidl_typesupport_cppExport.cmake", "/opt/ros/humble/share/geometry_msgs/cmake/export_geometry_msgs__rosidl_generator_pyExport.cmake", "/opt/ros/humble/share/geometry_msgs/cmake/geometry_msgs__rosidl_typesupport_cExport.cmake", "/opt/ros/humble/share/geometry_msgs/cmake/export_geometry_msgs__rosidl_generator_cExport.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cppExport.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cppConfig.cmake", "/opt/ros/humble/share/builtin_interfaces/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/builtin_interfaces/cmake/builtin_interfacesConfig.cmake", "/opt/ros/humble/share/rcutils/cmake/rcutilsExport.cmake", "/opt/ros/humble/share/rcutils/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/rcutils/cmake/rcutilsConfig.cmake", "/opt/ros/humble/share/rosidl_runtime_c/cmake/ament_cmake_export_dependencies-extras.cmake", "/opt/ros/humble/share/rosidl_runtime_c/cmake/rosidl_runtime_cConfig.cmake", "/home/<USER>/moveit2_ws/install/srdfdom/share/srdfdom/cmake/srdfdomTargetsExport.cmake", "/home/<USER>/moveit2_ws/install/srdfdom/share/srdfdom/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/warehouse_ros/cmake/export_warehouse_rosExport.cmake", "/opt/ros/humble/share/warehouse_ros/cmake/ament_cmake_export_targets-extras.cmake", "/opt/ros/humble/share/warehouse_ros/cmake/warehouse_rosConfig.cmake", "/home/<USER>/moveit2_ws/install/moveit_ros_warehouse/share/moveit_ros_warehouse/cmake/ament_cmake_export_dependencies-extras.cmake"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 21, "parent": 0}, {"command": 1, "file": 0, "line": 41, "parent": 0}, {"command": 3, "file": 0, "line": 24, "parent": 0}, {"command": 2, "file": 1, "line": 145, "parent": 3}, {"command": 6, "file": 0, "line": 16, "parent": 0}, {"file": 4, "parent": 5}, {"command": 5, "file": 4, "line": 41, "parent": 6}, {"file": 3, "parent": 7}, {"command": 5, "file": 3, "line": 9, "parent": 8}, {"file": 2, "parent": 9}, {"command": 4, "file": 2, "line": 64, "parent": 10}, {"command": 5, "file": 4, "line": 41, "parent": 6}, {"file": 8, "parent": 12}, {"command": 6, "file": 8, "line": 21, "parent": 13}, {"file": 7, "parent": 14}, {"command": 5, "file": 7, "line": 41, "parent": 15}, {"file": 6, "parent": 16}, {"command": 5, "file": 6, "line": 9, "parent": 17}, {"file": 5, "parent": 18}, {"command": 4, "file": 5, "line": 56, "parent": 19}, {"command": 4, "file": 2, "line": 72, "parent": 10}, {"command": 4, "file": 2, "line": 56, "parent": 10}, {"command": 6, "file": 8, "line": 21, "parent": 13}, {"file": 11, "parent": 23}, {"command": 5, "file": 11, "line": 41, "parent": 24}, {"file": 10, "parent": 25}, {"command": 5, "file": 10, "line": 9, "parent": 26}, {"file": 9, "parent": 27}, {"command": 4, "file": 9, "line": 73, "parent": 28}, {"command": 5, "file": 11, "line": 41, "parent": 24}, {"file": 17, "parent": 30}, {"command": 6, "file": 17, "line": 21, "parent": 31}, {"file": 16, "parent": 32}, {"command": 5, "file": 16, "line": 41, "parent": 33}, {"file": 15, "parent": 34}, {"command": 6, "file": 15, "line": 33, "parent": 35}, {"file": 14, "parent": 36}, {"command": 5, "file": 14, "line": 41, "parent": 37}, {"file": 13, "parent": 38}, {"command": 5, "file": 13, "line": 9, "parent": 39}, {"file": 12, "parent": 40}, {"command": 4, "file": 12, "line": 56, "parent": 41}, {"command": 4, "file": 9, "line": 56, "parent": 28}, {"command": 4, "file": 9, "line": 114, "parent": 28}, {"command": 4, "file": 9, "line": 81, "parent": 28}, {"command": 6, "file": 8, "line": 21, "parent": 13}, {"file": 20, "parent": 46}, {"command": 5, "file": 20, "line": 41, "parent": 47}, {"file": 19, "parent": 48}, {"command": 5, "file": 19, "line": 9, "parent": 49}, {"file": 18, "parent": 50}, {"command": 4, "file": 18, "line": 56, "parent": 51}, {"command": 4, "file": 9, "line": 234, "parent": 28}, {"command": 4, "file": 9, "line": 97, "parent": 28}, {"command": 6, "file": 17, "line": 21, "parent": 31}, {"file": 22, "parent": 55}, {"command": 5, "file": 22, "line": 52, "parent": 56}, {"file": 21, "parent": 57}, {"command": 4, "file": 21, "line": 66, "parent": 58}, {"command": 7, "file": 22, "line": 50, "parent": 56}, {"command": 6, "file": 25, "line": 78, "parent": 60}, {"file": 24, "parent": 61}, {"command": 5, "file": 24, "line": 77, "parent": 62}, {"file": 23, "parent": 63}, {"command": 4, "file": 23, "line": 69, "parent": 64}, {"command": 6, "file": 17, "line": 21, "parent": 31}, {"file": 28, "parent": 66}, {"command": 5, "file": 28, "line": 41, "parent": 67}, {"file": 27, "parent": 68}, {"command": 5, "file": 27, "line": 9, "parent": 69}, {"file": 26, "parent": 70}, {"command": 4, "file": 26, "line": 56, "parent": 71}, {"command": 5, "file": 28, "line": 41, "parent": 67}, {"file": 32, "parent": 73}, {"command": 6, "file": 32, "line": 21, "parent": 74}, {"file": 31, "parent": 75}, {"command": 5, "file": 31, "line": 41, "parent": 76}, {"file": 30, "parent": 77}, {"command": 5, "file": 30, "line": 9, "parent": 78}, {"file": 29, "parent": 79}, {"command": 4, "file": 29, "line": 56, "parent": 80}, {"command": 5, "file": 31, "line": 41, "parent": 76}, {"file": 36, "parent": 82}, {"command": 6, "file": 36, "line": 21, "parent": 83}, {"file": 35, "parent": 84}, {"command": 5, "file": 35, "line": 41, "parent": 85}, {"file": 34, "parent": 86}, {"command": 6, "file": 34, "line": 7, "parent": 87}, {"file": 33, "parent": 88}, {"command": 8, "file": 33, "line": 252, "parent": 89}, {"command": 4, "file": 9, "line": 89, "parent": 28}, {"command": 6, "file": 17, "line": 21, "parent": 31}, {"file": 42, "parent": 92}, {"command": 5, "file": 42, "line": 41, "parent": 93}, {"file": 41, "parent": 94}, {"command": 6, "file": 41, "line": 21, "parent": 95}, {"file": 40, "parent": 96}, {"command": 5, "file": 40, "line": 41, "parent": 97}, {"file": 39, "parent": 98}, {"command": 6, "file": 39, "line": 21, "parent": 99}, {"file": 38, "parent": 100}, {"command": 5, "file": 38, "line": 42, "parent": 101}, {"file": 37, "parent": 102}, {"command": 4, "file": 37, "line": 77, "parent": 103}, {"command": 6, "file": 8, "line": 21, "parent": 13}, {"file": 45, "parent": 105}, {"command": 5, "file": 45, "line": 41, "parent": 106}, {"file": 44, "parent": 107}, {"command": 5, "file": 44, "line": 9, "parent": 108}, {"file": 43, "parent": 109}, {"command": 4, "file": 43, "line": 56, "parent": 110}, {"command": 4, "file": 18, "line": 96, "parent": 51}, {"command": 5, "file": 44, "line": 9, "parent": 108}, {"file": 46, "parent": 113}, {"command": 4, "file": 46, "line": 56, "parent": 114}, {"command": 5, "file": 44, "line": 9, "parent": 108}, {"file": 47, "parent": 116}, {"command": 4, "file": 47, "line": 56, "parent": 117}, {"command": 5, "file": 44, "line": 9, "parent": 108}, {"file": 48, "parent": 119}, {"command": 4, "file": 48, "line": 56, "parent": 120}, {"command": 5, "file": 44, "line": 9, "parent": 108}, {"file": 49, "parent": 122}, {"command": 4, "file": 49, "line": 56, "parent": 123}, {"command": 5, "file": 44, "line": 9, "parent": 108}, {"file": 50, "parent": 125}, {"command": 4, "file": 50, "line": 56, "parent": 126}, {"command": 5, "file": 44, "line": 9, "parent": 108}, {"file": 51, "parent": 128}, {"command": 4, "file": 51, "line": 56, "parent": 129}, {"command": 5, "file": 44, "line": 9, "parent": 108}, {"file": 52, "parent": 131}, {"command": 4, "file": 52, "line": 56, "parent": 132}, {"command": 4, "file": 18, "line": 72, "parent": 51}, {"command": 4, "file": 9, "line": 130, "parent": 28}, {"command": 5, "file": 4, "line": 41, "parent": 6}, {"file": 56, "parent": 136}, {"command": 6, "file": 56, "line": 9, "parent": 137}, {"file": 55, "parent": 138}, {"command": 6, "file": 55, "line": 609, "parent": 139}, {"file": 54, "parent": 140}, {"command": 9, "file": 54, "line": 258, "parent": 141}, {"command": 6, "file": 54, "line": 141, "parent": 142}, {"file": 53, "parent": 143}, {"command": 8, "file": 53, "line": 101, "parent": 144}, {"command": 6, "file": 8, "line": 21, "parent": 13}, {"file": 59, "parent": 146}, {"command": 5, "file": 59, "line": 41, "parent": 147}, {"file": 58, "parent": 148}, {"command": 5, "file": 58, "line": 9, "parent": 149}, {"file": 57, "parent": 150}, {"command": 4, "file": 57, "line": 56, "parent": 151}, {"command": 6, "file": 0, "line": 14, "parent": 0}, {"file": 64, "parent": 153}, {"command": 5, "file": 64, "line": 41, "parent": 154}, {"file": 63, "parent": 155}, {"command": 6, "file": 63, "line": 21, "parent": 156}, {"file": 62, "parent": 157}, {"command": 5, "file": 62, "line": 41, "parent": 158}, {"file": 61, "parent": 159}, {"command": 5, "file": 61, "line": 9, "parent": 160}, {"file": 60, "parent": 161}, {"command": 4, "file": 60, "line": 56, "parent": 162}, {"command": 6, "file": 0, "line": 12, "parent": 0}, {"file": 67, "parent": 164}, {"command": 5, "file": 67, "line": 41, "parent": 165}, {"file": 66, "parent": 166}, {"command": 5, "file": 66, "line": 9, "parent": 167}, {"file": 65, "parent": 168}, {"command": 4, "file": 65, "line": 56, "parent": 169}, {"command": 5, "file": 62, "line": 41, "parent": 158}, {"file": 71, "parent": 171}, {"command": 6, "file": 71, "line": 21, "parent": 172}, {"file": 70, "parent": 173}, {"command": 5, "file": 70, "line": 41, "parent": 174}, {"file": 69, "parent": 175}, {"command": 5, "file": 69, "line": 9, "parent": 176}, {"file": 68, "parent": 177}, {"command": 4, "file": 68, "line": 56, "parent": 178}, {"command": 5, "file": 67, "line": 41, "parent": 165}, {"file": 79, "parent": 180}, {"command": 6, "file": 79, "line": 21, "parent": 181}, {"file": 78, "parent": 182}, {"command": 5, "file": 78, "line": 41, "parent": 183}, {"file": 77, "parent": 184}, {"command": 6, "file": 77, "line": 21, "parent": 185}, {"file": 76, "parent": 186}, {"command": 5, "file": 76, "line": 41, "parent": 187}, {"file": 75, "parent": 188}, {"command": 6, "file": 75, "line": 21, "parent": 189}, {"file": 74, "parent": 190}, {"command": 5, "file": 74, "line": 41, "parent": 191}, {"file": 73, "parent": 192}, {"command": 5, "file": 73, "line": 9, "parent": 193}, {"file": 72, "parent": 194}, {"command": 4, "file": 72, "line": 56, "parent": 195}, {"command": 5, "file": 76, "line": 41, "parent": 187}, {"file": 81, "parent": 197}, {"command": 5, "file": 81, "line": 9, "parent": 198}, {"file": 80, "parent": 199}, {"command": 4, "file": 80, "line": 56, "parent": 200}, {"command": 6, "file": 75, "line": 21, "parent": 189}, {"file": 86, "parent": 202}, {"command": 5, "file": 86, "line": 41, "parent": 203}, {"file": 85, "parent": 204}, {"command": 6, "file": 85, "line": 21, "parent": 205}, {"file": 84, "parent": 206}, {"command": 7, "file": 84, "line": 47, "parent": 207}, {"command": 6, "file": 25, "line": 78, "parent": 208}, {"file": 83, "parent": 209}, {"command": 5, "file": 83, "line": 37, "parent": 210}, {"file": 82, "parent": 211}, {"command": 4, "file": 82, "line": 66, "parent": 212}, {"command": 6, "file": 71, "line": 21, "parent": 172}, {"file": 89, "parent": 214}, {"command": 5, "file": 89, "line": 41, "parent": 215}, {"file": 88, "parent": 216}, {"command": 5, "file": 88, "line": 9, "parent": 217}, {"file": 87, "parent": 218}, {"command": 4, "file": 87, "line": 56, "parent": 219}, {"command": 5, "file": 88, "line": 9, "parent": 217}, {"file": 90, "parent": 221}, {"command": 4, "file": 90, "line": 56, "parent": 222}, {"command": 5, "file": 88, "line": 9, "parent": 217}, {"file": 91, "parent": 224}, {"command": 4, "file": 91, "line": 56, "parent": 225}, {"command": 5, "file": 88, "line": 9, "parent": 217}, {"file": 92, "parent": 227}, {"command": 4, "file": 92, "line": 56, "parent": 228}, {"command": 5, "file": 88, "line": 9, "parent": 217}, {"file": 93, "parent": 230}, {"command": 4, "file": 93, "line": 56, "parent": 231}, {"command": 5, "file": 88, "line": 9, "parent": 217}, {"file": 94, "parent": 233}, {"command": 4, "file": 94, "line": 56, "parent": 234}, {"command": 5, "file": 88, "line": 9, "parent": 217}, {"file": 95, "parent": 236}, {"command": 4, "file": 95, "line": 56, "parent": 237}, {"command": 5, "file": 88, "line": 9, "parent": 217}, {"file": 96, "parent": 239}, {"command": 4, "file": 96, "line": 56, "parent": 240}, {"command": 5, "file": 64, "line": 41, "parent": 154}, {"file": 98, "parent": 242}, {"command": 5, "file": 98, "line": 9, "parent": 243}, {"file": 97, "parent": 244}, {"command": 4, "file": 97, "line": 56, "parent": 245}, {"command": 6, "file": 0, "line": 13, "parent": 0}, {"file": 103, "parent": 247}, {"command": 5, "file": 103, "line": 41, "parent": 248}, {"file": 102, "parent": 249}, {"command": 6, "file": 102, "line": 21, "parent": 250}, {"file": 101, "parent": 251}, {"command": 5, "file": 101, "line": 41, "parent": 252}, {"file": 100, "parent": 253}, {"command": 5, "file": 100, "line": 9, "parent": 254}, {"file": 99, "parent": 255}, {"command": 4, "file": 99, "line": 56, "parent": 256}, {"command": 5, "file": 100, "line": 9, "parent": 254}, {"file": 104, "parent": 258}, {"command": 4, "file": 104, "line": 56, "parent": 259}, {"command": 5, "file": 100, "line": 9, "parent": 254}, {"file": 105, "parent": 261}, {"command": 4, "file": 105, "line": 56, "parent": 262}, {"command": 5, "file": 100, "line": 9, "parent": 254}, {"file": 106, "parent": 264}, {"command": 4, "file": 106, "line": 56, "parent": 265}, {"command": 5, "file": 100, "line": 9, "parent": 254}, {"file": 107, "parent": 267}, {"command": 4, "file": 107, "line": 56, "parent": 268}, {"command": 5, "file": 100, "line": 9, "parent": 254}, {"file": 108, "parent": 270}, {"command": 4, "file": 108, "line": 56, "parent": 271}, {"command": 5, "file": 100, "line": 9, "parent": 254}, {"file": 109, "parent": 273}, {"command": 4, "file": 109, "line": 56, "parent": 274}, {"command": 5, "file": 100, "line": 9, "parent": 254}, {"file": 110, "parent": 276}, {"command": 4, "file": 110, "line": 56, "parent": 277}, {"command": 6, "file": 77, "line": 21, "parent": 185}, {"file": 115, "parent": 279}, {"command": 5, "file": 115, "line": 41, "parent": 280}, {"file": 114, "parent": 281}, {"command": 6, "file": 114, "line": 21, "parent": 282}, {"file": 113, "parent": 283}, {"command": 5, "file": 113, "line": 41, "parent": 284}, {"file": 112, "parent": 285}, {"command": 5, "file": 112, "line": 9, "parent": 286}, {"file": 111, "parent": 287}, {"command": 4, "file": 111, "line": 56, "parent": 288}, {"command": 4, "file": 18, "line": 64, "parent": 51}, {"command": 6, "file": 114, "line": 21, "parent": 282}, {"file": 120, "parent": 291}, {"command": 5, "file": 120, "line": 41, "parent": 292}, {"file": 119, "parent": 293}, {"command": 6, "file": 119, "line": 21, "parent": 294}, {"file": 118, "parent": 295}, {"command": 5, "file": 118, "line": 41, "parent": 296}, {"file": 117, "parent": 297}, {"command": 5, "file": 117, "line": 9, "parent": 298}, {"file": 116, "parent": 299}, {"command": 4, "file": 116, "line": 56, "parent": 300}, {"command": 5, "file": 42, "line": 41, "parent": 93}, {"file": 122, "parent": 302}, {"command": 5, "file": 122, "line": 9, "parent": 303}, {"file": 121, "parent": 304}, {"command": 4, "file": 121, "line": 56, "parent": 305}, {"command": 5, "file": 59, "line": 41, "parent": 147}, {"file": 126, "parent": 307}, {"command": 6, "file": 126, "line": 21, "parent": 308}, {"file": 125, "parent": 309}, {"command": 5, "file": 125, "line": 41, "parent": 310}, {"file": 124, "parent": 311}, {"command": 5, "file": 124, "line": 9, "parent": 312}, {"file": 123, "parent": 313}, {"command": 4, "file": 123, "line": 56, "parent": 314}, {"command": 10, "file": 0, "line": 5, "parent": 0}, {"command": 11, "file": 0, "line": 8, "parent": 0}, {"command": 12, "file": 1, "line": 141, "parent": 3}]}, "compileGroups": [{"compileCommandFragments": [{"backtrace": 316, "fragment": "-Wall"}, {"backtrace": 316, "fragment": "-Wextra"}, {"backtrace": 316, "fragment": "-Wpedantic"}], "defines": [{"backtrace": 4, "define": "BOOST_ALL_NO_LIB"}, {"backtrace": 4, "define": "BOOST_ATOMIC_DYN_LINK"}, {"backtrace": 4, "define": "BOOST_CHRONO_DYN_LINK"}, {"backtrace": 4, "define": "BOOST_DATE_TIME_DYN_LINK"}, {"backtrace": 4, "define": "BOOST_FILESYSTEM_DYN_LINK"}, {"backtrace": 4, "define": "BOOST_IOSTREAMS_DYN_LINK"}, {"backtrace": 4, "define": "BOOST_PROGRAM_OPTIONS_DYN_LINK"}, {"backtrace": 4, "define": "BOOST_REGEX_DYN_LINK"}, {"backtrace": 4, "define": "BOOST_SERIALIZATION_DYN_LINK"}, {"backtrace": 4, "define": "BOOST_SYSTEM_DYN_LINK"}, {"backtrace": 4, "define": "BOOST_THREAD_DYN_LINK"}, {"backtrace": 4, "define": "DEFAULT_RMW_IMPLEMENTATION=rmw_fastrtps_cpp"}, {"backtrace": 4, "define": "FMT_LOCALE"}, {"backtrace": 4, "define": "FMT_SHARED"}, {"backtrace": 4, "define": "RCUTILS_ENABLE_FAULT_INJECTION"}], "includes": [{"backtrace": 317, "path": "/home/<USER>/Code/ws_0/src/arm_controller_pkg/include"}, {"backtrace": 318, "isSystem": true, "path": "/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/include"}, {"backtrace": 318, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp"}, {"backtrace": 318, "isSystem": true, "path": "/opt/ros/humble/include/tf2"}, {"backtrace": 318, "isSystem": true, "path": "/opt/ros/humble/include/tf2_ros"}, {"backtrace": 318, "isSystem": true, "path": "/opt/ros/humble/include/tf2_geometry_msgs"}, {"backtrace": 318, "isSystem": true, "path": "/opt/ros/humble/include/geometry_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/ament_index_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/libstatistics_collector"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/builtin_interfaces"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcutils"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_interface"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/fastcdr"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rmw"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcl"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcl_interfaces"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcl_logging_interface"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcl_yaml_param_parser"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/libyaml_vendor"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/tracetools"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcpputils"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/statistics_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosgraph_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_cpp"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_c"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/std_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/message_filters"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp_action"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/action_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/unique_identifier_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcl_action"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/tf2_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/usr/include/eigen3"}, {"backtrace": 4, "isSystem": true, "path": "/home/<USER>/moveit2_ws/install/moveit_ros_planning/include"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/urdf"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/urdf_parser_plugin"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/urdfdom_headers"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/urdfdom"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/pluginlib"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/class_loader"}, {"backtrace": 4, "isSystem": true, "path": "/home/<USER>/moveit2_ws/install/srdfdom/include/srdfdom"}, {"backtrace": 4, "isSystem": true, "path": "/usr/include/bullet"}, {"backtrace": 4, "isSystem": true, "path": "/home/<USER>/moveit2_ws/install/moveit_core/include"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/visualization_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/sensor_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/octomap_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/eigen_stl_containers"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/tf2_eigen"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/geometric_shapes"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/resource_retriever"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/shape_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/angles"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/moveit_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/object_recognition_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/trajectory_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/parameter_traits"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rsl"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rclcpp_lifecycle"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/rcl_lifecycle"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/lifecycle_msgs"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/kdl_parser"}, {"backtrace": 4, "isSystem": true, "path": "/home/<USER>/moveit2_ws/install/moveit_ros_occupancy_map_monitor/include"}, {"backtrace": 4, "isSystem": true, "path": "/home/<USER>/moveit2_ws/install/moveit_ros_move_group/include"}, {"backtrace": 4, "isSystem": true, "path": "/opt/ros/humble/include/std_srvs"}, {"backtrace": 4, "isSystem": true, "path": "/home/<USER>/moveit2_ws/install/moveit_ros_warehouse/include"}], "language": "CXX", "sourceIndexes": [0]}], "id": "path_planning_node::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "lib/arm_controller_pkg"}], "prefix": {"path": "/home/<USER>/Code/ws_0/install/arm_controller_pkg"}}, "link": {"commandFragments": [{"fragment": "-Wl,-r<PERSON>,/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/lib:/home/<USER>/moveit2_ws/install/moveit_ros_move_group/lib:/opt/ros/humble/lib:/home/<USER>/moveit2_ws/install/moveit_ros_warehouse/lib:/home/<USER>/moveit2_ws/install/moveit_ros_planning/lib:/home/<USER>/moveit2_ws/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/moveit2_ws/install/moveit_core/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/home/<USER>/moveit2_ws/install/srdfdom/lib:", "role": "libraries"}, {"backtrace": 4, "fragment": "/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/lib/libmoveit_move_group_interface.so.2.5.9", "role": "libraries"}, {"backtrace": 4, "fragment": "/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/lib/libmoveit_common_planning_interface_objects.so.2.5.9", "role": "libraries"}, {"backtrace": 4, "fragment": "/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/lib/libmoveit_planning_scene_interface.so.2.5.9", "role": "libraries"}, {"backtrace": 11, "fragment": "/home/<USER>/moveit2_ws/install/moveit_ros_move_group/lib/libmoveit_move_group_default_capabilities.so.2.5.9", "role": "libraries"}, {"backtrace": 11, "fragment": "/home/<USER>/moveit2_ws/install/moveit_ros_move_group/lib/libmoveit_move_group_capabilities_base.so.2.5.9", "role": "libraries"}, {"backtrace": 20, "fragment": "/opt/ros/humble/lib/libstd_srvs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 20, "fragment": "/opt/ros/humble/lib/libstd_srvs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 20, "fragment": "/opt/ros/humble/lib/libstd_srvs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 20, "fragment": "/opt/ros/humble/lib/libstd_srvs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 20, "fragment": "/opt/ros/humble/lib/libstd_srvs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 20, "fragment": "/opt/ros/humble/lib/libstd_srvs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 20, "fragment": "/opt/ros/humble/lib/libstd_srvs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 20, "fragment": "/opt/ros/humble/lib/libstd_srvs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 21, "fragment": "/home/<USER>/moveit2_ws/install/moveit_ros_warehouse/lib/libmoveit_warehouse.so.2.5.9", "role": "libraries"}, {"backtrace": 22, "fragment": "/home/<USER>/moveit2_ws/install/moveit_ros_planning/lib/libmoveit_constraint_sampler_manager_loader.so.2.5.9", "role": "libraries"}, {"backtrace": 22, "fragment": "/home/<USER>/moveit2_ws/install/moveit_ros_planning/lib/libmoveit_plan_execution.so.2.5.9", "role": "libraries"}, {"backtrace": 22, "fragment": "/home/<USER>/moveit2_ws/install/moveit_ros_planning/lib/libmoveit_default_planning_request_adapter_plugins.so.2.5.9", "role": "libraries"}, {"backtrace": 22, "fragment": "/home/<USER>/moveit2_ws/install/moveit_ros_planning/lib/libmoveit_cpp.so.2.5.9", "role": "libraries"}, {"backtrace": 22, "fragment": "/home/<USER>/moveit2_ws/install/moveit_ros_planning/lib/libmoveit_planning_pipeline.so.2.5.9", "role": "libraries"}, {"backtrace": 22, "fragment": "/home/<USER>/moveit2_ws/install/moveit_ros_planning/lib/libmoveit_trajectory_execution_manager.so.2.5.9", "role": "libraries"}, {"backtrace": 22, "fragment": "/home/<USER>/moveit2_ws/install/moveit_ros_planning/lib/libmoveit_planning_scene_monitor.so.2.5.9", "role": "libraries"}, {"backtrace": 22, "fragment": "/home/<USER>/moveit2_ws/install/moveit_ros_planning/lib/libmoveit_robot_model_loader.so.2.5.9", "role": "libraries"}, {"backtrace": 22, "fragment": "/home/<USER>/moveit2_ws/install/moveit_ros_planning/lib/libmoveit_kinematics_plugin_loader.so.2.5.9", "role": "libraries"}, {"backtrace": 22, "fragment": "/home/<USER>/moveit2_ws/install/moveit_ros_planning/lib/libmoveit_rdf_loader.so.2.5.9", "role": "libraries"}, {"backtrace": 22, "fragment": "/home/<USER>/moveit2_ws/install/moveit_ros_planning/lib/libmoveit_collision_plugin_loader.so.2.5.9", "role": "libraries"}, {"backtrace": 21, "fragment": "/home/<USER>/moveit2_ws/install/moveit_ros_occupancy_map_monitor/lib/libmoveit_ros_occupancy_map_monitor.so.2.5.9", "role": "libraries"}, {"backtrace": 11, "fragment": "/home/<USER>/moveit2_ws/install/moveit_core/lib/libcollision_detector_bullet_plugin.so.2.5.9", "role": "libraries"}, {"backtrace": 11, "fragment": "/home/<USER>/moveit2_ws/install/moveit_core/lib/libmoveit_butterworth_filter.so.2.5.9", "role": "libraries"}, {"backtrace": 29, "fragment": "/opt/ros/humble/lib/librclcpp_lifecycle.so", "role": "libraries"}, {"backtrace": 42, "fragment": "/opt/ros/humble/lib/librcl_lifecycle.so", "role": "libraries"}, {"backtrace": 42, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 42, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 42, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 42, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 42, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 42, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 42, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 42, "fragment": "/opt/ros/humble/lib/liblifecycle_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 29, "fragment": "/opt/ros/humble/lib/librsl.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/home/<USER>/moveit2_ws/install/moveit_core/lib/libmoveit_collision_distance_field.so.2.5.9", "role": "libraries"}, {"backtrace": 11, "fragment": "/home/<USER>/moveit2_ws/install/moveit_core/lib/libmoveit_collision_detection_bullet.so.2.5.9", "role": "libraries"}, {"backtrace": 43, "fragment": "/usr/lib/aarch64-linux-gnu/libBulletDynamics.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/usr/lib/aarch64-linux-gnu/libBulletCollision.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/usr/lib/aarch64-linux-gnu/libLinearMath.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/usr/lib/aarch64-linux-gnu/libBulletSoftBody.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/home/<USER>/moveit2_ws/install/moveit_core/lib/libmoveit_dynamics_solver.so.2.5.9", "role": "libraries"}, {"backtrace": 44, "fragment": "/opt/ros/humble/lib/libkdl_parser.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/home/<USER>/moveit2_ws/install/moveit_core/lib/libmoveit_constraint_samplers.so.2.5.9", "role": "libraries"}, {"backtrace": 11, "fragment": "/home/<USER>/moveit2_ws/install/moveit_core/lib/libmoveit_distance_field.so.2.5.9", "role": "libraries"}, {"backtrace": 11, "fragment": "/home/<USER>/moveit2_ws/install/moveit_core/lib/libmoveit_kinematics_metrics.so.2.5.9", "role": "libraries"}, {"backtrace": 11, "fragment": "/home/<USER>/moveit2_ws/install/moveit_core/lib/libmoveit_planning_interface.so.2.5.9", "role": "libraries"}, {"backtrace": 11, "fragment": "/home/<USER>/moveit2_ws/install/moveit_core/lib/libmoveit_planning_request_adapter.so.2.5.9", "role": "libraries"}, {"backtrace": 11, "fragment": "/home/<USER>/moveit2_ws/install/moveit_core/lib/libmoveit_planning_scene.so.2.5.9", "role": "libraries"}, {"backtrace": 11, "fragment": "/home/<USER>/moveit2_ws/install/moveit_core/lib/libmoveit_kinematic_constraints.so.2.5.9", "role": "libraries"}, {"backtrace": 11, "fragment": "/home/<USER>/moveit2_ws/install/moveit_core/lib/libmoveit_collision_detection_fcl.so.2.5.9", "role": "libraries"}, {"backtrace": 11, "fragment": "/home/<USER>/moveit2_ws/install/moveit_core/lib/libmoveit_collision_detection.so.2.5.9", "role": "libraries"}, {"backtrace": 45, "fragment": "/opt/ros/humble/lib/aarch64-linux-gnu/liboctomap.so", "role": "libraries"}, {"backtrace": 45, "fragment": "/opt/ros/humble/lib/aarch64-linux-gnu/liboctomath.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/home/<USER>/moveit2_ws/install/moveit_core/lib/libmoveit_smoothing_base.so.2.5.9", "role": "libraries"}, {"backtrace": 11, "fragment": "/home/<USER>/moveit2_ws/install/moveit_core/lib/libmoveit_test_utils.so.2.5.9", "role": "libraries"}, {"backtrace": 11, "fragment": "/home/<USER>/moveit2_ws/install/moveit_core/lib/libmoveit_trajectory_processing.so.2.5.9", "role": "libraries"}, {"backtrace": 11, "fragment": "/home/<USER>/moveit2_ws/install/moveit_core/lib/libmoveit_robot_trajectory.so.2.5.9", "role": "libraries"}, {"backtrace": 11, "fragment": "/home/<USER>/moveit2_ws/install/moveit_core/lib/libmoveit_robot_state.so.2.5.9", "role": "libraries"}, {"backtrace": 11, "fragment": "/home/<USER>/moveit2_ws/install/moveit_core/lib/libmoveit_robot_model.so.2.5.9", "role": "libraries"}, {"backtrace": 11, "fragment": "/home/<USER>/moveit2_ws/install/moveit_core/lib/libmoveit_exceptions.so.2.5.9", "role": "libraries"}, {"backtrace": 11, "fragment": "/home/<USER>/moveit2_ws/install/moveit_core/lib/libmoveit_kinematics_base.so", "role": "libraries"}, {"backtrace": 52, "fragment": "/home/<USER>/moveit2_ws/install/srdfdom/lib/libsrdfdom.so.2.0.8", "role": "libraries"}, {"backtrace": 52, "fragment": "/opt/ros/humble/lib/liburdf.so", "role": "libraries"}, {"backtrace": 53, "fragment": "/opt/ros/humble/lib/aarch64-linux-gnu/libruckig.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/home/<USER>/moveit2_ws/install/moveit_core/lib/libmoveit_transforms.so.2.5.9", "role": "libraries"}, {"backtrace": 45, "fragment": "/opt/ros/humble/lib/libgeometric_shapes.so.2.3.2", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 43, "fragment": "/opt/ros/humble/lib/libvisualization_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 54, "fragment": "/usr/lib/aarch64-linux-gnu/libfcl.so.0.7.0", "role": "libraries"}, {"backtrace": 59, "fragment": "/usr/lib/aarch64-linux-gnu/libccd.so", "role": "libraries"}, {"backtrace": 59, "fragment": "/usr/lib/aarch64-linux-gnu/libm.so", "role": "libraries"}, {"backtrace": 59, "fragment": "/opt/ros/humble/lib/aarch64-linux-gnu/liboctomap.so.1.9.8", "role": "libraries"}, {"backtrace": 65, "fragment": "/opt/ros/humble/lib/aarch64-linux-gnu/liboctomath.so.1.9.8", "role": "libraries"}, {"backtrace": 72, "fragment": "/opt/ros/humble/lib/libresource_retriever.so", "role": "libraries"}, {"backtrace": 81, "fragment": "/usr/lib/aarch64-linux-gnu/libcurl.so", "role": "libraries"}, {"backtrace": 90, "fragment": "/usr/lib/aarch64-linux-gnu/libcurl.so", "role": "libraries"}, {"backtrace": 72, "fragment": "/opt/ros/humble/lib/librandom_numbers.so", "role": "libraries"}, {"backtrace": 91, "fragment": "/opt/ros/humble/lib/aarch64-linux-gnu/liburdfdom_sensor.so.3.0", "role": "libraries"}, {"backtrace": 91, "fragment": "/opt/ros/humble/lib/aarch64-linux-gnu/liburdfdom_model_state.so.3.0", "role": "libraries"}, {"backtrace": 91, "fragment": "/opt/ros/humble/lib/aarch64-linux-gnu/liburdfdom_model.so.3.0", "role": "libraries"}, {"backtrace": 91, "fragment": "/opt/ros/humble/lib/aarch64-linux-gnu/liburdfdom_world.so.3.0", "role": "libraries"}, {"backtrace": 104, "fragment": "/usr/lib/aarch64-linux-gnu/libtinyxml.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/home/<USER>/moveit2_ws/install/moveit_core/lib/libmoveit_utils.so.2.5.9", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libmoveit_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 111, "fragment": "/opt/ros/humble/lib/libobject_recognition_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 112, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 111, "fragment": "/opt/ros/humble/lib/libshape_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 111, "fragment": "/opt/ros/humble/lib/liboctomap_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 111, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libmoveit_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 115, "fragment": "/opt/ros/humble/lib/libobject_recognition_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 112, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 115, "fragment": "/opt/ros/humble/lib/libshape_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 115, "fragment": "/opt/ros/humble/lib/liboctomap_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 115, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libmoveit_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 118, "fragment": "/opt/ros/humble/lib/libobject_recognition_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 112, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 118, "fragment": "/opt/ros/humble/lib/libshape_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 118, "fragment": "/opt/ros/humble/lib/liboctomap_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 118, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libmoveit_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 121, "fragment": "/opt/ros/humble/lib/libobject_recognition_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 112, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 121, "fragment": "/opt/ros/humble/lib/libshape_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 121, "fragment": "/opt/ros/humble/lib/liboctomap_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 121, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libmoveit_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 124, "fragment": "/opt/ros/humble/lib/libobject_recognition_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 112, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 124, "fragment": "/opt/ros/humble/lib/libshape_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 124, "fragment": "/opt/ros/humble/lib/liboctomap_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 124, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libmoveit_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libmoveit_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libmoveit_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 127, "fragment": "/opt/ros/humble/lib/libobject_recognition_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 112, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 130, "fragment": "/opt/ros/humble/lib/libobject_recognition_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 112, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 133, "fragment": "/opt/ros/humble/lib/libobject_recognition_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 112, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 127, "fragment": "/opt/ros/humble/lib/libshape_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 130, "fragment": "/opt/ros/humble/lib/libshape_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 133, "fragment": "/opt/ros/humble/lib/libshape_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 127, "fragment": "/opt/ros/humble/lib/liboctomap_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 130, "fragment": "/opt/ros/humble/lib/liboctomap_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 133, "fragment": "/opt/ros/humble/lib/liboctomap_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 127, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 130, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 133, "fragment": "/opt/ros/humble/lib/libtrajectory_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 134, "fragment": "/usr/lib/aarch64-linux-gnu/libboost_chrono.so.1.74.0", "role": "libraries"}, {"backtrace": 135, "fragment": "/usr/lib/aarch64-linux-gnu/libboost_iostreams.so.1.74.0", "role": "libraries"}, {"backtrace": 135, "fragment": "/usr/lib/aarch64-linux-gnu/libboost_serialization.so.1.74.0", "role": "libraries"}, {"backtrace": 21, "fragment": "/usr/lib/aarch64-linux-gnu/libboost_thread.so.1.74.0", "role": "libraries"}, {"backtrace": 145, "fragment": "/usr/lib/aarch64-linux-gnu/libboost_atomic.so.1.74.0", "role": "libraries"}, {"backtrace": 134, "fragment": "/usr/lib/aarch64-linux-gnu/libboost_system.so.1.74.0", "role": "libraries"}, {"backtrace": 134, "fragment": "/usr/lib/aarch64-linux-gnu/libboost_filesystem.so.1.74.0", "role": "libraries"}, {"backtrace": 134, "fragment": "/usr/lib/aarch64-linux-gnu/libboost_date_time.so.1.74.0", "role": "libraries"}, {"backtrace": 134, "fragment": "/usr/lib/aarch64-linux-gnu/libboost_program_options.so.1.74.0", "role": "libraries"}, {"backtrace": 135, "fragment": "/usr/lib/aarch64-linux-gnu/libboost_regex.so.1.74.0", "role": "libraries"}, {"backtrace": 152, "fragment": "/opt/ros/humble/lib/libwarehouse_ros.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libstatic_transform_broadcaster_node.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libtf2_ros.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libtf2.so", "role": "libraries"}, {"backtrace": 163, "fragment": "/opt/ros/humble/lib/libmessage_filters.so", "role": "libraries"}, {"backtrace": 163, "fragment": "/opt/ros/humble/lib/librclcpp_action.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/librclcpp.so", "role": "libraries"}, {"backtrace": 170, "fragment": "/opt/ros/humble/lib/liblibstatistics_collector.so", "role": "libraries"}, {"backtrace": 170, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 170, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 170, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 170, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 170, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 170, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 170, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 170, "fragment": "/opt/ros/humble/lib/librosgraph_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 170, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 170, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 170, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 170, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 170, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 170, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 170, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 170, "fragment": "/opt/ros/humble/lib/libstatistics_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 179, "fragment": "/opt/ros/humble/lib/librcl_action.so", "role": "libraries"}, {"backtrace": 170, "fragment": "/opt/ros/humble/lib/librcl.so", "role": "libraries"}, {"backtrace": 170, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 170, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 170, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 170, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 170, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 170, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 170, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 170, "fragment": "/opt/ros/humble/lib/librcl_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 170, "fragment": "/opt/ros/humble/lib/librcl_yaml_param_parser.so", "role": "libraries"}, {"backtrace": 196, "fragment": "/opt/ros/humble/lib/libyaml.so", "role": "libraries"}, {"backtrace": 170, "fragment": "/opt/ros/humble/lib/libtracetools.so", "role": "libraries"}, {"backtrace": 201, "fragment": "/opt/ros/humble/lib/librmw_implementation.so", "role": "libraries"}, {"backtrace": 201, "fragment": "/opt/ros/humble/lib/librcl_logging_spdlog.so", "role": "libraries"}, {"backtrace": 201, "fragment": "/opt/ros/humble/lib/librcl_logging_interface.so", "role": "libraries"}, {"backtrace": 29, "fragment": "/usr/lib/aarch64-linux-gnu/libfmt.so.8.1.1", "role": "libraries"}, {"backtrace": 213, "fragment": "-Wl,--as-needed", "role": "libraries"}, {"backtrace": 163, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 179, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 220, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 163, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 179, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 223, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 163, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 179, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 226, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 163, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 179, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 229, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 163, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 179, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 232, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 163, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 163, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 163, "fragment": "/opt/ros/humble/lib/libtf2_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 179, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 179, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 179, "fragment": "/opt/ros/humble/lib/libaction_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 235, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 238, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 241, "fragment": "/opt/ros/humble/lib/libunique_identifier_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 246, "fragment": "/usr/lib/aarch64-linux-gnu/liborocos-kdl.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 4, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 257, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 170, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 257, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 260, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 170, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 263, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 170, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 257, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 257, "fragment": "/opt/ros/humble/lib/libfastcdr.so.1.0.24", "role": "libraries"}, {"backtrace": 263, "fragment": "/opt/ros/humble/lib/librmw.so", "role": "libraries"}, {"backtrace": 266, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 170, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 266, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 260, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 269, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 170, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 170, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 272, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 170, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 275, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 170, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 278, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 170, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 289, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 278, "fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"backtrace": 272, "fragment": "/usr/lib/aarch64-linux-gnu/libpython3.10.so", "role": "libraries"}, {"backtrace": 170, "fragment": "/opt/ros/humble/lib/libament_index_cpp.so", "role": "libraries"}, {"backtrace": 290, "fragment": "/opt/ros/humble/lib/libclass_loader.so", "role": "libraries"}, {"backtrace": 170, "fragment": "/opt/ros/humble/lib/librcpputils.so", "role": "libraries"}, {"backtrace": 170, "fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"backtrace": 301, "fragment": "-ldl", "role": "libraries"}, {"backtrace": 306, "fragment": "/usr/lib/aarch64-linux-gnu/libconsole_bridge.so.1.0", "role": "libraries"}, {"backtrace": 306, "fragment": "/usr/lib/aarch64-linux-gnu/libtinyxml2.so", "role": "libraries"}, {"backtrace": 315, "fragment": "/usr/lib/aarch64-linux-gnu/libcrypto.so", "role": "libraries"}], "language": "CXX"}, "name": "path_planning_node", "nameOnDisk": "path_planning_node", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "src/path_planning_node.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}