{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-5847c69710277ab913c4.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2, 3]}], "name": "", "projects": [{"directoryIndexes": [0], "name": "arm_controller_pkg", "targetIndexes": [0, 1, 2, 3]}], "targets": [{"directoryIndex": 0, "id": "RB_builder_node::@6890427a1f51a3e7e1df", "jsonFile": "target-RB_builder_node-2eb38363d0bfbe997de6.json", "name": "RB_builder_node", "projectIndex": 0}, {"directoryIndex": 0, "id": "arm_controller_pkg_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-arm_controller_pkg_uninstall-54fff994fcb4f88d391d.json", "name": "arm_controller_pkg_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "path_planning_node::@6890427a1f51a3e7e1df", "jsonFile": "target-path_planning_node-6f30b6c4d200ed83b5e2.json", "name": "path_planning_node", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-6357a840433a73ac32c5.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/Code/ws_0/build/arm_controller_pkg", "source": "/home/<USER>/Code/ws_0/src/arm_controller_pkg"}, "version": {"major": 2, "minor": 8}}