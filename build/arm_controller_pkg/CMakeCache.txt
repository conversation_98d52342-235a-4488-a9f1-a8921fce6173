# This is the CMakeCache file.
# For build in directory: /home/<USER>/Code/ws_0/build/arm_controller_pkg
# It was generated by CMake: /usr/local/cmake/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Generate environment files in the CMAKE_INSTALL_PREFIX
AMENT_CMAKE_ENVIRONMENT_GENERATION:BOOL=OFF

//Generate environment files in the package share folder
AMENT_CMAKE_ENVIRONMENT_PACKAGE_GENERATION:BOOL=ON

//Generate marker file containing the parent prefix path
AMENT_CMAKE_ENVIRONMENT_PARENT_PREFIX_PATH_GENERATION:BOOL=ON

//Replace the CMake install command with a custom implementation
// using symlinks instead of copying resources
AMENT_CMAKE_SYMLINK_INSTALL:BOOL=OFF

//Generate an uninstall target to revert the effects of the install
// step
AMENT_CMAKE_UNINSTALL_TARGET:BOOL=ON

//The path where test results are generated
AMENT_TEST_RESULTS_DIR:PATH=/home/<USER>/Code/ws_0/build/arm_controller_pkg/test_results

//Build the testing tree.
BUILD_TESTING:BOOL=ON

//Path to a library.
BULLET_COLLISION_LIBRARY:FILEPATH=/usr/lib/aarch64-linux-gnu/libBulletCollision.so

//Path to a library.
BULLET_COLLISION_LIBRARY_DEBUG:FILEPATH=BULLET_COLLISION_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
BULLET_DYNAMICS_LIBRARY:FILEPATH=/usr/lib/aarch64-linux-gnu/libBulletDynamics.so

//Path to a library.
BULLET_DYNAMICS_LIBRARY_DEBUG:FILEPATH=BULLET_DYNAMICS_LIBRARY_DEBUG-NOTFOUND

//Path to a file.
BULLET_INCLUDE_DIR:PATH=/usr/include/bullet

//Path to a library.
BULLET_MATH_LIBRARY:FILEPATH=/usr/lib/aarch64-linux-gnu/libLinearMath.so

//Path to a library.
BULLET_MATH_LIBRARY_DEBUG:FILEPATH=BULLET_MATH_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
BULLET_SOFTBODY_LIBRARY:FILEPATH=/usr/lib/aarch64-linux-gnu/libBulletSoftBody.so

//Path to a library.
BULLET_SOFTBODY_LIBRARY_DEBUG:FILEPATH=BULLET_SOFTBODY_LIBRARY_DEBUG-NOTFOUND

Boost_CHRONO_LIBRARY_RELEASE:STRING=/usr/lib/aarch64-linux-gnu/libboost_chrono.so.1.74.0

Boost_DATE_TIME_LIBRARY_RELEASE:STRING=/usr/lib/aarch64-linux-gnu/libboost_date_time.so.1.74.0

//The directory containing a CMake configuration file for Boost.
Boost_DIR:PATH=/usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0

Boost_FILESYSTEM_LIBRARY_RELEASE:STRING=/usr/lib/aarch64-linux-gnu/libboost_filesystem.so.1.74.0

//Path to a file.
Boost_INCLUDE_DIR:PATH=/usr/include

Boost_IOSTREAMS_LIBRARY_RELEASE:STRING=/usr/lib/aarch64-linux-gnu/libboost_iostreams.so.1.74.0

Boost_PROGRAM_OPTIONS_LIBRARY_RELEASE:STRING=/usr/lib/aarch64-linux-gnu/libboost_program_options.so.1.74.0

Boost_REGEX_LIBRARY_RELEASE:STRING=/usr/lib/aarch64-linux-gnu/libboost_regex.so.1.74.0

Boost_SERIALIZATION_LIBRARY_RELEASE:STRING=/usr/lib/aarch64-linux-gnu/libboost_serialization.so.1.74.0

Boost_SYSTEM_LIBRARY_RELEASE:STRING=/usr/lib/aarch64-linux-gnu/libboost_system.so.1.74.0

Boost_THREAD_LIBRARY_RELEASE:STRING=/usr/lib/aarch64-linux-gnu/libboost_thread.so.1.74.0

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=/usr/bin/addr2line

//Path to a program.
CMAKE_AR:FILEPATH=/usr/bin/ar

//Choose the type of build, options are: None Debug Release RelWithDebInfo
// MinSizeRel ...
CMAKE_BUILD_TYPE:STRING=

//Enable/Disable color output during build.
CMAKE_COLOR_MAKEFILE:BOOL=ON

//CXX compiler
CMAKE_CXX_COMPILER:FILEPATH=/usr/bin/c++

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_AR:FILEPATH=/usr/bin/gcc-ar-11

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_RANLIB:FILEPATH=/usr/bin/gcc-ranlib-11

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//C compiler
CMAKE_C_COMPILER:FILEPATH=/usr/bin/cc

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_AR:FILEPATH=/usr/bin/gcc-ar-11

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_RANLIB:FILEPATH=/usr/bin/gcc-ranlib-11

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=-g

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=/home/<USER>/Code/ws_0/build/arm_controller_pkg/CMakeFiles/pkgRedirects

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=/home/<USER>/Code/ws_0/install/arm_controller_pkg

//Path to a program.
CMAKE_LINKER:FILEPATH=/usr/bin/ld

//Path to a program.
CMAKE_MAKE_PROGRAM:FILEPATH=/usr/bin/gmake

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=/usr/bin/nm

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=/usr/bin/objcopy

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=/usr/bin/objdump

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=arm_controller_pkg

//Path to a program.
CMAKE_RANLIB:FILEPATH=/usr/bin/ranlib

//Path to a program.
CMAKE_READELF:FILEPATH=/usr/bin/readelf

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=/usr/bin/strip

//Path to a program.
CMAKE_TAPI:FILEPATH=CMAKE_TAPI-NOTFOUND

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//The directory containing a CMake configuration file for CURL.
CURL_DIR:PATH=CURL_DIR-NOTFOUND

//Path to a file.
CURL_INCLUDE_DIR:PATH=/usr/include/aarch64-linux-gnu

//Path to a library.
CURL_LIBRARY_DEBUG:FILEPATH=CURL_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
CURL_LIBRARY_RELEASE:FILEPATH=/usr/lib/aarch64-linux-gnu/libcurl.so

//The directory containing a CMake configuration file for Eigen3.
Eigen3_DIR:PATH=/usr/share/eigen3/cmake

//Path to a library.
FastCDR_LIBRARY_DEBUG:FILEPATH=FastCDR_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
FastCDR_LIBRARY_RELEASE:FILEPATH=/opt/ros/humble/lib/libfastcdr.so

//Path to a file.
FastRTPS_INCLUDE_DIR:PATH=/opt/ros/humble/include

//Path to a library.
FastRTPS_LIBRARY_DEBUG:FILEPATH=FastRTPS_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
FastRTPS_LIBRARY_RELEASE:FILEPATH=/opt/ros/humble/lib/libfastrtps.so

//The directory containing a CMake configuration file for OCTOMAP.
OCTOMAP_DIR:PATH=/opt/ros/humble/share/octomap

//Path to a library.
OPENSSL_CRYPTO_LIBRARY:FILEPATH=/usr/lib/aarch64-linux-gnu/libcrypto.so

//Path to a file.
OPENSSL_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
OPENSSL_SSL_LIBRARY:FILEPATH=/usr/lib/aarch64-linux-gnu/libssl.so

//Arguments to supply to pkg-config
PKG_CONFIG_ARGN:STRING=

//pkg-config executable
PKG_CONFIG_EXECUTABLE:FILEPATH=/usr/bin/pkg-config

//Path to a program.
Python3_EXECUTABLE:FILEPATH=/usr/local/bin/python3

//Name of the computer/site where compile is being run
SITE:STRING=ubuntu

//Path to a file.
TINYXML2_INCLUDE_DIR:PATH=/usr/include

//Path to a library.
TINYXML2_LIBRARY:FILEPATH=/usr/lib/aarch64-linux-gnu/libtinyxml2.so

//The directory containing a CMake configuration file for TinyXML2.
TinyXML2_DIR:PATH=TinyXML2_DIR-NOTFOUND

//Path to a library.
_lib:FILEPATH=/opt/ros/humble/lib/libstd_srvs__rosidl_typesupport_fastrtps_cpp.so

//The directory containing a CMake configuration file for action_msgs.
action_msgs_DIR:PATH=/opt/ros/humble/share/action_msgs/cmake

//The directory containing a CMake configuration file for ament_cmake.
ament_cmake_DIR:PATH=/opt/ros/humble/share/ament_cmake/cmake

//The directory containing a CMake configuration file for ament_cmake_core.
ament_cmake_core_DIR:PATH=/opt/ros/humble/share/ament_cmake_core/cmake

//The directory containing a CMake configuration file for ament_cmake_cppcheck.
ament_cmake_cppcheck_DIR:PATH=/opt/ros/humble/share/ament_cmake_cppcheck/cmake

//The directory containing a CMake configuration file for ament_cmake_export_definitions.
ament_cmake_export_definitions_DIR:PATH=/opt/ros/humble/share/ament_cmake_export_definitions/cmake

//The directory containing a CMake configuration file for ament_cmake_export_dependencies.
ament_cmake_export_dependencies_DIR:PATH=/opt/ros/humble/share/ament_cmake_export_dependencies/cmake

//The directory containing a CMake configuration file for ament_cmake_export_include_directories.
ament_cmake_export_include_directories_DIR:PATH=/opt/ros/humble/share/ament_cmake_export_include_directories/cmake

//The directory containing a CMake configuration file for ament_cmake_export_interfaces.
ament_cmake_export_interfaces_DIR:PATH=/opt/ros/humble/share/ament_cmake_export_interfaces/cmake

//The directory containing a CMake configuration file for ament_cmake_export_libraries.
ament_cmake_export_libraries_DIR:PATH=/opt/ros/humble/share/ament_cmake_export_libraries/cmake

//The directory containing a CMake configuration file for ament_cmake_export_link_flags.
ament_cmake_export_link_flags_DIR:PATH=/opt/ros/humble/share/ament_cmake_export_link_flags/cmake

//The directory containing a CMake configuration file for ament_cmake_export_targets.
ament_cmake_export_targets_DIR:PATH=/opt/ros/humble/share/ament_cmake_export_targets/cmake

//The directory containing a CMake configuration file for ament_cmake_flake8.
ament_cmake_flake8_DIR:PATH=/opt/ros/humble/share/ament_cmake_flake8/cmake

//The directory containing a CMake configuration file for ament_cmake_gen_version_h.
ament_cmake_gen_version_h_DIR:PATH=/opt/ros/humble/share/ament_cmake_gen_version_h/cmake

//The directory containing a CMake configuration file for ament_cmake_include_directories.
ament_cmake_include_directories_DIR:PATH=/opt/ros/humble/share/ament_cmake_include_directories/cmake

//The directory containing a CMake configuration file for ament_cmake_libraries.
ament_cmake_libraries_DIR:PATH=/opt/ros/humble/share/ament_cmake_libraries/cmake

//The directory containing a CMake configuration file for ament_cmake_lint_cmake.
ament_cmake_lint_cmake_DIR:PATH=/opt/ros/humble/share/ament_cmake_lint_cmake/cmake

//The directory containing a CMake configuration file for ament_cmake_pep257.
ament_cmake_pep257_DIR:PATH=/opt/ros/humble/share/ament_cmake_pep257/cmake

//The directory containing a CMake configuration file for ament_cmake_python.
ament_cmake_python_DIR:PATH=/opt/ros/humble/share/ament_cmake_python/cmake

//The directory containing a CMake configuration file for ament_cmake_target_dependencies.
ament_cmake_target_dependencies_DIR:PATH=/opt/ros/humble/share/ament_cmake_target_dependencies/cmake

//The directory containing a CMake configuration file for ament_cmake_test.
ament_cmake_test_DIR:PATH=/opt/ros/humble/share/ament_cmake_test/cmake

//The directory containing a CMake configuration file for ament_cmake_uncrustify.
ament_cmake_uncrustify_DIR:PATH=/opt/ros/humble/share/ament_cmake_uncrustify/cmake

//The directory containing a CMake configuration file for ament_cmake_version.
ament_cmake_version_DIR:PATH=/opt/ros/humble/share/ament_cmake_version/cmake

//The directory containing a CMake configuration file for ament_cmake_xmllint.
ament_cmake_xmllint_DIR:PATH=/opt/ros/humble/share/ament_cmake_xmllint/cmake

//Path to a program.
ament_cppcheck_BIN:FILEPATH=/opt/ros/humble/bin/ament_cppcheck

//Path to a program.
ament_flake8_BIN:FILEPATH=/opt/ros/humble/bin/ament_flake8

//The directory containing a CMake configuration file for ament_index_cpp.
ament_index_cpp_DIR:PATH=/opt/ros/humble/share/ament_index_cpp/cmake

//The directory containing a CMake configuration file for ament_lint_auto.
ament_lint_auto_DIR:PATH=/opt/ros/humble/share/ament_lint_auto/cmake

//Path to a program.
ament_lint_cmake_BIN:FILEPATH=/opt/ros/humble/bin/ament_lint_cmake

//The directory containing a CMake configuration file for ament_lint_common.
ament_lint_common_DIR:PATH=/opt/ros/humble/share/ament_lint_common/cmake

//Path to a program.
ament_pep257_BIN:FILEPATH=/opt/ros/humble/bin/ament_pep257

//Path to a program.
ament_uncrustify_BIN:FILEPATH=/opt/ros/humble/bin/ament_uncrustify

//Path to a program.
ament_xmllint_BIN:FILEPATH=/opt/ros/humble/bin/ament_xmllint

//The directory containing a CMake configuration file for angles.
angles_DIR:PATH=/opt/ros/humble/share/angles/cmake

//Value Computed by CMake
arm_controller_pkg_BINARY_DIR:STATIC=/home/<USER>/Code/ws_0/build/arm_controller_pkg

//Value Computed by CMake
arm_controller_pkg_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
arm_controller_pkg_SOURCE_DIR:STATIC=/home/<USER>/Code/ws_0/src/arm_controller_pkg

//The directory containing a CMake configuration file for boost_atomic.
boost_atomic_DIR:PATH=/usr/lib/aarch64-linux-gnu/cmake/boost_atomic-1.74.0

//The directory containing a CMake configuration file for boost_chrono.
boost_chrono_DIR:PATH=/usr/lib/aarch64-linux-gnu/cmake/boost_chrono-1.74.0

//The directory containing a CMake configuration file for boost_date_time.
boost_date_time_DIR:PATH=/usr/lib/aarch64-linux-gnu/cmake/boost_date_time-1.74.0

//The directory containing a CMake configuration file for boost_filesystem.
boost_filesystem_DIR:PATH=/usr/lib/aarch64-linux-gnu/cmake/boost_filesystem-1.74.0

//The directory containing a CMake configuration file for boost_headers.
boost_headers_DIR:PATH=/usr/lib/aarch64-linux-gnu/cmake/boost_headers-1.74.0

//The directory containing a CMake configuration file for boost_iostreams.
boost_iostreams_DIR:PATH=/usr/lib/aarch64-linux-gnu/cmake/boost_iostreams-1.74.0

//The directory containing a CMake configuration file for boost_program_options.
boost_program_options_DIR:PATH=/usr/lib/aarch64-linux-gnu/cmake/boost_program_options-1.74.0

//The directory containing a CMake configuration file for boost_regex.
boost_regex_DIR:PATH=/usr/lib/aarch64-linux-gnu/cmake/boost_regex-1.74.0

//The directory containing a CMake configuration file for boost_serialization.
boost_serialization_DIR:PATH=/usr/lib/aarch64-linux-gnu/cmake/boost_serialization-1.74.0

//The directory containing a CMake configuration file for boost_system.
boost_system_DIR:PATH=/usr/lib/aarch64-linux-gnu/cmake/boost_system-1.74.0

//The directory containing a CMake configuration file for boost_thread.
boost_thread_DIR:PATH=/usr/lib/aarch64-linux-gnu/cmake/boost_thread-1.74.0

//The directory containing a CMake configuration file for builtin_interfaces.
builtin_interfaces_DIR:PATH=/opt/ros/humble/share/builtin_interfaces/cmake

//The directory containing a CMake configuration file for class_loader.
class_loader_DIR:PATH=/opt/ros/humble/share/class_loader/cmake

//The directory containing a CMake configuration file for common_interfaces.
common_interfaces_DIR:PATH=/opt/ros/humble/share/common_interfaces/cmake

//The directory containing a CMake configuration file for composition_interfaces.
composition_interfaces_DIR:PATH=/opt/ros/humble/share/composition_interfaces/cmake

//The directory containing a CMake configuration file for console_bridge.
console_bridge_DIR:PATH=/usr/lib/aarch64-linux-gnu/console_bridge/cmake

//The directory containing a CMake configuration file for console_bridge_vendor.
console_bridge_vendor_DIR:PATH=/opt/ros/humble/share/console_bridge_vendor/cmake

//The directory containing a CMake configuration file for eigen3_cmake_module.
eigen3_cmake_module_DIR:PATH=/opt/ros/humble/share/eigen3_cmake_module/cmake

//The directory containing a CMake configuration file for eigen_stl_containers.
eigen_stl_containers_DIR:PATH=/opt/ros/humble/share/eigen_stl_containers/cmake

//The directory containing a CMake configuration file for fastcdr.
fastcdr_DIR:PATH=/opt/ros/humble/lib/cmake/fastcdr

//The directory containing a CMake configuration file for fastrtps.
fastrtps_DIR:PATH=/opt/ros/humble/share/fastrtps/cmake

//The directory containing a CMake configuration file for fastrtps_cmake_module.
fastrtps_cmake_module_DIR:PATH=/opt/ros/humble/share/fastrtps_cmake_module/cmake

//The directory containing a CMake configuration file for fcl.
fcl_DIR:PATH=/usr/lib/aarch64-linux-gnu/cmake/fcl

//The directory containing a CMake configuration file for fmt.
fmt_DIR:PATH=/usr/lib/aarch64-linux-gnu/cmake/fmt

//The directory containing a CMake configuration file for foonathan_memory.
foonathan_memory_DIR:PATH=/opt/ros/humble/lib/foonathan_memory/cmake

//The directory containing a CMake configuration file for generate_parameter_library.
generate_parameter_library_DIR:PATH=/opt/ros/humble/share/generate_parameter_library/cmake

//The directory containing a CMake configuration file for geometric_shapes.
geometric_shapes_DIR:PATH=/opt/ros/humble/share/geometric_shapes/cmake

//The directory containing a CMake configuration file for geometry_msgs.
geometry_msgs_DIR:PATH=/opt/ros/humble/share/geometry_msgs/cmake

//The directory containing a CMake configuration file for kdl_parser.
kdl_parser_DIR:PATH=/opt/ros/humble/share/kdl_parser/cmake

//The directory containing a CMake configuration file for libcurl_vendor.
libcurl_vendor_DIR:PATH=/opt/ros/humble/share/libcurl_vendor/cmake

//The directory containing a CMake configuration file for libstatistics_collector.
libstatistics_collector_DIR:PATH=/opt/ros/humble/share/libstatistics_collector/cmake

//The directory containing a CMake configuration file for libyaml_vendor.
libyaml_vendor_DIR:PATH=/opt/ros/humble/share/libyaml_vendor/cmake

//The directory containing a CMake configuration file for lifecycle_msgs.
lifecycle_msgs_DIR:PATH=/opt/ros/humble/share/lifecycle_msgs/cmake

//The directory containing a CMake configuration file for message_filters.
message_filters_DIR:PATH=/opt/ros/humble/share/message_filters/cmake

//The directory containing a CMake configuration file for moveit.
moveit_DIR:PATH=/home/<USER>/moveit2_ws/install/moveit/share/moveit/cmake

//The directory containing a CMake configuration file for moveit_core.
moveit_core_DIR:PATH=/home/<USER>/moveit2_ws/install/moveit_core/share/moveit_core/cmake

//The directory containing a CMake configuration file for moveit_msgs.
moveit_msgs_DIR:PATH=/opt/ros/humble/share/moveit_msgs/cmake

//The directory containing a CMake configuration file for moveit_ros_move_group.
moveit_ros_move_group_DIR:PATH=/home/<USER>/moveit2_ws/install/moveit_ros_move_group/share/moveit_ros_move_group/cmake

//The directory containing a CMake configuration file for moveit_ros_occupancy_map_monitor.
moveit_ros_occupancy_map_monitor_DIR:PATH=/home/<USER>/moveit2_ws/install/moveit_ros_occupancy_map_monitor/share/moveit_ros_occupancy_map_monitor/cmake

//The directory containing a CMake configuration file for moveit_ros_planning.
moveit_ros_planning_DIR:PATH=/home/<USER>/moveit2_ws/install/moveit_ros_planning/share/moveit_ros_planning/cmake

//The directory containing a CMake configuration file for moveit_ros_planning_interface.
moveit_ros_planning_interface_DIR:PATH=/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake

//The directory containing a CMake configuration file for moveit_ros_warehouse.
moveit_ros_warehouse_DIR:PATH=/home/<USER>/moveit2_ws/install/moveit_ros_warehouse/share/moveit_ros_warehouse/cmake

//The directory containing a CMake configuration file for object_recognition_msgs.
object_recognition_msgs_DIR:PATH=/opt/ros/humble/share/object_recognition_msgs/cmake

//The directory containing a CMake configuration file for octomap.
octomap_DIR:PATH=/opt/ros/humble/share/octomap

//The directory containing a CMake configuration file for octomap_msgs.
octomap_msgs_DIR:PATH=/opt/ros/humble/share/octomap_msgs/cmake

//Path to a library.
onelib:FILEPATH=/opt/ros/humble/lib/aarch64-linux-gnu/liburdfdom_world.so

//Path to a library.
onelibd:FILEPATH=onelibd-NOTFOUND

//The directory containing a CMake configuration file for orocos_kdl.
orocos_kdl_DIR:PATH=/usr/lib/aarch64-linux-gnu/cmake/orocos_kdl

//Path to a library.
orocos_kdl_LIBRARY:FILEPATH=/usr/lib/aarch64-linux-gnu/liborocos-kdl.so

//The directory containing a CMake configuration file for orocos_kdl_vendor.
orocos_kdl_vendor_DIR:PATH=/opt/ros/humble/share/orocos_kdl_vendor/cmake

//The directory containing a CMake configuration file for parameter_traits.
parameter_traits_DIR:PATH=/opt/ros/humble/share/parameter_traits/cmake

//Path to a library.
pkgcfg_lib_PC_CURL_curl:FILEPATH=/usr/lib/aarch64-linux-gnu/libcurl.so

//Path to a library.
pkgcfg_lib__OPENSSL_crypto:FILEPATH=/usr/lib/aarch64-linux-gnu/libcrypto.so

//Path to a library.
pkgcfg_lib__OPENSSL_ssl:FILEPATH=/usr/lib/aarch64-linux-gnu/libssl.so

//The directory containing a CMake configuration file for pluginlib.
pluginlib_DIR:PATH=/opt/ros/humble/share/pluginlib/cmake

//The directory containing a CMake configuration file for random_numbers.
random_numbers_DIR:PATH=/opt/ros/humble/share/random_numbers/cmake

//The directory containing a CMake configuration file for rcl.
rcl_DIR:PATH=/opt/ros/humble/share/rcl/cmake

//The directory containing a CMake configuration file for rcl_action.
rcl_action_DIR:PATH=/opt/ros/humble/share/rcl_action/cmake

//The directory containing a CMake configuration file for rcl_interfaces.
rcl_interfaces_DIR:PATH=/opt/ros/humble/share/rcl_interfaces/cmake

//The directory containing a CMake configuration file for rcl_lifecycle.
rcl_lifecycle_DIR:PATH=/opt/ros/humble/share/rcl_lifecycle/cmake

//The directory containing a CMake configuration file for rcl_logging_interface.
rcl_logging_interface_DIR:PATH=/opt/ros/humble/share/rcl_logging_interface/cmake

//The directory containing a CMake configuration file for rcl_logging_spdlog.
rcl_logging_spdlog_DIR:PATH=/opt/ros/humble/share/rcl_logging_spdlog/cmake

//The directory containing a CMake configuration file for rcl_yaml_param_parser.
rcl_yaml_param_parser_DIR:PATH=/opt/ros/humble/share/rcl_yaml_param_parser/cmake

//The directory containing a CMake configuration file for rclcpp.
rclcpp_DIR:PATH=/opt/ros/humble/share/rclcpp/cmake

//The directory containing a CMake configuration file for rclcpp_action.
rclcpp_action_DIR:PATH=/opt/ros/humble/share/rclcpp_action/cmake

//The directory containing a CMake configuration file for rclcpp_components.
rclcpp_components_DIR:PATH=/opt/ros/humble/share/rclcpp_components/cmake

//The directory containing a CMake configuration file for rclcpp_lifecycle.
rclcpp_lifecycle_DIR:PATH=/opt/ros/humble/share/rclcpp_lifecycle/cmake

//The directory containing a CMake configuration file for rcpputils.
rcpputils_DIR:PATH=/opt/ros/humble/share/rcpputils/cmake

//The directory containing a CMake configuration file for rcutils.
rcutils_DIR:PATH=/opt/ros/humble/share/rcutils/cmake

//The directory containing a CMake configuration file for resource_retriever.
resource_retriever_DIR:PATH=/opt/ros/humble/share/resource_retriever/cmake

//The directory containing a CMake configuration file for rmw.
rmw_DIR:PATH=/opt/ros/humble/share/rmw/cmake

//The directory containing a CMake configuration file for rmw_dds_common.
rmw_dds_common_DIR:PATH=/opt/ros/humble/share/rmw_dds_common/cmake

//The directory containing a CMake configuration file for rmw_fastrtps_cpp.
rmw_fastrtps_cpp_DIR:PATH=/opt/ros/humble/share/rmw_fastrtps_cpp/cmake

//The directory containing a CMake configuration file for rmw_fastrtps_shared_cpp.
rmw_fastrtps_shared_cpp_DIR:PATH=/opt/ros/humble/share/rmw_fastrtps_shared_cpp/cmake

//The directory containing a CMake configuration file for rmw_implementation.
rmw_implementation_DIR:PATH=/opt/ros/humble/share/rmw_implementation/cmake

//The directory containing a CMake configuration file for rmw_implementation_cmake.
rmw_implementation_cmake_DIR:PATH=/opt/ros/humble/share/rmw_implementation_cmake/cmake

//The directory containing a CMake configuration file for rosgraph_msgs.
rosgraph_msgs_DIR:PATH=/opt/ros/humble/share/rosgraph_msgs/cmake

//The directory containing a CMake configuration file for rosidl_adapter.
rosidl_adapter_DIR:PATH=/opt/ros/humble/share/rosidl_adapter/cmake

//The directory containing a CMake configuration file for rosidl_cmake.
rosidl_cmake_DIR:PATH=/opt/ros/humble/share/rosidl_cmake/cmake

//The directory containing a CMake configuration file for rosidl_default_runtime.
rosidl_default_runtime_DIR:PATH=/opt/ros/humble/share/rosidl_default_runtime/cmake

//The directory containing a CMake configuration file for rosidl_generator_c.
rosidl_generator_c_DIR:PATH=/opt/ros/humble/share/rosidl_generator_c/cmake

//The directory containing a CMake configuration file for rosidl_generator_cpp.
rosidl_generator_cpp_DIR:PATH=/opt/ros/humble/share/rosidl_generator_cpp/cmake

//The directory containing a CMake configuration file for rosidl_runtime_c.
rosidl_runtime_c_DIR:PATH=/opt/ros/humble/share/rosidl_runtime_c/cmake

//The directory containing a CMake configuration file for rosidl_runtime_cpp.
rosidl_runtime_cpp_DIR:PATH=/opt/ros/humble/share/rosidl_runtime_cpp/cmake

//The directory containing a CMake configuration file for rosidl_typesupport_c.
rosidl_typesupport_c_DIR:PATH=/opt/ros/humble/share/rosidl_typesupport_c/cmake

//The directory containing a CMake configuration file for rosidl_typesupport_cpp.
rosidl_typesupport_cpp_DIR:PATH=/opt/ros/humble/share/rosidl_typesupport_cpp/cmake

//The directory containing a CMake configuration file for rosidl_typesupport_fastrtps_c.
rosidl_typesupport_fastrtps_c_DIR:PATH=/opt/ros/humble/share/rosidl_typesupport_fastrtps_c/cmake

//The directory containing a CMake configuration file for rosidl_typesupport_fastrtps_cpp.
rosidl_typesupport_fastrtps_cpp_DIR:PATH=/opt/ros/humble/share/rosidl_typesupport_fastrtps_cpp/cmake

//The directory containing a CMake configuration file for rosidl_typesupport_interface.
rosidl_typesupport_interface_DIR:PATH=/opt/ros/humble/share/rosidl_typesupport_interface/cmake

//The directory containing a CMake configuration file for rosidl_typesupport_introspection_c.
rosidl_typesupport_introspection_c_DIR:PATH=/opt/ros/humble/share/rosidl_typesupport_introspection_c/cmake

//The directory containing a CMake configuration file for rosidl_typesupport_introspection_cpp.
rosidl_typesupport_introspection_cpp_DIR:PATH=/opt/ros/humble/share/rosidl_typesupport_introspection_cpp/cmake

//The directory containing a CMake configuration file for rsl.
rsl_DIR:PATH=/opt/ros/humble/share/rsl/cmake

//The directory containing a CMake configuration file for ruckig.
ruckig_DIR:PATH=/opt/ros/humble/lib/aarch64-linux-gnu/cmake/ruckig

//The directory containing a CMake configuration file for sensor_msgs.
sensor_msgs_DIR:PATH=/opt/ros/humble/share/sensor_msgs/cmake

//The directory containing a CMake configuration file for shape_msgs.
shape_msgs_DIR:PATH=/opt/ros/humble/share/shape_msgs/cmake

//The directory containing a CMake configuration file for spdlog.
spdlog_DIR:PATH=/usr/lib/aarch64-linux-gnu/cmake/spdlog

//The directory containing a CMake configuration file for spdlog_vendor.
spdlog_vendor_DIR:PATH=/opt/ros/humble/share/spdlog_vendor/cmake

//The directory containing a CMake configuration file for srdfdom.
srdfdom_DIR:PATH=/home/<USER>/moveit2_ws/install/srdfdom/share/srdfdom/cmake

//The directory containing a CMake configuration file for statistics_msgs.
statistics_msgs_DIR:PATH=/opt/ros/humble/share/statistics_msgs/cmake

//The directory containing a CMake configuration file for std_msgs.
std_msgs_DIR:PATH=/opt/ros/humble/share/std_msgs/cmake

//The directory containing a CMake configuration file for std_srvs.
std_srvs_DIR:PATH=/opt/ros/humble/share/std_srvs/cmake

//The directory containing a CMake configuration file for tcb_span.
tcb_span_DIR:PATH=/opt/ros/humble/share/tcb_span/cmake

//The directory containing a CMake configuration file for tf2.
tf2_DIR:PATH=/opt/ros/humble/share/tf2/cmake

//The directory containing a CMake configuration file for tf2_eigen.
tf2_eigen_DIR:PATH=/opt/ros/humble/share/tf2_eigen/cmake

//The directory containing a CMake configuration file for tf2_geometry_msgs.
tf2_geometry_msgs_DIR:PATH=/opt/ros/humble/share/tf2_geometry_msgs/cmake

//The directory containing a CMake configuration file for tf2_kdl.
tf2_kdl_DIR:PATH=/opt/ros/humble/share/tf2_kdl/cmake

//The directory containing a CMake configuration file for tf2_msgs.
tf2_msgs_DIR:PATH=/opt/ros/humble/share/tf2_msgs/cmake

//The directory containing a CMake configuration file for tf2_ros.
tf2_ros_DIR:PATH=/opt/ros/humble/share/tf2_ros/cmake

//The directory containing a CMake configuration file for tinyxml2_vendor.
tinyxml2_vendor_DIR:PATH=/opt/ros/humble/share/tinyxml2_vendor/cmake

//The directory containing a CMake configuration file for tl_expected.
tl_expected_DIR:PATH=/opt/ros/humble/share/tl_expected/cmake

//The directory containing a CMake configuration file for tracetools.
tracetools_DIR:PATH=/opt/ros/humble/share/tracetools/cmake

//The directory containing a CMake configuration file for trajectory_msgs.
trajectory_msgs_DIR:PATH=/opt/ros/humble/share/trajectory_msgs/cmake

//The directory containing a CMake configuration file for unique_identifier_msgs.
unique_identifier_msgs_DIR:PATH=/opt/ros/humble/share/unique_identifier_msgs/cmake

//The directory containing a CMake configuration file for urdf.
urdf_DIR:PATH=/opt/ros/humble/share/urdf/cmake

//The directory containing a CMake configuration file for urdf_parser_plugin.
urdf_parser_plugin_DIR:PATH=/opt/ros/humble/share/urdf_parser_plugin/cmake

//The directory containing a CMake configuration file for urdfdom.
urdfdom_DIR:PATH=/opt/ros/humble/lib/aarch64-linux-gnu/urdfdom/cmake

//The directory containing a CMake configuration file for urdfdom_headers.
urdfdom_headers_DIR:PATH=/opt/ros/humble/lib/aarch64-linux-gnu/urdfdom_headers/cmake

//The directory containing a CMake configuration file for visualization_msgs.
visualization_msgs_DIR:PATH=/opt/ros/humble/share/visualization_msgs/cmake

//The directory containing a CMake configuration file for warehouse_ros.
warehouse_ros_DIR:PATH=/opt/ros/humble/share/warehouse_ros/cmake

//Path to a program.
xmllint_BIN:FILEPATH=/usr/bin/xmllint

//The directory containing a CMake configuration file for yaml.
yaml_DIR:PATH=/opt/ros/humble/cmake


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: BULLET_COLLISION_LIBRARY
BULLET_COLLISION_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BULLET_COLLISION_LIBRARY_DEBUG
BULLET_COLLISION_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BULLET_DYNAMICS_LIBRARY
BULLET_DYNAMICS_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BULLET_DYNAMICS_LIBRARY_DEBUG
BULLET_DYNAMICS_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BULLET_MATH_LIBRARY
BULLET_MATH_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BULLET_MATH_LIBRARY_DEBUG
BULLET_MATH_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BULLET_SOFTBODY_LIBRARY
BULLET_SOFTBODY_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BULLET_SOFTBODY_LIBRARY_DEBUG
BULLET_SOFTBODY_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_DIR
Boost_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/home/<USER>/Code/ws_0/build/arm_controller_pkg
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=4
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=0
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=0
//ADVANCED property for variable: CMAKE_COLOR_MAKEFILE
CMAKE_COLOR_MAKEFILE-ADVANCED:INTERNAL=1
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/usr/local/cmake/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/usr/local/cmake/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/usr/local/cmake/bin/ctest
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_AR
CMAKE_CXX_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_RANLIB
CMAKE_CXX_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER
CMAKE_C_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_AR
CMAKE_C_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_RANLIB
CMAKE_C_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=ELF
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Unix Makefiles
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Test CMAKE_HAVE_LIBC_PTHREAD
CMAKE_HAVE_LIBC_PTHREAD:INTERNAL=1
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/home/<USER>/Code/ws_0/src/arm_controller_pkg
//Install .so files without execute permission.
CMAKE_INSTALL_SO_NO_EXE:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//Name of CMakeLists files to read
CMAKE_LIST_FILE_NAME:INTERNAL=CMakeLists.txt
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/usr/local/cmake/share/cmake-4.0
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_TAPI
CMAKE_TAPI-ADVANCED:INTERNAL=1
//uname command
CMAKE_UNAME:INTERNAL=/usr/bin/uname
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CURL_DIR
CURL_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CURL_INCLUDE_DIR
CURL_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CURL_LIBRARY_DEBUG
CURL_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CURL_LIBRARY_RELEASE
CURL_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//Details about finding Boost
FIND_PACKAGE_MESSAGE_DETAILS_Boost:INTERNAL=[/usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake][found components: system filesystem date_time program_options thread ][v1.74.0()]
//Details about finding Eigen3
FIND_PACKAGE_MESSAGE_DETAILS_Eigen3:INTERNAL=[1][v3.4.0()]
//Details about finding FastRTPS
FIND_PACKAGE_MESSAGE_DETAILS_FastRTPS:INTERNAL=[/opt/ros/humble/include][/opt/ros/humble/lib/libfastrtps.so;/opt/ros/humble/lib/libfastcdr.so][v()]
//Details about finding OpenSSL
FIND_PACKAGE_MESSAGE_DETAILS_OpenSSL:INTERNAL=[/usr/lib/aarch64-linux-gnu/libcrypto.so][/usr/include][ ][v3.0.2()]
//Details about finding Python3
FIND_PACKAGE_MESSAGE_DETAILS_Python3:INTERNAL=[/usr/local/bin/python3][found components: Interpreter ][v3.10.12()]
//Details about finding Threads
FIND_PACKAGE_MESSAGE_DETAILS_Threads:INTERNAL=[TRUE][v()]
//ADVANCED property for variable: OPENSSL_CRYPTO_LIBRARY
OPENSSL_CRYPTO_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENSSL_INCLUDE_DIR
OPENSSL_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENSSL_SSL_LIBRARY
OPENSSL_SSL_LIBRARY-ADVANCED:INTERNAL=1
PC_CURL_CFLAGS:INTERNAL=-I/usr/include/aarch64-linux-gnu
PC_CURL_CFLAGS_I:INTERNAL=
PC_CURL_CFLAGS_OTHER:INTERNAL=
PC_CURL_FOUND:INTERNAL=1
PC_CURL_INCLUDEDIR:INTERNAL=/usr/include/aarch64-linux-gnu
PC_CURL_INCLUDE_DIRS:INTERNAL=/usr/include/aarch64-linux-gnu
PC_CURL_LDFLAGS:INTERNAL=-L/usr/lib/aarch64-linux-gnu;-lcurl
PC_CURL_LDFLAGS_OTHER:INTERNAL=
PC_CURL_LIBDIR:INTERNAL=/usr/lib/aarch64-linux-gnu
PC_CURL_LIBRARIES:INTERNAL=curl
PC_CURL_LIBRARY_DIRS:INTERNAL=/usr/lib/aarch64-linux-gnu
PC_CURL_LIBS:INTERNAL=
PC_CURL_LIBS_L:INTERNAL=
PC_CURL_LIBS_OTHER:INTERNAL=
PC_CURL_LIBS_PATHS:INTERNAL=
PC_CURL_MODULE_NAME:INTERNAL=libcurl
PC_CURL_PREFIX:INTERNAL=/usr
PC_CURL_STATIC_CFLAGS:INTERNAL=-I/usr/include/aarch64-linux-gnu
PC_CURL_STATIC_CFLAGS_I:INTERNAL=
PC_CURL_STATIC_CFLAGS_OTHER:INTERNAL=
PC_CURL_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include/aarch64-linux-gnu
PC_CURL_STATIC_LDFLAGS:INTERNAL=-L/usr/lib/aarch64-linux-gnu;-L/usr/lib/aarch64-linux-gnu/mit-krb5;-lcurl;-lnghttp2;-lidn2;-lrtmp;-lssh;-lpsl;-lssl;-lcrypto;-lssl;-lcrypto;-lgssapi_krb5;-llber;-lldap;-llber;-lzstd;-lbrotlidec;-lz
PC_CURL_STATIC_LDFLAGS_OTHER:INTERNAL=
PC_CURL_STATIC_LIBDIR:INTERNAL=
PC_CURL_STATIC_LIBRARIES:INTERNAL=curl;nghttp2;idn2;rtmp;ssh;psl;ssl;crypto;ssl;crypto;gssapi_krb5;lber;ldap;lber;zstd;brotlidec;z
PC_CURL_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib/aarch64-linux-gnu;/usr/lib/aarch64-linux-gnu/mit-krb5
PC_CURL_STATIC_LIBS:INTERNAL=
PC_CURL_STATIC_LIBS_L:INTERNAL=
PC_CURL_STATIC_LIBS_OTHER:INTERNAL=
PC_CURL_STATIC_LIBS_PATHS:INTERNAL=
PC_CURL_VERSION:INTERNAL=7.81.0
PC_CURL_libcurl_INCLUDEDIR:INTERNAL=
PC_CURL_libcurl_LIBDIR:INTERNAL=
PC_CURL_libcurl_PREFIX:INTERNAL=
PC_CURL_libcurl_VERSION:INTERNAL=
//ADVANCED property for variable: PKG_CONFIG_ARGN
PKG_CONFIG_ARGN-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_EXECUTABLE
PKG_CONFIG_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: TINYXML2_INCLUDE_DIR
TINYXML2_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: TINYXML2_LIBRARY
TINYXML2_LIBRARY-ADVANCED:INTERNAL=1
_OPENSSL_CFLAGS:INTERNAL=-I/usr/include
_OPENSSL_CFLAGS_I:INTERNAL=
_OPENSSL_CFLAGS_OTHER:INTERNAL=
_OPENSSL_FOUND:INTERNAL=1
_OPENSSL_INCLUDEDIR:INTERNAL=/usr/include
_OPENSSL_INCLUDE_DIRS:INTERNAL=/usr/include
_OPENSSL_LDFLAGS:INTERNAL=-L/usr/lib/aarch64-linux-gnu;-lssl;-lcrypto
_OPENSSL_LDFLAGS_OTHER:INTERNAL=
_OPENSSL_LIBDIR:INTERNAL=/usr/lib/aarch64-linux-gnu
_OPENSSL_LIBRARIES:INTERNAL=ssl;crypto
_OPENSSL_LIBRARY_DIRS:INTERNAL=/usr/lib/aarch64-linux-gnu
_OPENSSL_LIBS:INTERNAL=
_OPENSSL_LIBS_L:INTERNAL=
_OPENSSL_LIBS_OTHER:INTERNAL=
_OPENSSL_LIBS_PATHS:INTERNAL=
_OPENSSL_MODULE_NAME:INTERNAL=openssl
_OPENSSL_PREFIX:INTERNAL=/usr
_OPENSSL_STATIC_CFLAGS:INTERNAL=-I/usr/include
_OPENSSL_STATIC_CFLAGS_I:INTERNAL=
_OPENSSL_STATIC_CFLAGS_OTHER:INTERNAL=
_OPENSSL_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include
_OPENSSL_STATIC_LDFLAGS:INTERNAL=-L/usr/lib/aarch64-linux-gnu;-lssl;-lcrypto;-ldl;-pthread
_OPENSSL_STATIC_LDFLAGS_OTHER:INTERNAL=-pthread
_OPENSSL_STATIC_LIBDIR:INTERNAL=
_OPENSSL_STATIC_LIBRARIES:INTERNAL=ssl;crypto;dl
_OPENSSL_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib/aarch64-linux-gnu
_OPENSSL_STATIC_LIBS:INTERNAL=
_OPENSSL_STATIC_LIBS_L:INTERNAL=
_OPENSSL_STATIC_LIBS_OTHER:INTERNAL=
_OPENSSL_STATIC_LIBS_PATHS:INTERNAL=
_OPENSSL_VERSION:INTERNAL=3.0.2
_OPENSSL_openssl_INCLUDEDIR:INTERNAL=
_OPENSSL_openssl_LIBDIR:INTERNAL=
_OPENSSL_openssl_PREFIX:INTERNAL=
_OPENSSL_openssl_VERSION:INTERNAL=
//Compiler reason failure
_Python3_Compiler_REASON_FAILURE:INTERNAL=
//Development reason failure
_Python3_Development_REASON_FAILURE:INTERNAL=
_Python3_EXECUTABLE:INTERNAL=/usr/local/bin/python3
//Python3 Properties
_Python3_INTERPRETER_PROPERTIES:INTERNAL=Python;3;10;12;64;32;<none>;cpython-310-aarch64-linux-gnu;abi3;/usr/lib/python3.10;/usr/lib/python3.10;/usr/local/lib/python3.10/dist-packages;/usr/local/lib/python3.10/dist-packages
_Python3_INTERPRETER_SIGNATURE:INTERNAL=33d5709af582ef0f7f4b5b284ada8370
//NumPy reason failure
_Python3_NumPy_REASON_FAILURE:INTERNAL=
__pkg_config_arguments_PC_CURL:INTERNAL=QUIET;libcurl
__pkg_config_arguments__OPENSSL:INTERNAL=QUIET;openssl
__pkg_config_checked_PC_CURL:INTERNAL=1
__pkg_config_checked__OPENSSL:INTERNAL=1
//ADVANCED property for variable: boost_atomic_DIR
boost_atomic_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: boost_chrono_DIR
boost_chrono_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: boost_date_time_DIR
boost_date_time_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: boost_filesystem_DIR
boost_filesystem_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: boost_headers_DIR
boost_headers_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: boost_iostreams_DIR
boost_iostreams_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: boost_program_options_DIR
boost_program_options_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: boost_regex_DIR
boost_regex_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: boost_serialization_DIR
boost_serialization_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: boost_system_DIR
boost_system_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: boost_thread_DIR
boost_thread_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_PC_CURL_curl
pkgcfg_lib_PC_CURL_curl-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib__OPENSSL_crypto
pkgcfg_lib__OPENSSL_crypto-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib__OPENSSL_ssl
pkgcfg_lib__OPENSSL_ssl-ADVANCED:INTERNAL=1
prefix_result:INTERNAL=AsynchDNS;GSS-API;HSTS;HTTP2;HTTPS-proxy;IDN;IPv6;Kerberos;Largefile;NTLM;NTLM_WB;PSL;SPNEGO;SSL;TLS-SRP;UnixSockets;alt-svc;brotli;libz;zstd

