set(_AMENT_PACKAGE_NAME "arm_controller_pkg")
set(arm_controller_pkg_VERSION "0.0.0")
set(arm_controller_pkg_MAINTAINER "mac <<EMAIL>>")
set(arm_controller_pkg_BUILD_DEPENDS "rclcpp" "tf2" "tf2_geometry_msgs" "geometry_msgs" "moveit_ros_planning_interface" "moveit_msgs" "shape_msgs" "tf2_ros")
set(arm_controller_pkg_BUILDTOOL_DEPENDS "ament_cmake")
set(arm_controller_pkg_BUILD_EXPORT_DEPENDS "rclcpp" "tf2" "tf2_geometry_msgs" "geometry_msgs" "moveit_ros_planning_interface" "moveit_msgs" "shape_msgs" "tf2_ros")
set(arm_controller_pkg_BUILDTOOL_EXPORT_DEPENDS )
set(arm_controller_pkg_EXEC_DEPENDS "rclcpp" "tf2" "tf2_geometry_msgs" "geometry_msgs" "moveit_ros_planning_interface" "moveit_msgs" "shape_msgs" "tf2_ros")
set(arm_controller_pkg_TEST_DEPENDS "ament_lint_auto" "ament_lint_common")
set(arm_controller_pkg_GROUP_DEPENDS )
set(arm_controller_pkg_MEMBER_OF_GROUPS )
set(arm_controller_pkg_DEPRECATED "")
set(arm_controller_pkg_EXPORT_TAGS)
list(APPEND arm_controller_pkg_EXPORT_TAGS "<build_type>ament_cmake</build_type>")
