[0.140s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.140s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=8, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0xffffae88ab60>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0xffffae88a650>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0xffffae88a650>>, mixin_verb=('build',))
[0.246s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.246s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.246s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.246s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.246s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.247s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.247s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/Code/ws_0'
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.247s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.257s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extensions ['ignore', 'ignore_ament_install']
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extension 'ignore'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extension 'ignore_ament_install'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extensions ['colcon_pkg']
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extension 'colcon_pkg'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extensions ['colcon_meta']
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extension 'colcon_meta'
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extensions ['ros']
[0.258s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extension 'ros'
[0.260s] DEBUG:colcon.colcon_core.package_identification:Package 'src/arm_controller_pkg' with type 'ros.ament_cmake' and name 'arm_controller_pkg'
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extensions ['ignore', 'ignore_ament_install']
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extension 'ignore'
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extension 'ignore_ament_install'
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extensions ['colcon_pkg']
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extension 'colcon_pkg'
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extensions ['colcon_meta']
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extension 'colcon_meta'
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extensions ['ros']
[0.260s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extension 'ros'
[0.261s] DEBUG:colcon.colcon_core.package_identification:Package 'src/engineer_vision_pkg' with type 'ros.ament_cmake' and name 'engineer_vision_pkg'
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extensions ['ignore', 'ignore_ament_install']
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extension 'ignore'
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extension 'ignore_ament_install'
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extensions ['colcon_pkg']
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extension 'colcon_pkg'
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extensions ['colcon_meta']
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extension 'colcon_meta'
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extensions ['ros']
[0.261s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extension 'ros'
[0.261s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rm_vision' with type 'ros.ament_cmake' and name 'rm_vision'
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extensions ['ignore', 'ignore_ament_install']
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'ignore'
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'ignore_ament_install'
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extensions ['colcon_pkg']
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'colcon_pkg'
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extensions ['colcon_meta']
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'colcon_meta'
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extensions ['ros']
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'ros'
[0.262s] DEBUG:colcon.colcon_core.package_identification:Package 'src/robot_description' with type 'ros.ament_cmake' and name 'robot_description'
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extensions ['ignore', 'ignore_ament_install']
[0.262s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extension 'ignore'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extension 'ignore_ament_install'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extensions ['colcon_pkg']
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extension 'colcon_pkg'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extensions ['colcon_meta']
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extension 'colcon_meta'
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extensions ['ros']
[0.263s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extension 'ros'
[0.263s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ros2_mindvision_camera' with type 'ros.ament_cmake' and name 'mindvision_camera'
[0.263s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.263s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.263s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.263s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.263s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.285s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.285s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.290s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 5 installed packages in /home/<USER>/Code/ws_0/install
[0.292s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 57 installed packages in /home/<USER>/moveit2_ws/install
[0.293s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 323 installed packages in /opt/ros/humble
[0.294s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.339s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'cmake_args' from command line to 'None'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'cmake_target' from command line to 'None'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'cmake_clean_cache' from command line to 'False'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'cmake_clean_first' from command line to 'False'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'cmake_force_configure' from command line to 'False'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'ament_cmake_args' from command line to 'None'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'catkin_cmake_args' from command line to 'None'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.339s] DEBUG:colcon.colcon_core.verb:Building package 'arm_controller_pkg' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Code/ws_0/build/arm_controller_pkg', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Code/ws_0/install/arm_controller_pkg', 'merge_install': False, 'path': '/home/<USER>/Code/ws_0/src/arm_controller_pkg', 'symlink_install': False, 'test_result_base': None}
[0.339s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'cmake_args' from command line to 'None'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'cmake_target' from command line to 'None'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'cmake_clean_cache' from command line to 'False'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'cmake_clean_first' from command line to 'False'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'cmake_force_configure' from command line to 'False'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'ament_cmake_args' from command line to 'None'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'catkin_cmake_args' from command line to 'None'
[0.339s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.339s] DEBUG:colcon.colcon_core.verb:Building package 'engineer_vision_pkg' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Code/ws_0/build/engineer_vision_pkg', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Code/ws_0/install/engineer_vision_pkg', 'merge_install': False, 'path': '/home/<USER>/Code/ws_0/src/engineer_vision_pkg', 'symlink_install': False, 'test_result_base': None}
[0.340s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'cmake_args' from command line to 'None'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'cmake_target' from command line to 'None'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'cmake_clean_cache' from command line to 'False'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'cmake_clean_first' from command line to 'False'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'cmake_force_configure' from command line to 'False'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'ament_cmake_args' from command line to 'None'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'catkin_cmake_args' from command line to 'None'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.340s] DEBUG:colcon.colcon_core.verb:Building package 'mindvision_camera' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Code/ws_0/build/mindvision_camera', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Code/ws_0/install/mindvision_camera', 'merge_install': False, 'path': '/home/<USER>/Code/ws_0/src/ros2_mindvision_camera', 'symlink_install': False, 'test_result_base': None}
[0.340s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'cmake_args' from command line to 'None'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'cmake_target' from command line to 'None'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'cmake_clean_cache' from command line to 'False'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'cmake_clean_first' from command line to 'False'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'cmake_force_configure' from command line to 'False'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'ament_cmake_args' from command line to 'None'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'catkin_cmake_args' from command line to 'None'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.340s] DEBUG:colcon.colcon_core.verb:Building package 'rm_vision' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Code/ws_0/build/rm_vision', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Code/ws_0/install/rm_vision', 'merge_install': False, 'path': '/home/<USER>/Code/ws_0/src/rm_vision', 'symlink_install': False, 'test_result_base': None}
[0.340s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_args' from command line to 'None'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_target' from command line to 'None'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_clean_cache' from command line to 'False'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_clean_first' from command line to 'False'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_force_configure' from command line to 'False'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'ament_cmake_args' from command line to 'None'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'catkin_cmake_args' from command line to 'None'
[0.340s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.340s] DEBUG:colcon.colcon_core.verb:Building package 'robot_description' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Code/ws_0/build/robot_description', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Code/ws_0/install/robot_description', 'merge_install': False, 'path': '/home/<USER>/Code/ws_0/src/robot_description', 'symlink_install': False, 'test_result_base': None}
[0.340s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.341s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.342s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/Code/ws_0/src/arm_controller_pkg' with build type 'ament_cmake'
[0.342s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Code/ws_0/src/arm_controller_pkg'
[0.343s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.343s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.343s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.348s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/Code/ws_0/src/engineer_vision_pkg' with build type 'ament_cmake'
[0.348s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Code/ws_0/src/engineer_vision_pkg'
[0.349s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.349s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.353s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/Code/ws_0/src/ros2_mindvision_camera' with build type 'ament_cmake'
[0.353s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Code/ws_0/src/ros2_mindvision_camera'
[0.354s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.354s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.358s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/Code/ws_0/src/rm_vision' with build type 'ament_cmake'
[0.359s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Code/ws_0/src/rm_vision'
[0.359s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.359s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.365s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/Code/ws_0/src/robot_description' with build type 'ament_cmake'
[0.366s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Code/ws_0/src/robot_description'
[0.366s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.366s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.380s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/arm_controller_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/arm_controller_pkg -- -j8 -l8
[0.384s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/engineer_vision_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/engineer_vision_pkg -- -j8 -l8
[0.391s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/mindvision_camera': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/mindvision_camera -- -j8 -l8
[0.393s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/rm_vision': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/rm_vision -- -j8 -l8
[0.401s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/robot_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/robot_description -- -j8 -l8
[0.428s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/rm_vision' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/rm_vision -- -j8 -l8
[0.428s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/rm_vision': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/rm_vision
[0.440s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rm_vision)
[0.440s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/rm_vision' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/rm_vision
[0.446s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision' for CMake module files
[0.446s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision' for CMake config files
[0.446s] Level 1:colcon.colcon_core.shell:create_environment_hook('rm_vision', 'cmake_prefix_path')
[0.447s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/hook/cmake_prefix_path.ps1'
[0.447s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/hook/cmake_prefix_path.dsv'
[0.448s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/hook/cmake_prefix_path.sh'
[0.449s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision/bin'
[0.449s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision/lib/pkgconfig/rm_vision.pc'
[0.449s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision/lib/python3.10/site-packages'
[0.449s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision/bin'
[0.450s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.ps1'
[0.450s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.dsv'
[0.451s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.sh'
[0.452s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.bash'
[0.452s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.zsh'
[0.453s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/rm_vision/share/colcon-core/packages/rm_vision)
[0.453s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rm_vision)
[0.453s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision' for CMake module files
[0.454s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision' for CMake config files
[0.454s] Level 1:colcon.colcon_core.shell:create_environment_hook('rm_vision', 'cmake_prefix_path')
[0.454s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/hook/cmake_prefix_path.ps1'
[0.454s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/hook/cmake_prefix_path.dsv'
[0.455s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/hook/cmake_prefix_path.sh'
[0.455s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision/bin'
[0.455s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision/lib/pkgconfig/rm_vision.pc'
[0.456s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision/lib/python3.10/site-packages'
[0.456s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision/bin'
[0.456s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.ps1'
[0.456s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.dsv'
[0.456s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.sh'
[0.457s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.bash'
[0.457s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.zsh'
[0.457s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/rm_vision/share/colcon-core/packages/rm_vision)
[0.459s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/robot_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/robot_description -- -j8 -l8
[0.459s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/robot_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/robot_description
[0.460s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/mindvision_camera' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/mindvision_camera -- -j8 -l8
[0.461s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/mindvision_camera': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/mindvision_camera
[0.464s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/engineer_vision_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/engineer_vision_pkg -- -j8 -l8
[0.464s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/engineer_vision_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/engineer_vision_pkg
[0.466s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(robot_description)
[0.467s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description' for CMake module files
[0.467s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/robot_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/robot_description
[0.467s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description' for CMake config files
[0.470s] Level 1:colcon.colcon_core.shell:create_environment_hook('robot_description', 'cmake_prefix_path')
[0.470s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/hook/cmake_prefix_path.ps1'
[0.470s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/hook/cmake_prefix_path.dsv'
[0.471s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/hook/cmake_prefix_path.sh'
[0.471s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description/bin'
[0.471s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description/lib/pkgconfig/robot_description.pc'
[0.471s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description/lib/python3.10/site-packages'
[0.471s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description/bin'
[0.472s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.ps1'
[0.472s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.dsv'
[0.472s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.sh'
[0.472s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.bash'
[0.473s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.zsh'
[0.473s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/robot_description/share/colcon-core/packages/robot_description)
[0.473s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(robot_description)
[0.473s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description' for CMake module files
[0.473s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description' for CMake config files
[0.473s] Level 1:colcon.colcon_core.shell:create_environment_hook('robot_description', 'cmake_prefix_path')
[0.473s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/hook/cmake_prefix_path.ps1'
[0.474s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/hook/cmake_prefix_path.dsv'
[0.474s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/hook/cmake_prefix_path.sh'
[0.474s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description/bin'
[0.474s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description/lib/pkgconfig/robot_description.pc'
[0.474s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description/lib/python3.10/site-packages'
[0.475s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description/bin'
[0.475s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.ps1'
[0.475s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.dsv'
[0.475s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.sh'
[0.476s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.bash'
[0.476s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.zsh'
[0.476s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/robot_description/share/colcon-core/packages/robot_description)
[0.478s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(mindvision_camera)
[0.478s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera' for CMake module files
[0.478s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera' for CMake config files
[0.479s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/mindvision_camera' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/mindvision_camera
[0.479s] Level 1:colcon.colcon_core.shell:create_environment_hook('mindvision_camera', 'cmake_prefix_path')
[0.479s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/cmake_prefix_path.ps1'
[0.479s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/cmake_prefix_path.dsv'
[0.480s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/cmake_prefix_path.sh'
[0.480s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/lib'
[0.480s] Level 1:colcon.colcon_core.shell:create_environment_hook('mindvision_camera', 'ld_library_path_lib')
[0.480s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/ld_library_path_lib.ps1'
[0.481s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/ld_library_path_lib.dsv'
[0.481s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/ld_library_path_lib.sh'
[0.481s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/bin'
[0.481s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/lib/pkgconfig/mindvision_camera.pc'
[0.481s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/lib/python3.10/site-packages'
[0.482s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/bin'
[0.482s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.ps1'
[0.483s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.dsv'
[0.483s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.sh'
[0.483s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.bash'
[0.483s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.zsh'
[0.484s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/mindvision_camera/share/colcon-core/packages/mindvision_camera)
[0.484s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(mindvision_camera)
[0.484s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera' for CMake module files
[0.484s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera' for CMake config files
[0.485s] Level 1:colcon.colcon_core.shell:create_environment_hook('mindvision_camera', 'cmake_prefix_path')
[0.485s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/cmake_prefix_path.ps1'
[0.485s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/cmake_prefix_path.dsv'
[0.485s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/cmake_prefix_path.sh'
[0.486s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/lib'
[0.486s] Level 1:colcon.colcon_core.shell:create_environment_hook('mindvision_camera', 'ld_library_path_lib')
[0.486s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/ld_library_path_lib.ps1'
[0.486s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/ld_library_path_lib.dsv'
[0.487s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/ld_library_path_lib.sh'
[0.487s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/bin'
[0.487s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/lib/pkgconfig/mindvision_camera.pc'
[0.487s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/lib/python3.10/site-packages'
[0.487s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/bin'
[0.488s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.ps1'
[0.488s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.dsv'
[0.489s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.sh'
[0.489s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.bash'
[0.489s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.zsh'
[0.489s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/mindvision_camera/share/colcon-core/packages/mindvision_camera)
[0.490s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(engineer_vision_pkg)
[0.491s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg' for CMake module files
[0.491s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg' for CMake config files
[0.492s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/engineer_vision_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/engineer_vision_pkg
[0.492s] Level 1:colcon.colcon_core.shell:create_environment_hook('engineer_vision_pkg', 'cmake_prefix_path')
[0.492s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/hook/cmake_prefix_path.ps1'
[0.492s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/hook/cmake_prefix_path.dsv'
[0.493s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/hook/cmake_prefix_path.sh'
[0.494s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib'
[0.494s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/bin'
[0.494s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/pkgconfig/engineer_vision_pkg.pc'
[0.494s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/python3.10/site-packages'
[0.495s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/bin'
[0.495s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.ps1'
[0.495s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.dsv'
[0.496s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.sh'
[0.496s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.bash'
[0.496s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.zsh'
[0.498s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/colcon-core/packages/engineer_vision_pkg)
[0.498s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(engineer_vision_pkg)
[0.498s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg' for CMake module files
[0.499s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg' for CMake config files
[0.499s] Level 1:colcon.colcon_core.shell:create_environment_hook('engineer_vision_pkg', 'cmake_prefix_path')
[0.499s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/hook/cmake_prefix_path.ps1'
[0.500s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/hook/cmake_prefix_path.dsv'
[0.500s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/hook/cmake_prefix_path.sh'
[0.501s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib'
[0.501s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/bin'
[0.501s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/pkgconfig/engineer_vision_pkg.pc'
[0.501s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/python3.10/site-packages'
[0.501s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/bin'
[0.502s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.ps1'
[0.502s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.dsv'
[0.502s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.sh'
[0.503s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.bash'
[0.504s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.zsh'
[0.505s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/colcon-core/packages/engineer_vision_pkg)
[10.280s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/arm_controller_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/arm_controller_pkg -- -j8 -l8
[10.281s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/arm_controller_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/arm_controller_pkg
[10.295s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(arm_controller_pkg)
[10.298s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg' for CMake module files
[10.299s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/arm_controller_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/arm_controller_pkg
[10.300s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg' for CMake config files
[10.301s] Level 1:colcon.colcon_core.shell:create_environment_hook('arm_controller_pkg', 'cmake_prefix_path')
[10.301s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/hook/cmake_prefix_path.ps1'
[10.303s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/hook/cmake_prefix_path.dsv'
[10.304s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/hook/cmake_prefix_path.sh'
[10.305s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/lib'
[10.305s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/bin'
[10.305s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/lib/pkgconfig/arm_controller_pkg.pc'
[10.306s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/lib/python3.10/site-packages'
[10.306s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/bin'
[10.306s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.ps1'
[10.307s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.dsv'
[10.307s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.sh'
[10.308s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.bash'
[10.308s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.zsh'
[10.309s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/colcon-core/packages/arm_controller_pkg)
[10.309s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(arm_controller_pkg)
[10.309s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg' for CMake module files
[10.310s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg' for CMake config files
[10.310s] Level 1:colcon.colcon_core.shell:create_environment_hook('arm_controller_pkg', 'cmake_prefix_path')
[10.310s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/hook/cmake_prefix_path.ps1'
[10.310s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/hook/cmake_prefix_path.dsv'
[10.311s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/hook/cmake_prefix_path.sh'
[10.311s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/lib'
[10.311s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/bin'
[10.311s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/lib/pkgconfig/arm_controller_pkg.pc'
[10.311s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/lib/python3.10/site-packages'
[10.311s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/bin'
[10.312s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.ps1'
[10.312s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.dsv'
[10.313s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.sh'
[10.313s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.bash'
[10.313s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.zsh'
[10.314s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/colcon-core/packages/arm_controller_pkg)
[10.315s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[10.315s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[10.315s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[10.315s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[10.323s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[10.323s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[10.323s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[10.423s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[10.424s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Code/ws_0/install/local_setup.ps1'
[10.425s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/Code/ws_0/install/_local_setup_util_ps1.py'
[10.430s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Code/ws_0/install/setup.ps1'
[10.435s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Code/ws_0/install/local_setup.sh'
[10.436s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/Code/ws_0/install/_local_setup_util_sh.py'
[10.437s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Code/ws_0/install/setup.sh'
[10.441s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Code/ws_0/install/local_setup.bash'
[10.442s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Code/ws_0/install/setup.bash'
[10.444s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Code/ws_0/install/local_setup.zsh'
[10.444s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Code/ws_0/install/setup.zsh'
