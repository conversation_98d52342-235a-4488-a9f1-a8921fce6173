[0.162s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.162s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=8, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0xffffa784eb60>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0xffffa784e650>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0xffffa784e650>>, mixin_verb=('build',))
[0.457s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.457s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.457s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.457s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.457s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.457s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.457s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/Code/ws_0'
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.458s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.471s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.471s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.471s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.471s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extensions ['ignore', 'ignore_ament_install']
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extension 'ignore'
[0.472s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extension 'ignore_ament_install'
[0.473s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extensions ['colcon_pkg']
[0.473s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extension 'colcon_pkg'
[0.473s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extensions ['colcon_meta']
[0.473s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extension 'colcon_meta'
[0.473s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extensions ['ros']
[0.473s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extension 'ros'
[0.475s] DEBUG:colcon.colcon_core.package_identification:Package 'src/arm_controller_pkg' with type 'ros.ament_cmake' and name 'arm_controller_pkg'
[0.476s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extensions ['ignore', 'ignore_ament_install']
[0.476s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extension 'ignore'
[0.476s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extension 'ignore_ament_install'
[0.476s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extensions ['colcon_pkg']
[0.476s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extension 'colcon_pkg'
[0.476s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extensions ['colcon_meta']
[0.476s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extension 'colcon_meta'
[0.476s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extensions ['ros']
[0.476s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extension 'ros'
[0.478s] DEBUG:colcon.colcon_core.package_identification:Package 'src/engineer_vision_pkg' with type 'ros.ament_cmake' and name 'engineer_vision_pkg'
[0.478s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extensions ['ignore', 'ignore_ament_install']
[0.478s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extension 'ignore'
[0.478s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extension 'ignore_ament_install'
[0.478s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extensions ['colcon_pkg']
[0.478s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extension 'colcon_pkg'
[0.478s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extensions ['colcon_meta']
[0.478s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extension 'colcon_meta'
[0.478s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extensions ['ros']
[0.478s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extension 'ros'
[0.479s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rm_vision' with type 'ros.ament_cmake' and name 'rm_vision'
[0.480s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extensions ['ignore', 'ignore_ament_install']
[0.480s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'ignore'
[0.481s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'ignore_ament_install'
[0.481s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extensions ['colcon_pkg']
[0.481s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'colcon_pkg'
[0.481s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extensions ['colcon_meta']
[0.481s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'colcon_meta'
[0.481s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extensions ['ros']
[0.481s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'ros'
[0.481s] DEBUG:colcon.colcon_core.package_identification:Package 'src/robot_description' with type 'ros.ament_cmake' and name 'robot_description'
[0.482s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extensions ['ignore', 'ignore_ament_install']
[0.482s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extension 'ignore'
[0.483s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extension 'ignore_ament_install'
[0.483s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extensions ['colcon_pkg']
[0.483s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extension 'colcon_pkg'
[0.483s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extensions ['colcon_meta']
[0.483s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extension 'colcon_meta'
[0.483s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extensions ['ros']
[0.483s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extension 'ros'
[0.485s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ros2_mindvision_camera' with type 'ros.ament_cmake' and name 'mindvision_camera'
[0.485s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.485s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.485s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.485s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.485s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.531s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.531s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.559s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 5 installed packages in /home/<USER>/Code/ws_0/install
[0.564s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 57 installed packages in /home/<USER>/moveit2_ws/install
[0.567s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 323 installed packages in /opt/ros/humble
[0.568s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.860s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'cmake_args' from command line to 'None'
[0.860s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'cmake_target' from command line to 'None'
[0.860s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.860s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'cmake_clean_cache' from command line to 'False'
[0.860s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'cmake_clean_first' from command line to 'False'
[0.860s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'cmake_force_configure' from command line to 'False'
[0.860s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'ament_cmake_args' from command line to 'None'
[0.861s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'catkin_cmake_args' from command line to 'None'
[0.861s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.861s] DEBUG:colcon.colcon_core.verb:Building package 'arm_controller_pkg' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Code/ws_0/build/arm_controller_pkg', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Code/ws_0/install/arm_controller_pkg', 'merge_install': False, 'path': '/home/<USER>/Code/ws_0/src/arm_controller_pkg', 'symlink_install': False, 'test_result_base': None}
[0.861s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'cmake_args' from command line to 'None'
[0.861s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'cmake_target' from command line to 'None'
[0.861s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.861s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'cmake_clean_cache' from command line to 'False'
[0.861s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'cmake_clean_first' from command line to 'False'
[0.861s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'cmake_force_configure' from command line to 'False'
[0.861s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'ament_cmake_args' from command line to 'None'
[0.861s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'catkin_cmake_args' from command line to 'None'
[0.861s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.861s] DEBUG:colcon.colcon_core.verb:Building package 'engineer_vision_pkg' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Code/ws_0/build/engineer_vision_pkg', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Code/ws_0/install/engineer_vision_pkg', 'merge_install': False, 'path': '/home/<USER>/Code/ws_0/src/engineer_vision_pkg', 'symlink_install': False, 'test_result_base': None}
[0.861s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'cmake_args' from command line to 'None'
[0.861s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'cmake_target' from command line to 'None'
[0.861s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.861s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'cmake_clean_cache' from command line to 'False'
[0.861s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'cmake_clean_first' from command line to 'False'
[0.861s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'cmake_force_configure' from command line to 'False'
[0.861s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'ament_cmake_args' from command line to 'None'
[0.861s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'catkin_cmake_args' from command line to 'None'
[0.861s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.861s] DEBUG:colcon.colcon_core.verb:Building package 'mindvision_camera' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Code/ws_0/build/mindvision_camera', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Code/ws_0/install/mindvision_camera', 'merge_install': False, 'path': '/home/<USER>/Code/ws_0/src/ros2_mindvision_camera', 'symlink_install': False, 'test_result_base': None}
[0.862s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'cmake_args' from command line to 'None'
[0.862s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'cmake_target' from command line to 'None'
[0.862s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.862s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'cmake_clean_cache' from command line to 'False'
[0.862s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'cmake_clean_first' from command line to 'False'
[0.862s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'cmake_force_configure' from command line to 'False'
[0.862s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'ament_cmake_args' from command line to 'None'
[0.862s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'catkin_cmake_args' from command line to 'None'
[0.862s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.862s] DEBUG:colcon.colcon_core.verb:Building package 'rm_vision' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Code/ws_0/build/rm_vision', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Code/ws_0/install/rm_vision', 'merge_install': False, 'path': '/home/<USER>/Code/ws_0/src/rm_vision', 'symlink_install': False, 'test_result_base': None}
[0.862s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_args' from command line to 'None'
[0.862s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_target' from command line to 'None'
[0.862s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.862s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_clean_cache' from command line to 'False'
[0.862s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_clean_first' from command line to 'False'
[0.862s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_force_configure' from command line to 'False'
[0.862s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'ament_cmake_args' from command line to 'None'
[0.862s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'catkin_cmake_args' from command line to 'None'
[0.862s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.862s] DEBUG:colcon.colcon_core.verb:Building package 'robot_description' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Code/ws_0/build/robot_description', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Code/ws_0/install/robot_description', 'merge_install': False, 'path': '/home/<USER>/Code/ws_0/src/robot_description', 'symlink_install': False, 'test_result_base': None}
[0.862s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.866s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.866s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/Code/ws_0/src/arm_controller_pkg' with build type 'ament_cmake'
[0.866s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Code/ws_0/src/arm_controller_pkg'
[0.872s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.872s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.872s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.884s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/Code/ws_0/src/engineer_vision_pkg' with build type 'ament_cmake'
[0.884s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Code/ws_0/src/engineer_vision_pkg'
[0.885s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.886s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.899s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/Code/ws_0/src/ros2_mindvision_camera' with build type 'ament_cmake'
[0.900s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Code/ws_0/src/ros2_mindvision_camera'
[0.900s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.900s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.909s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/Code/ws_0/src/rm_vision' with build type 'ament_cmake'
[0.910s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Code/ws_0/src/rm_vision'
[0.910s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.910s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.923s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/Code/ws_0/src/robot_description' with build type 'ament_cmake'
[0.924s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Code/ws_0/src/robot_description'
[0.925s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.925s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.963s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/arm_controller_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/arm_controller_pkg -- -j8 -l8
[0.970s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/engineer_vision_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/engineer_vision_pkg -- -j8 -l8
[0.976s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/mindvision_camera': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/mindvision_camera -- -j8 -l8
[0.986s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/rm_vision': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/rm_vision -- -j8 -l8
[1.002s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/robot_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/robot_description -- -j8 -l8
[1.065s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/robot_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/robot_description -- -j8 -l8
[1.066s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/robot_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/robot_description
[1.089s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/rm_vision' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/rm_vision -- -j8 -l8
[1.089s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/rm_vision': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/rm_vision
[1.108s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rm_vision)
[1.111s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision' for CMake module files
[1.111s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision' for CMake config files
[1.111s] Level 1:colcon.colcon_core.shell:create_environment_hook('rm_vision', 'cmake_prefix_path')
[1.112s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/hook/cmake_prefix_path.ps1'
[1.121s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/rm_vision' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/rm_vision
[1.122s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/hook/cmake_prefix_path.dsv'
[1.122s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/hook/cmake_prefix_path.sh'
[1.123s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision/bin'
[1.123s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision/lib/pkgconfig/rm_vision.pc'
[1.124s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision/lib/python3.10/site-packages'
[1.124s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision/bin'
[1.125s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.ps1'
[1.126s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.dsv'
[1.127s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.sh'
[1.130s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.bash'
[1.133s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.zsh'
[1.134s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/rm_vision/share/colcon-core/packages/rm_vision)
[1.134s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rm_vision)
[1.135s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision' for CMake module files
[1.135s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision' for CMake config files
[1.136s] Level 1:colcon.colcon_core.shell:create_environment_hook('rm_vision', 'cmake_prefix_path')
[1.136s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/hook/cmake_prefix_path.ps1'
[1.136s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/hook/cmake_prefix_path.dsv'
[1.137s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/hook/cmake_prefix_path.sh'
[1.137s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision/bin'
[1.137s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision/lib/pkgconfig/rm_vision.pc'
[1.137s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision/lib/python3.10/site-packages'
[1.137s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision/bin'
[1.138s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.ps1'
[1.142s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.dsv'
[1.143s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.sh'
[1.143s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.bash'
[1.143s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.zsh'
[1.143s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/rm_vision/share/colcon-core/packages/rm_vision)
[1.144s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(robot_description)
[1.144s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description' for CMake module files
[1.144s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description' for CMake config files
[1.144s] Level 1:colcon.colcon_core.shell:create_environment_hook('robot_description', 'cmake_prefix_path')
[1.144s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/hook/cmake_prefix_path.ps1'
[1.145s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/hook/cmake_prefix_path.dsv'
[1.145s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/hook/cmake_prefix_path.sh'
[1.145s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description/bin'
[1.145s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description/lib/pkgconfig/robot_description.pc'
[1.146s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description/lib/python3.10/site-packages'
[1.149s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description/bin'
[1.150s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/robot_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/robot_description
[1.150s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.ps1'
[1.154s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.dsv'
[1.155s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.sh'
[1.155s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.bash'
[1.155s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.zsh'
[1.156s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/robot_description/share/colcon-core/packages/robot_description)
[1.157s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(robot_description)
[1.157s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description' for CMake module files
[1.157s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description' for CMake config files
[1.157s] Level 1:colcon.colcon_core.shell:create_environment_hook('robot_description', 'cmake_prefix_path')
[1.157s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/hook/cmake_prefix_path.ps1'
[1.157s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/hook/cmake_prefix_path.dsv'
[1.158s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/hook/cmake_prefix_path.sh'
[1.158s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description/bin'
[1.158s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description/lib/pkgconfig/robot_description.pc'
[1.158s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description/lib/python3.10/site-packages'
[1.158s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description/bin'
[1.159s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.ps1'
[1.159s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.dsv'
[1.159s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.sh'
[1.160s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.bash'
[1.160s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.zsh'
[1.160s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/robot_description/share/colcon-core/packages/robot_description)
[1.161s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/mindvision_camera' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/mindvision_camera -- -j8 -l8
[1.163s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/mindvision_camera': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/mindvision_camera
[1.164s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/arm_controller_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/arm_controller_pkg -- -j8 -l8
[1.166s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/arm_controller_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/arm_controller_pkg
[1.172s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(mindvision_camera)
[1.172s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/mindvision_camera' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/mindvision_camera
[1.172s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera' for CMake module files
[1.175s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera' for CMake config files
[1.175s] Level 1:colcon.colcon_core.shell:create_environment_hook('mindvision_camera', 'cmake_prefix_path')
[1.175s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/cmake_prefix_path.ps1'
[1.176s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/cmake_prefix_path.dsv'
[1.177s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/cmake_prefix_path.sh'
[1.177s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/lib'
[1.177s] Level 1:colcon.colcon_core.shell:create_environment_hook('mindvision_camera', 'ld_library_path_lib')
[1.177s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/ld_library_path_lib.ps1'
[1.178s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/ld_library_path_lib.dsv'
[1.178s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/ld_library_path_lib.sh'
[1.179s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/bin'
[1.179s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/lib/pkgconfig/mindvision_camera.pc'
[1.180s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/lib/python3.10/site-packages'
[1.180s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/bin'
[1.181s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.ps1'
[1.181s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.dsv'
[1.181s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.sh'
[1.182s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.bash'
[1.182s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.zsh'
[1.182s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/mindvision_camera/share/colcon-core/packages/mindvision_camera)
[1.183s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(mindvision_camera)
[1.183s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera' for CMake module files
[1.183s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera' for CMake config files
[1.183s] Level 1:colcon.colcon_core.shell:create_environment_hook('mindvision_camera', 'cmake_prefix_path')
[1.184s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/cmake_prefix_path.ps1'
[1.184s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/cmake_prefix_path.dsv'
[1.185s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/cmake_prefix_path.sh'
[1.186s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/lib'
[1.186s] Level 1:colcon.colcon_core.shell:create_environment_hook('mindvision_camera', 'ld_library_path_lib')
[1.186s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/ld_library_path_lib.ps1'
[1.187s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/ld_library_path_lib.dsv'
[1.187s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/ld_library_path_lib.sh'
[1.187s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/bin'
[1.188s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/lib/pkgconfig/mindvision_camera.pc'
[1.188s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/lib/python3.10/site-packages'
[1.188s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/bin'
[1.188s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.ps1'
[1.189s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.dsv'
[1.189s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.sh'
[1.190s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.bash'
[1.190s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.zsh'
[1.191s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/mindvision_camera/share/colcon-core/packages/mindvision_camera)
[1.196s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(arm_controller_pkg)
[1.196s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg' for CMake module files
[1.197s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg' for CMake config files
[1.198s] Level 1:colcon.colcon_core.shell:create_environment_hook('arm_controller_pkg', 'cmake_prefix_path')
[1.198s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/hook/cmake_prefix_path.ps1'
[1.199s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/arm_controller_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/arm_controller_pkg
[1.199s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/hook/cmake_prefix_path.dsv'
[1.200s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/hook/cmake_prefix_path.sh'
[1.200s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/lib'
[1.201s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/bin'
[1.201s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/lib/pkgconfig/arm_controller_pkg.pc'
[1.201s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/lib/python3.10/site-packages'
[1.201s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/bin'
[1.201s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.ps1'
[1.202s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.dsv'
[1.202s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.sh'
[1.203s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.bash'
[1.203s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.zsh'
[1.204s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/colcon-core/packages/arm_controller_pkg)
[1.204s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(arm_controller_pkg)
[1.204s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg' for CMake module files
[1.205s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg' for CMake config files
[1.205s] Level 1:colcon.colcon_core.shell:create_environment_hook('arm_controller_pkg', 'cmake_prefix_path')
[1.205s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/hook/cmake_prefix_path.ps1'
[1.206s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/hook/cmake_prefix_path.dsv'
[1.206s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/hook/cmake_prefix_path.sh'
[1.207s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/lib'
[1.207s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/bin'
[1.207s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/lib/pkgconfig/arm_controller_pkg.pc'
[1.207s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/lib/python3.10/site-packages'
[1.207s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/bin'
[1.207s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.ps1'
[1.208s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.dsv'
[1.209s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.sh'
[1.209s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.bash'
[1.209s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.zsh'
[1.209s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/colcon-core/packages/arm_controller_pkg)
[16.982s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/engineer_vision_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/engineer_vision_pkg -- -j8 -l8
[16.983s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/engineer_vision_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/engineer_vision_pkg
[17.012s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(engineer_vision_pkg)
[17.013s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg' for CMake module files
[17.013s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg' for CMake config files
[17.013s] Level 1:colcon.colcon_core.shell:create_environment_hook('engineer_vision_pkg', 'cmake_prefix_path')
[17.013s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/hook/cmake_prefix_path.ps1'
[17.015s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/engineer_vision_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/engineer_vision_pkg
[17.015s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/hook/cmake_prefix_path.dsv'
[17.015s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/hook/cmake_prefix_path.sh'
[17.016s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib'
[17.016s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/bin'
[17.016s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/pkgconfig/engineer_vision_pkg.pc'
[17.017s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/python3.10/site-packages'
[17.017s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/bin'
[17.017s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.ps1'
[17.018s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.dsv'
[17.019s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.sh'
[17.020s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.bash'
[17.020s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.zsh'
[17.021s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/colcon-core/packages/engineer_vision_pkg)
[17.022s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(engineer_vision_pkg)
[17.022s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg' for CMake module files
[17.022s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg' for CMake config files
[17.022s] Level 1:colcon.colcon_core.shell:create_environment_hook('engineer_vision_pkg', 'cmake_prefix_path')
[17.022s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/hook/cmake_prefix_path.ps1'
[17.023s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/hook/cmake_prefix_path.dsv'
[17.024s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/hook/cmake_prefix_path.sh'
[17.024s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib'
[17.024s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/bin'
[17.024s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/pkgconfig/engineer_vision_pkg.pc'
[17.025s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/python3.10/site-packages'
[17.025s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/bin'
[17.025s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.ps1'
[17.025s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.dsv'
[17.026s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.sh'
[17.027s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.bash'
[17.027s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.zsh'
[17.027s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/colcon-core/packages/engineer_vision_pkg)
[17.029s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[17.030s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[17.030s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[17.030s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[17.045s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[17.046s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[17.046s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[17.314s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[17.314s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Code/ws_0/install/local_setup.ps1'
[17.317s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/Code/ws_0/install/_local_setup_util_ps1.py'
[17.326s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Code/ws_0/install/setup.ps1'
[17.354s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Code/ws_0/install/local_setup.sh'
[17.362s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/Code/ws_0/install/_local_setup_util_sh.py'
[17.362s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Code/ws_0/install/setup.sh'
[17.376s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Code/ws_0/install/local_setup.bash'
[17.377s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Code/ws_0/install/setup.bash'
[17.380s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Code/ws_0/install/local_setup.zsh'
[17.381s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Code/ws_0/install/setup.zsh'
