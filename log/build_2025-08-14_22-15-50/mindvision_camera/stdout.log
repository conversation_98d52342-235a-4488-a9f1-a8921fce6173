[ 50%] Built target mindvision_camera
[100%] Built target mindvision_camera_node
-- Install configuration: ""
-- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/lib/mindvision_camera/mindvision_camera_node
-- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/environment/library_path.sh
-- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/environment/library_path.dsv
-- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/lib/libmindvision_camera.so
-- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/config
-- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/config/camera_info.yaml
-- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/config/camera_params.yaml
-- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/launch
-- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/launch/mv_launch.py
-- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/ament_index/resource_index/package_run_dependencies/mindvision_camera
-- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/ament_index/resource_index/parent_prefix_path/mindvision_camera
-- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/environment/ament_prefix_path.sh
-- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/environment/ament_prefix_path.dsv
-- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/environment/path.sh
-- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/environment/path.dsv
-- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/local_setup.bash
-- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/local_setup.sh
-- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/local_setup.zsh
-- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/local_setup.dsv
-- Installing: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.dsv
-- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/ament_index/resource_index/packages/mindvision_camera
-- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/ament_index/resource_index/rclcpp_components/mindvision_camera
-- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/cmake/ament_cmake_export_dependencies-extras.cmake
-- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/cmake/ament_cmake_export_libraries-extras.cmake
-- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/cmake/mindvision_cameraConfig.cmake
-- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/cmake/mindvision_cameraConfig-version.cmake
-- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.xml
