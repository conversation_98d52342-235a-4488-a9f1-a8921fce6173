[0.000000] (-) TimerEvent: {}
[0.001023] (-) JobUnselected: {'identifier': 'engineer_vision_pkg'}
[0.001074] (-) JobUnselected: {'identifier': 'mindvision_camera'}
[0.001091] (-) JobUnselected: {'identifier': 'rm_vision'}
[0.001104] (-) JobUnselected: {'identifier': 'robot_description'}
[0.001115] (arm_controller_pkg) JobQueued: {'identifier': 'arm_controller_pkg', 'dependencies': OrderedDict()}
[0.001241] (arm_controller_pkg) JobStarted: {'identifier': 'arm_controller_pkg'}
[0.019264] (arm_controller_pkg) JobProgress: {'identifier': 'arm_controller_pkg', 'progress': 'cmake'}
[0.021937] (arm_controller_pkg) JobProgress: {'identifier': 'arm_controller_pkg', 'progress': 'build'}
[0.022777] (arm_controller_pkg) Command: {'cmd': ['/usr/local/cmake/bin/cmake', '--build', '/home/<USER>/Code/ws_0/build/arm_controller_pkg', '--', '-j8', '-l8'], 'cwd': '/home/<USER>/Code/ws_0/build/arm_controller_pkg', 'env': OrderedDict([('USER', 'mac'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner_testutils/lib:/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner/lib:/home/<USER>/moveit2_ws/install/moveit_visual_tools/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_visualization/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_demo/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_capabilities/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_core/lib:/home/<USER>/moveit2_ws/install/moveit_ros_control_interface/lib:/home/<USER>/moveit2_ws/install/moveit_simple_controller_manager/lib:/home/<USER>/moveit2_ws/install/moveit_setup_assistant/lib:/home/<USER>/moveit2_ws/install/moveit_setup_srdf_plugins/lib:/home/<USER>/moveit2_ws/install/moveit_setup_core_plugins/lib:/home/<USER>/moveit2_ws/install/moveit_setup_controllers/lib:/home/<USER>/moveit2_ws/install/moveit_setup_app_plugins/lib:/home/<USER>/moveit2_ws/install/moveit_setup_framework/lib:/home/<USER>/moveit2_ws/install/moveit_servo/lib:/home/<USER>/moveit2_ws/install/moveit_ros_visualization/lib:/home/<USER>/moveit2_ws/install/moveit_hybrid_planning/lib:/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/lib:/home/<USER>/moveit2_ws/install/moveit_ros_benchmarks/lib:/home/<USER>/moveit2_ws/install/moveit_ros_warehouse/lib:/home/<USER>/moveit2_ws/install/moveit_ros_robot_interaction/lib:/home/<USER>/moveit2_ws/install/moveit_ros_perception/lib:/home/<USER>/moveit2_ws/install/moveit_ros_move_group/lib:/home/<USER>/moveit2_ws/install/moveit_planners_ompl/lib:/home/<USER>/moveit2_ws/install/moveit_ros_planning/lib:/home/<USER>/moveit2_ws/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/moveit2_ws/install/moveit_planners_chomp/lib:/home/<USER>/moveit2_ws/install/moveit_kinematics/lib:/home/<USER>/moveit2_ws/install/moveit_chomp_optimizer_adapter/lib:/home/<USER>/moveit2_ws/install/chomp_motion_planner/lib:/home/<USER>/moveit2_ws/install/moveit_core/lib:/home/<USER>/moveit2_ws/install/srdfdom/lib:/home/<USER>/moveit2_ws/install/rviz_marker_tools/lib:/home/<USER>/moveit2_ws/install/rosparam_shortcuts/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_msgs/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/bin/helpers/browser.sh'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/Code/ws_0'), ('TERM_PROGRAM_VERSION', '1.103.1'), ('VSCODE_IPC_HOOK_CLI', '/run/user/502/vscode-ipc-89356d9e-2569-497e-8a14-9200925310d6.sock'), ('ROS_PYTHON_VERSION', '3'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/502/bus'), ('COLORTERM', 'truecolor'), ('COLCON_PREFIX_PATH', '/home/<USER>/moveit2_ws/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mac'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/home/<USER>/moveit2_ws/install/moveit_core/bin:/opt/ros/humble/bin:/usr/local/cmake/bin:/opt/orbstack-guest/bin-hiprio:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/opt/orbstack-guest/bin:/opt/orbstack-guest/data/bin/cmdlinks'), ('PYTHON_EXECUTABLE', '/usr/bin/python3.10'), ('XDG_RUNTIME_DIR', '/run/user/502'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-ec2f90e73ca2d53c.txt'), ('LANG', 'C.UTF-8'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/502/vscode-git-4bcffef955.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AUTH_SOCK', '/run/user/502/vscode-ssh-auth-sock-589875342'), ('AMENT_PREFIX_PATH', '/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner_testutils:/home/<USER>/moveit2_ws/install/moveit_runtime:/home/<USER>/moveit2_ws/install/moveit2_tutorials:/home/<USER>/moveit2_ws/install/moveit:/home/<USER>/moveit2_ws/install/moveit_planners:/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner:/home/<USER>/moveit2_ws/install/moveit_test:/home/<USER>/moveit2_ws/install/moveit_visual_tools:/home/<USER>/moveit2_ws/install/moveit_task_constructor_visualization:/home/<USER>/moveit2_ws/install/moveit_task_constructor_demo:/home/<USER>/moveit2_ws/install/moveit_task_constructor_capabilities:/home/<USER>/moveit2_ws/install/moveit_task_constructor_core:/home/<USER>/moveit2_ws/install/moveit_ros_control_interface:/home/<USER>/moveit2_ws/install/moveit_plugins:/home/<USER>/moveit2_ws/install/moveit_simple_controller_manager:/home/<USER>/moveit2_ws/install/moveit_setup_assistant:/home/<USER>/moveit2_ws/install/moveit_setup_srdf_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_core_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_controllers:/home/<USER>/moveit2_ws/install/moveit_setup_app_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_framework:/home/<USER>/moveit2_ws/install/moveit_servo:/home/<USER>/moveit2_ws/install/moveit_ros:/home/<USER>/moveit2_ws/install/moveit_ros_visualization:/home/<USER>/moveit2_ws/install/moveit_hybrid_planning:/home/<USER>/moveit2_ws/install/move_program:/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface:/home/<USER>/moveit2_ws/install/moveit_ros_benchmarks:/home/<USER>/moveit2_ws/install/moveit_ros_warehouse:/home/<USER>/moveit2_ws/install/moveit_ros_robot_interaction:/home/<USER>/moveit2_ws/install/moveit_ros_perception:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_pg70_support:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_moveit_config:/home/<USER>/moveit2_ws/install/moveit_ros_move_group:/home/<USER>/moveit2_ws/install/moveit_planners_ompl:/home/<USER>/moveit2_ws/install/moveit_ros_planning:/home/<USER>/moveit2_ws/install/moveit_ros_occupancy_map_monitor:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/moveit2_ws/install/moveit_planners_chomp:/home/<USER>/moveit2_ws/install/moveit_kinematics:/home/<USER>/moveit2_ws/install/moveit_chomp_optimizer_adapter:/home/<USER>/moveit2_ws/install/chomp_motion_planner:/home/<USER>/moveit2_ws/install/moveit_core:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/srdfdom:/home/<USER>/moveit2_ws/install/rviz_marker_tools:/home/<USER>/moveit2_ws/install/rosparam_shortcuts:/home/<USER>/moveit2_ws/install/moveit_task_constructor_msgs:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_support:/home/<USER>/moveit2_ws/install/moveit_resources:/home/<USER>/moveit2_ws/install/moveit_resources_pr2_description:/home/<USER>/moveit2_ws/install/moveit_resources_panda_moveit_config:/home/<USER>/moveit2_ws/install/moveit_resources_panda_description:/home/<USER>/moveit2_ws/install/moveit_resources_fanuc_moveit_config:/home/<USER>/moveit2_ws/install/moveit_resources_fanuc_description:/home/<USER>/moveit2_ws/install/moveit_common:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('CMAKE_ARGS', '-DPYTHON_EXECUTABLE=/usr/bin/python3.10 -DPython3_EXECUTABLE=/usr/bin/python3.10'), ('CMAKE_MODULE_PATH', '/home/<USER>/moveit2_ws/install/moveit_task_constructor_core/share/moveit_task_constructor_core/cmake'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('PWD', '/home/<USER>/Code/ws_0/build/arm_controller_pkg'), ('SSH_CONNECTION', '::1 0 ::1 22'), ('XDG_DATA_DIRS', '/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/moveit2_ws/install/moveit_task_constructor_core/local/lib/python3.10/dist-packages:/home/<USER>/moveit2_ws/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/moveit2_ws/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/moveit2_ws/install/moveit_task_constructor_msgs/local/lib/python3.10/dist-packages:/home/<USER>/moveit2_ws/install/launch_param_builder/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('Python3_EXECUTABLE', '/usr/bin/python3.10'), ('CMAKE_PREFIX_PATH', '/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner_testutils:/home/<USER>/moveit2_ws/install/moveit_runtime:/home/<USER>/moveit2_ws/install/moveit2_tutorials:/home/<USER>/moveit2_ws/install/moveit:/home/<USER>/moveit2_ws/install/moveit_planners:/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner:/home/<USER>/moveit2_ws/install/moveit_test:/home/<USER>/moveit2_ws/install/moveit_visual_tools:/home/<USER>/moveit2_ws/install/moveit_task_constructor_visualization:/home/<USER>/moveit2_ws/install/moveit_task_constructor_demo:/home/<USER>/moveit2_ws/install/moveit_task_constructor_capabilities:/home/<USER>/moveit2_ws/install/moveit_task_constructor_core:/home/<USER>/moveit2_ws/install/moveit_ros_control_interface:/home/<USER>/moveit2_ws/install/moveit_plugins:/home/<USER>/moveit2_ws/install/moveit_simple_controller_manager:/home/<USER>/moveit2_ws/install/moveit_setup_assistant:/home/<USER>/moveit2_ws/install/moveit_setup_srdf_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_core_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_controllers:/home/<USER>/moveit2_ws/install/moveit_setup_app_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_framework:/home/<USER>/moveit2_ws/install/moveit_servo:/home/<USER>/moveit2_ws/install/moveit_ros:/home/<USER>/moveit2_ws/install/moveit_ros_visualization:/home/<USER>/moveit2_ws/install/moveit_hybrid_planning:/home/<USER>/moveit2_ws/install/move_program:/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface:/home/<USER>/moveit2_ws/install/moveit_ros_benchmarks:/home/<USER>/moveit2_ws/install/moveit_ros_warehouse:/home/<USER>/moveit2_ws/install/moveit_ros_robot_interaction:/home/<USER>/moveit2_ws/install/moveit_ros_perception:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_pg70_support:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_moveit_config:/home/<USER>/moveit2_ws/install/moveit_ros_move_group:/home/<USER>/moveit2_ws/install/moveit_planners_ompl:/home/<USER>/moveit2_ws/install/moveit_ros_planning:/home/<USER>/moveit2_ws/install/moveit_ros_occupancy_map_monitor:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/moveit2_ws/install/moveit_planners_chomp:/home/<USER>/moveit2_ws/install/moveit_kinematics:/home/<USER>/moveit2_ws/install/moveit_chomp_optimizer_adapter:/home/<USER>/moveit2_ws/install/chomp_motion_planner:/home/<USER>/moveit2_ws/install/moveit_core:/home/<USER>/moveit2_ws/install/srdfdom:/home/<USER>/moveit2_ws/install/rviz_marker_tools:/home/<USER>/moveit2_ws/install/rosparam_shortcuts:/home/<USER>/moveit2_ws/install/moveit_task_constructor_msgs:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_support:/home/<USER>/moveit2_ws/install/moveit_resources:/home/<USER>/moveit2_ws/install/moveit_resources_pr2_description:/home/<USER>/moveit2_ws/install/moveit_resources_panda_moveit_config:/home/<USER>/moveit2_ws/install/moveit_resources_panda_description:/home/<USER>/moveit2_ws/install/moveit_resources_fanuc_moveit_config:/home/<USER>/moveit2_ws/install/moveit_resources_fanuc_description:/home/<USER>/moveit2_ws/install/moveit_common:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble')]), 'shell': False}
[0.100170] (-) TimerEvent: {}
[0.184533] (arm_controller_pkg) StdoutLine: {'line': b'[ 50%] Built target path_planning_node\n'}
[0.199940] (arm_controller_pkg) StdoutLine: {'line': b'[ 75%] \x1b[32mBuilding CXX object CMakeFiles/RB_builder_node.dir/src/RB_builder_node.cpp.o\x1b[0m\n'}
[0.200143] (-) TimerEvent: {}
[0.300566] (-) TimerEvent: {}
[0.438114] (-) TimerEvent: {}
[0.541465] (-) TimerEvent: {}
[0.641790] (-) TimerEvent: {}
[0.743573] (-) TimerEvent: {}
[0.844935] (-) TimerEvent: {}
[0.947302] (-) TimerEvent: {}
[1.048967] (-) TimerEvent: {}
[1.150439] (-) TimerEvent: {}
[1.251061] (-) TimerEvent: {}
[1.351945] (-) TimerEvent: {}
[1.452972] (-) TimerEvent: {}
[1.553618] (-) TimerEvent: {}
[1.655155] (-) TimerEvent: {}
[1.755486] (-) TimerEvent: {}
[1.859048] (-) TimerEvent: {}
[1.961866] (-) TimerEvent: {}
[2.063751] (-) TimerEvent: {}
[2.165124] (-) TimerEvent: {}
[2.265894] (-) TimerEvent: {}
[2.367186] (-) TimerEvent: {}
[2.468040] (-) TimerEvent: {}
[2.570250] (-) TimerEvent: {}
[2.680031] (-) TimerEvent: {}
[2.782010] (-) TimerEvent: {}
[2.882975] (-) TimerEvent: {}
[2.983682] (-) TimerEvent: {}
[3.087136] (-) TimerEvent: {}
[3.190932] (-) TimerEvent: {}
[3.291538] (-) TimerEvent: {}
[3.394095] (-) TimerEvent: {}
[3.496203] (-) TimerEvent: {}
[3.596511] (-) TimerEvent: {}
[3.697137] (-) TimerEvent: {}
[3.804274] (-) TimerEvent: {}
[3.908305] (-) TimerEvent: {}
[4.016800] (-) TimerEvent: {}
[4.312449] (-) TimerEvent: {}
[4.746124] (-) TimerEvent: {}
[4.849311] (-) TimerEvent: {}
[4.950197] (-) TimerEvent: {}
[5.051958] (-) TimerEvent: {}
[5.155062] (-) TimerEvent: {}
[5.257526] (-) TimerEvent: {}
[5.360129] (-) TimerEvent: {}
[5.463548] (-) TimerEvent: {}
[5.564289] (-) TimerEvent: {}
[5.665503] (-) TimerEvent: {}
[5.767102] (-) TimerEvent: {}
[5.869177] (-) TimerEvent: {}
[5.976540] (-) TimerEvent: {}
[6.077683] (-) TimerEvent: {}
[6.180788] (-) TimerEvent: {}
[6.282028] (-) TimerEvent: {}
[6.389753] (-) TimerEvent: {}
[6.491952] (-) TimerEvent: {}
[6.594157] (-) TimerEvent: {}
[6.695803] (-) TimerEvent: {}
[6.798291] (-) TimerEvent: {}
[6.899050] (-) TimerEvent: {}
[7.025690] (-) TimerEvent: {}
[7.130864] (-) TimerEvent: {}
[7.238985] (-) TimerEvent: {}
[7.339452] (-) TimerEvent: {}
[7.441362] (-) TimerEvent: {}
[7.548461] (-) TimerEvent: {}
[7.653766] (-) TimerEvent: {}
[7.754967] (-) TimerEvent: {}
[7.856451] (-) TimerEvent: {}
[7.958021] (-) TimerEvent: {}
[8.060203] (-) TimerEvent: {}
[8.161400] (-) TimerEvent: {}
[8.269959] (-) TimerEvent: {}
[8.371034] (-) TimerEvent: {}
[8.472460] (-) TimerEvent: {}
[8.574709] (-) TimerEvent: {}
[8.676576] (-) TimerEvent: {}
[8.778098] (-) TimerEvent: {}
[8.883826] (-) TimerEvent: {}
[8.987959] (-) TimerEvent: {}
[9.088779] (-) TimerEvent: {}
[9.190225] (-) TimerEvent: {}
[9.298934] (-) TimerEvent: {}
[9.399900] (-) TimerEvent: {}
[9.501673] (-) TimerEvent: {}
[9.604065] (-) TimerEvent: {}
[9.705574] (-) TimerEvent: {}
[9.806947] (-) TimerEvent: {}
[9.907951] (-) TimerEvent: {}
[10.009140] (-) TimerEvent: {}
[10.112988] (-) TimerEvent: {}
[10.214328] (-) TimerEvent: {}
[10.316067] (-) TimerEvent: {}
[10.417899] (-) TimerEvent: {}
[10.518346] (-) TimerEvent: {}
[10.620648] (-) TimerEvent: {}
[10.721783] (-) TimerEvent: {}
[10.824047] (-) TimerEvent: {}
[10.924345] (-) TimerEvent: {}
[11.026087] (-) TimerEvent: {}
[11.127451] (-) TimerEvent: {}
[11.229440] (-) TimerEvent: {}
[11.330208] (-) TimerEvent: {}
[11.434596] (-) TimerEvent: {}
[11.537463] (-) TimerEvent: {}
[11.639101] (-) TimerEvent: {}
[11.742395] (-) TimerEvent: {}
[11.842861] (-) TimerEvent: {}
[11.946537] (-) TimerEvent: {}
[12.048597] (-) TimerEvent: {}
[12.149891] (-) TimerEvent: {}
[12.251521] (-) TimerEvent: {}
[12.352311] (-) TimerEvent: {}
[12.452931] (-) TimerEvent: {}
[12.553349] (-) TimerEvent: {}
[12.654059] (-) TimerEvent: {}
[12.756903] (-) TimerEvent: {}
[12.859447] (-) TimerEvent: {}
[12.960937] (-) TimerEvent: {}
[13.061295] (-) TimerEvent: {}
[13.161704] (-) TimerEvent: {}
[13.262017] (-) TimerEvent: {}
[13.365266] (-) TimerEvent: {}
[13.465538] (-) TimerEvent: {}
[13.569773] (-) TimerEvent: {}
[13.670967] (-) TimerEvent: {}
[13.771543] (-) TimerEvent: {}
[13.873229] (-) TimerEvent: {}
[13.973559] (-) TimerEvent: {}
[14.077652] (-) TimerEvent: {}
[14.181958] (-) TimerEvent: {}
[14.282276] (-) TimerEvent: {}
[14.382721] (-) TimerEvent: {}
[14.484310] (-) TimerEvent: {}
[14.584927] (-) TimerEvent: {}
[14.685211] (-) TimerEvent: {}
[14.789916] (-) TimerEvent: {}
[14.890294] (-) TimerEvent: {}
[14.991788] (-) TimerEvent: {}
[15.092529] (-) TimerEvent: {}
[15.177376] (arm_controller_pkg) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX executable RB_builder_node\x1b[0m\n'}
[15.193426] (-) TimerEvent: {}
[15.294040] (-) TimerEvent: {}
[15.395099] (-) TimerEvent: {}
[15.499168] (-) TimerEvent: {}
[15.599685] (-) TimerEvent: {}
[15.700276] (-) TimerEvent: {}
[15.805852] (-) TimerEvent: {}
[15.909738] (-) TimerEvent: {}
[16.011379] (-) TimerEvent: {}
[16.112742] (-) TimerEvent: {}
[16.213462] (-) TimerEvent: {}
[16.321460] (-) TimerEvent: {}
[16.351423] (arm_controller_pkg) StdoutLine: {'line': b'[100%] Built target RB_builder_node\n'}
[16.370866] (arm_controller_pkg) CommandEnded: {'returncode': 0}
[16.379104] (arm_controller_pkg) JobProgress: {'identifier': 'arm_controller_pkg', 'progress': 'install'}
[16.385620] (arm_controller_pkg) Command: {'cmd': ['/usr/local/cmake/bin/cmake', '--install', '/home/<USER>/Code/ws_0/build/arm_controller_pkg'], 'cwd': '/home/<USER>/Code/ws_0/build/arm_controller_pkg', 'env': OrderedDict([('USER', 'mac'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner_testutils/lib:/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner/lib:/home/<USER>/moveit2_ws/install/moveit_visual_tools/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_visualization/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_demo/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_capabilities/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_core/lib:/home/<USER>/moveit2_ws/install/moveit_ros_control_interface/lib:/home/<USER>/moveit2_ws/install/moveit_simple_controller_manager/lib:/home/<USER>/moveit2_ws/install/moveit_setup_assistant/lib:/home/<USER>/moveit2_ws/install/moveit_setup_srdf_plugins/lib:/home/<USER>/moveit2_ws/install/moveit_setup_core_plugins/lib:/home/<USER>/moveit2_ws/install/moveit_setup_controllers/lib:/home/<USER>/moveit2_ws/install/moveit_setup_app_plugins/lib:/home/<USER>/moveit2_ws/install/moveit_setup_framework/lib:/home/<USER>/moveit2_ws/install/moveit_servo/lib:/home/<USER>/moveit2_ws/install/moveit_ros_visualization/lib:/home/<USER>/moveit2_ws/install/moveit_hybrid_planning/lib:/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/lib:/home/<USER>/moveit2_ws/install/moveit_ros_benchmarks/lib:/home/<USER>/moveit2_ws/install/moveit_ros_warehouse/lib:/home/<USER>/moveit2_ws/install/moveit_ros_robot_interaction/lib:/home/<USER>/moveit2_ws/install/moveit_ros_perception/lib:/home/<USER>/moveit2_ws/install/moveit_ros_move_group/lib:/home/<USER>/moveit2_ws/install/moveit_planners_ompl/lib:/home/<USER>/moveit2_ws/install/moveit_ros_planning/lib:/home/<USER>/moveit2_ws/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/moveit2_ws/install/moveit_planners_chomp/lib:/home/<USER>/moveit2_ws/install/moveit_kinematics/lib:/home/<USER>/moveit2_ws/install/moveit_chomp_optimizer_adapter/lib:/home/<USER>/moveit2_ws/install/chomp_motion_planner/lib:/home/<USER>/moveit2_ws/install/moveit_core/lib:/home/<USER>/moveit2_ws/install/srdfdom/lib:/home/<USER>/moveit2_ws/install/rviz_marker_tools/lib:/home/<USER>/moveit2_ws/install/rosparam_shortcuts/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_msgs/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/bin/helpers/browser.sh'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/Code/ws_0'), ('TERM_PROGRAM_VERSION', '1.103.1'), ('VSCODE_IPC_HOOK_CLI', '/run/user/502/vscode-ipc-89356d9e-2569-497e-8a14-9200925310d6.sock'), ('ROS_PYTHON_VERSION', '3'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/502/bus'), ('COLORTERM', 'truecolor'), ('COLCON_PREFIX_PATH', '/home/<USER>/moveit2_ws/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mac'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/home/<USER>/moveit2_ws/install/moveit_core/bin:/opt/ros/humble/bin:/usr/local/cmake/bin:/opt/orbstack-guest/bin-hiprio:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/opt/orbstack-guest/bin:/opt/orbstack-guest/data/bin/cmdlinks'), ('PYTHON_EXECUTABLE', '/usr/bin/python3.10'), ('XDG_RUNTIME_DIR', '/run/user/502'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-ec2f90e73ca2d53c.txt'), ('LANG', 'C.UTF-8'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/502/vscode-git-4bcffef955.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AUTH_SOCK', '/run/user/502/vscode-ssh-auth-sock-589875342'), ('AMENT_PREFIX_PATH', '/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner_testutils:/home/<USER>/moveit2_ws/install/moveit_runtime:/home/<USER>/moveit2_ws/install/moveit2_tutorials:/home/<USER>/moveit2_ws/install/moveit:/home/<USER>/moveit2_ws/install/moveit_planners:/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner:/home/<USER>/moveit2_ws/install/moveit_test:/home/<USER>/moveit2_ws/install/moveit_visual_tools:/home/<USER>/moveit2_ws/install/moveit_task_constructor_visualization:/home/<USER>/moveit2_ws/install/moveit_task_constructor_demo:/home/<USER>/moveit2_ws/install/moveit_task_constructor_capabilities:/home/<USER>/moveit2_ws/install/moveit_task_constructor_core:/home/<USER>/moveit2_ws/install/moveit_ros_control_interface:/home/<USER>/moveit2_ws/install/moveit_plugins:/home/<USER>/moveit2_ws/install/moveit_simple_controller_manager:/home/<USER>/moveit2_ws/install/moveit_setup_assistant:/home/<USER>/moveit2_ws/install/moveit_setup_srdf_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_core_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_controllers:/home/<USER>/moveit2_ws/install/moveit_setup_app_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_framework:/home/<USER>/moveit2_ws/install/moveit_servo:/home/<USER>/moveit2_ws/install/moveit_ros:/home/<USER>/moveit2_ws/install/moveit_ros_visualization:/home/<USER>/moveit2_ws/install/moveit_hybrid_planning:/home/<USER>/moveit2_ws/install/move_program:/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface:/home/<USER>/moveit2_ws/install/moveit_ros_benchmarks:/home/<USER>/moveit2_ws/install/moveit_ros_warehouse:/home/<USER>/moveit2_ws/install/moveit_ros_robot_interaction:/home/<USER>/moveit2_ws/install/moveit_ros_perception:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_pg70_support:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_moveit_config:/home/<USER>/moveit2_ws/install/moveit_ros_move_group:/home/<USER>/moveit2_ws/install/moveit_planners_ompl:/home/<USER>/moveit2_ws/install/moveit_ros_planning:/home/<USER>/moveit2_ws/install/moveit_ros_occupancy_map_monitor:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/moveit2_ws/install/moveit_planners_chomp:/home/<USER>/moveit2_ws/install/moveit_kinematics:/home/<USER>/moveit2_ws/install/moveit_chomp_optimizer_adapter:/home/<USER>/moveit2_ws/install/chomp_motion_planner:/home/<USER>/moveit2_ws/install/moveit_core:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/srdfdom:/home/<USER>/moveit2_ws/install/rviz_marker_tools:/home/<USER>/moveit2_ws/install/rosparam_shortcuts:/home/<USER>/moveit2_ws/install/moveit_task_constructor_msgs:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_support:/home/<USER>/moveit2_ws/install/moveit_resources:/home/<USER>/moveit2_ws/install/moveit_resources_pr2_description:/home/<USER>/moveit2_ws/install/moveit_resources_panda_moveit_config:/home/<USER>/moveit2_ws/install/moveit_resources_panda_description:/home/<USER>/moveit2_ws/install/moveit_resources_fanuc_moveit_config:/home/<USER>/moveit2_ws/install/moveit_resources_fanuc_description:/home/<USER>/moveit2_ws/install/moveit_common:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('CMAKE_ARGS', '-DPYTHON_EXECUTABLE=/usr/bin/python3.10 -DPython3_EXECUTABLE=/usr/bin/python3.10'), ('CMAKE_MODULE_PATH', '/home/<USER>/moveit2_ws/install/moveit_task_constructor_core/share/moveit_task_constructor_core/cmake'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('PWD', '/home/<USER>/Code/ws_0/build/arm_controller_pkg'), ('SSH_CONNECTION', '::1 0 ::1 22'), ('XDG_DATA_DIRS', '/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/moveit2_ws/install/moveit_task_constructor_core/local/lib/python3.10/dist-packages:/home/<USER>/moveit2_ws/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/moveit2_ws/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/moveit2_ws/install/moveit_task_constructor_msgs/local/lib/python3.10/dist-packages:/home/<USER>/moveit2_ws/install/launch_param_builder/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('Python3_EXECUTABLE', '/usr/bin/python3.10'), ('CMAKE_PREFIX_PATH', '/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner_testutils:/home/<USER>/moveit2_ws/install/moveit_runtime:/home/<USER>/moveit2_ws/install/moveit2_tutorials:/home/<USER>/moveit2_ws/install/moveit:/home/<USER>/moveit2_ws/install/moveit_planners:/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner:/home/<USER>/moveit2_ws/install/moveit_test:/home/<USER>/moveit2_ws/install/moveit_visual_tools:/home/<USER>/moveit2_ws/install/moveit_task_constructor_visualization:/home/<USER>/moveit2_ws/install/moveit_task_constructor_demo:/home/<USER>/moveit2_ws/install/moveit_task_constructor_capabilities:/home/<USER>/moveit2_ws/install/moveit_task_constructor_core:/home/<USER>/moveit2_ws/install/moveit_ros_control_interface:/home/<USER>/moveit2_ws/install/moveit_plugins:/home/<USER>/moveit2_ws/install/moveit_simple_controller_manager:/home/<USER>/moveit2_ws/install/moveit_setup_assistant:/home/<USER>/moveit2_ws/install/moveit_setup_srdf_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_core_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_controllers:/home/<USER>/moveit2_ws/install/moveit_setup_app_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_framework:/home/<USER>/moveit2_ws/install/moveit_servo:/home/<USER>/moveit2_ws/install/moveit_ros:/home/<USER>/moveit2_ws/install/moveit_ros_visualization:/home/<USER>/moveit2_ws/install/moveit_hybrid_planning:/home/<USER>/moveit2_ws/install/move_program:/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface:/home/<USER>/moveit2_ws/install/moveit_ros_benchmarks:/home/<USER>/moveit2_ws/install/moveit_ros_warehouse:/home/<USER>/moveit2_ws/install/moveit_ros_robot_interaction:/home/<USER>/moveit2_ws/install/moveit_ros_perception:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_pg70_support:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_moveit_config:/home/<USER>/moveit2_ws/install/moveit_ros_move_group:/home/<USER>/moveit2_ws/install/moveit_planners_ompl:/home/<USER>/moveit2_ws/install/moveit_ros_planning:/home/<USER>/moveit2_ws/install/moveit_ros_occupancy_map_monitor:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/moveit2_ws/install/moveit_planners_chomp:/home/<USER>/moveit2_ws/install/moveit_kinematics:/home/<USER>/moveit2_ws/install/moveit_chomp_optimizer_adapter:/home/<USER>/moveit2_ws/install/chomp_motion_planner:/home/<USER>/moveit2_ws/install/moveit_core:/home/<USER>/moveit2_ws/install/srdfdom:/home/<USER>/moveit2_ws/install/rviz_marker_tools:/home/<USER>/moveit2_ws/install/rosparam_shortcuts:/home/<USER>/moveit2_ws/install/moveit_task_constructor_msgs:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_support:/home/<USER>/moveit2_ws/install/moveit_resources:/home/<USER>/moveit2_ws/install/moveit_resources_pr2_description:/home/<USER>/moveit2_ws/install/moveit_resources_panda_moveit_config:/home/<USER>/moveit2_ws/install/moveit_resources_panda_description:/home/<USER>/moveit2_ws/install/moveit_resources_fanuc_moveit_config:/home/<USER>/moveit2_ws/install/moveit_resources_fanuc_description:/home/<USER>/moveit2_ws/install/moveit_common:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble')]), 'shell': False}
[16.395366] (arm_controller_pkg) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[16.397033] (arm_controller_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/lib/arm_controller_pkg/path_planning_node\n'}
[16.398256] (arm_controller_pkg) StdoutLine: {'line': b'-- Installing: /home/<USER>/Code/ws_0/install/arm_controller_pkg/lib/arm_controller_pkg/RB_builder_node\n'}
[16.400824] (arm_controller_pkg) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/Code/ws_0/install/arm_controller_pkg/lib/arm_controller_pkg/RB_builder_node" to ""\n'}
[16.400968] (arm_controller_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/ament_index/resource_index/package_run_dependencies/arm_controller_pkg\n'}
[16.401020] (arm_controller_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/ament_index/resource_index/parent_prefix_path/arm_controller_pkg\n'}
[16.401063] (arm_controller_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/environment/ament_prefix_path.sh\n'}
[16.402127] (arm_controller_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/environment/ament_prefix_path.dsv\n'}
[16.402188] (arm_controller_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/environment/path.sh\n'}
[16.402220] (arm_controller_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/environment/path.dsv\n'}
[16.402247] (arm_controller_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/local_setup.bash\n'}
[16.402274] (arm_controller_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/local_setup.sh\n'}
[16.402303] (arm_controller_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/local_setup.zsh\n'}
[16.402338] (arm_controller_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/local_setup.dsv\n'}
[16.402366] (arm_controller_pkg) StdoutLine: {'line': b'-- Installing: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.dsv\n'}
[16.402395] (arm_controller_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/ament_index/resource_index/packages/arm_controller_pkg\n'}
[16.402424] (arm_controller_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/cmake/arm_controller_pkgConfig.cmake\n'}
[16.402450] (arm_controller_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/cmake/arm_controller_pkgConfig-version.cmake\n'}
[16.402476] (arm_controller_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.xml\n'}
[16.405930] (arm_controller_pkg) CommandEnded: {'returncode': 0}
[16.421841] (-) TimerEvent: {}
[16.424047] (arm_controller_pkg) JobEnded: {'identifier': 'arm_controller_pkg', 'rc': 0}
[16.425150] (-) EventReactorShutdown: {}
