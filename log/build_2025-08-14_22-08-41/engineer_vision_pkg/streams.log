[0.101s] Invoking command in '/home/<USER>/Code/ws_0/build/engineer_vision_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/engineer_vision_pkg -- -j8 -l8
[0.230s] [ 25%] Built target video_capturer_node
[0.235s] [ 50%] Built target video_saver_node
[0.237s] [ 75%] Built target serial_node
[0.243s] [ 87%] [32mBuilding CXX object CMakeFiles/RB_detector_node.dir/src/RB_detector_node.cpp.o[0m
[11.963s] [100%] [32m[1mLinking CXX executable RB_detector_node[0m
[12.405s] /usr/bin/ld: warning: libopencv_imgcodecs.so.4.5d, needed by /opt/ros/humble/lib/libcv_bridge.so, may conflict with libopencv_imgcodecs.so.411
[12.409s] /usr/bin/ld: warning: libopencv_imgproc.so.4.5d, needed by /opt/ros/humble/lib/libcv_bridge.so, may conflict with libopencv_imgproc.so.411
[12.416s] /usr/bin/ld: warning: libopencv_core.so.4.5d, needed by /opt/ros/humble/lib/libcv_bridge.so, may conflict with libopencv_core.so.411
[13.193s] [100%] Built target RB_detector_node
[13.205s] Invoked command in '/home/<USER>/Code/ws_0/build/engineer_vision_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/engineer_vision_pkg -- -j8 -l8
[13.206s] Invoking command in '/home/<USER>/Code/ws_0/build/engineer_vision_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/engineer_vision_pkg
[13.212s] -- Install configuration: ""
[13.214s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/engineer_vision_pkg/video_capturer_node
[13.215s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/engineer_vision_pkg/serial_node
[13.216s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/engineer_vision_pkg/video_saver_node
[13.218s] -- Installing: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/engineer_vision_pkg/RB_detector_node
[13.222s] -- Set non-toolchain portion of runtime path of "/home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/engineer_vision_pkg/RB_detector_node" to ""
[13.222s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/launch
[13.222s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/launch/launch.py
[13.222s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/ament_index/resource_index/package_run_dependencies/engineer_vision_pkg
[13.222s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/ament_index/resource_index/parent_prefix_path/engineer_vision_pkg
[13.222s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/environment/ament_prefix_path.sh
[13.222s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/environment/ament_prefix_path.dsv
[13.222s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/environment/path.sh
[13.222s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/environment/path.dsv
[13.222s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/local_setup.bash
[13.223s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/local_setup.sh
[13.223s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/local_setup.zsh
[13.223s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/local_setup.dsv
[13.223s] -- Installing: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.dsv
[13.223s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/ament_index/resource_index/packages/engineer_vision_pkg
[13.223s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/cmake/engineer_vision_pkgConfig.cmake
[13.223s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/cmake/engineer_vision_pkgConfig-version.cmake
[13.223s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.xml
[13.228s] Invoked command in '/home/<USER>/Code/ws_0/build/engineer_vision_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/engineer_vision_pkg
