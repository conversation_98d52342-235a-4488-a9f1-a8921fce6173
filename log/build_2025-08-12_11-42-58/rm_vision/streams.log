[0.043s] Invoking command in '/home/<USER>/Code/ws_0/build/rm_vision': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/rm_vision -- -j8 -l8
[0.077s] Invoked command in '/home/<USER>/Code/ws_0/build/rm_vision' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/rm_vision -- -j8 -l8
[0.082s] Invoking command in '/home/<USER>/Code/ws_0/build/rm_vision': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/rm_vision
[0.089s] -- Install configuration: ""
[0.089s] -- Up-to-date: /home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision//launch
[0.090s] -- Up-to-date: /home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision//launch/launch.py
[0.090s] -- Up-to-date: /home/<USER>/Code/ws_0/install/rm_vision/share/ament_index/resource_index/package_run_dependencies/rm_vision
[0.090s] -- Up-to-date: /home/<USER>/Code/ws_0/install/rm_vision/share/ament_index/resource_index/parent_prefix_path/rm_vision
[0.090s] -- Up-to-date: /home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/environment/ament_prefix_path.sh
[0.090s] -- Up-to-date: /home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/environment/ament_prefix_path.dsv
[0.090s] -- Up-to-date: /home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/environment/path.sh
[0.094s] -- Up-to-date: /home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/environment/path.dsv
[0.100s] -- Up-to-date: /home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/local_setup.bash
[0.101s] -- Up-to-date: /home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/local_setup.sh
[0.101s] -- Up-to-date: /home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/local_setup.zsh
[0.101s] -- Up-to-date: /home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/local_setup.dsv
[0.101s] -- Installing: /home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.dsv
[0.102s] -- Up-to-date: /home/<USER>/Code/ws_0/install/rm_vision/share/ament_index/resource_index/packages/rm_vision
[0.102s] -- Up-to-date: /home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/cmake/rm_visionConfig.cmake
[0.102s] -- Up-to-date: /home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/cmake/rm_visionConfig-version.cmake
[0.102s] -- Up-to-date: /home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.xml
[0.106s] Invoked command in '/home/<USER>/Code/ws_0/build/rm_vision' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/rm_vision
