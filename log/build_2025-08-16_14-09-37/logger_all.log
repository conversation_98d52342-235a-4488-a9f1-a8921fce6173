[0.275s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build', '--packages-select', 'arm_controller_pkg']
[0.275s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=8, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['arm_controller_pkg'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0xffff8c68abf0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0xffff8c68a6e0>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0xffff8c68a6e0>>, mixin_verb=('build',))
[0.519s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.519s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.519s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.519s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.519s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.519s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.519s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/Code/ws_0'
[0.520s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.520s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.520s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.520s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.520s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.520s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.520s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.520s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.520s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.530s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.531s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.531s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.531s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.531s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.531s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.531s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.531s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.531s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.531s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.531s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.531s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.531s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.531s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.531s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.531s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.531s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.531s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.531s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.531s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.531s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.531s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.531s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.531s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.531s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.531s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.531s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.532s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.532s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extensions ['ignore', 'ignore_ament_install']
[0.532s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extension 'ignore'
[0.532s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extension 'ignore_ament_install'
[0.532s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extensions ['colcon_pkg']
[0.532s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extension 'colcon_pkg'
[0.532s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extensions ['colcon_meta']
[0.532s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extension 'colcon_meta'
[0.532s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extensions ['ros']
[0.532s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extension 'ros'
[0.536s] DEBUG:colcon.colcon_core.package_identification:Package 'src/arm_controller_pkg' with type 'ros.ament_cmake' and name 'arm_controller_pkg'
[0.536s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extensions ['ignore', 'ignore_ament_install']
[0.536s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extension 'ignore'
[0.536s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extension 'ignore_ament_install'
[0.536s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extensions ['colcon_pkg']
[0.536s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extension 'colcon_pkg'
[0.536s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extensions ['colcon_meta']
[0.536s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extension 'colcon_meta'
[0.536s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extensions ['ros']
[0.536s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extension 'ros'
[0.537s] DEBUG:colcon.colcon_core.package_identification:Package 'src/engineer_vision_pkg' with type 'ros.ament_cmake' and name 'engineer_vision_pkg'
[0.538s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extensions ['ignore', 'ignore_ament_install']
[0.538s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extension 'ignore'
[0.538s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extension 'ignore_ament_install'
[0.538s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extensions ['colcon_pkg']
[0.538s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extension 'colcon_pkg'
[0.538s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extensions ['colcon_meta']
[0.538s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extension 'colcon_meta'
[0.538s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extensions ['ros']
[0.538s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extension 'ros'
[0.539s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rm_vision' with type 'ros.ament_cmake' and name 'rm_vision'
[0.540s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extensions ['ignore', 'ignore_ament_install']
[0.540s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'ignore'
[0.540s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'ignore_ament_install'
[0.540s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extensions ['colcon_pkg']
[0.540s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'colcon_pkg'
[0.540s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extensions ['colcon_meta']
[0.540s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'colcon_meta'
[0.540s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extensions ['ros']
[0.540s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'ros'
[0.540s] DEBUG:colcon.colcon_core.package_identification:Package 'src/robot_description' with type 'ros.ament_cmake' and name 'robot_description'
[0.541s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extensions ['ignore', 'ignore_ament_install']
[0.541s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extension 'ignore'
[0.541s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extension 'ignore_ament_install'
[0.541s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extensions ['colcon_pkg']
[0.541s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extension 'colcon_pkg'
[0.541s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extensions ['colcon_meta']
[0.541s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extension 'colcon_meta'
[0.541s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extensions ['ros']
[0.541s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extension 'ros'
[0.543s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ros2_mindvision_camera' with type 'ros.ament_cmake' and name 'mindvision_camera'
[0.543s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.543s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.543s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.543s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.543s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.586s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'engineer_vision_pkg' in 'src/engineer_vision_pkg'
[0.586s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'mindvision_camera' in 'src/ros2_mindvision_camera'
[0.586s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'rm_vision' in 'src/rm_vision'
[0.586s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'robot_description' in 'src/robot_description'
[0.586s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.586s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.592s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 57 installed packages in /home/<USER>/moveit2_ws/install
[0.593s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 323 installed packages in /opt/ros/humble
[0.594s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.666s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'cmake_args' from command line to 'None'
[0.666s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'cmake_target' from command line to 'None'
[0.666s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.666s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'cmake_clean_cache' from command line to 'False'
[0.666s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'cmake_clean_first' from command line to 'False'
[0.666s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'cmake_force_configure' from command line to 'False'
[0.666s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'ament_cmake_args' from command line to 'None'
[0.666s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'catkin_cmake_args' from command line to 'None'
[0.666s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.666s] DEBUG:colcon.colcon_core.verb:Building package 'arm_controller_pkg' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Code/ws_0/build/arm_controller_pkg', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Code/ws_0/install/arm_controller_pkg', 'merge_install': False, 'path': '/home/<USER>/Code/ws_0/src/arm_controller_pkg', 'symlink_install': False, 'test_result_base': None}
[0.666s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.671s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.671s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/Code/ws_0/src/arm_controller_pkg' with build type 'ament_cmake'
[0.672s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Code/ws_0/src/arm_controller_pkg'
[0.674s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.674s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.674s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.711s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/arm_controller_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/arm_controller_pkg -- -j8 -l8
[12.062s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/arm_controller_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/arm_controller_pkg -- -j8 -l8
[12.068s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/arm_controller_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/arm_controller_pkg
[12.088s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(arm_controller_pkg)
[12.091s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/arm_controller_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/arm_controller_pkg
[12.095s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg' for CMake module files
[12.096s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg' for CMake config files
[12.096s] Level 1:colcon.colcon_core.shell:create_environment_hook('arm_controller_pkg', 'cmake_prefix_path')
[12.096s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/hook/cmake_prefix_path.ps1'
[12.098s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/hook/cmake_prefix_path.dsv'
[12.098s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/hook/cmake_prefix_path.sh'
[12.099s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/lib'
[12.099s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/bin'
[12.099s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/lib/pkgconfig/arm_controller_pkg.pc'
[12.100s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/lib/python3.10/site-packages'
[12.100s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/bin'
[12.100s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.ps1'
[12.101s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.dsv'
[12.101s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.sh'
[12.102s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.bash'
[12.103s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.zsh'
[12.104s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/colcon-core/packages/arm_controller_pkg)
[12.105s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(arm_controller_pkg)
[12.105s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg' for CMake module files
[12.106s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg' for CMake config files
[12.106s] Level 1:colcon.colcon_core.shell:create_environment_hook('arm_controller_pkg', 'cmake_prefix_path')
[12.106s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/hook/cmake_prefix_path.ps1'
[12.106s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/hook/cmake_prefix_path.dsv'
[12.106s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/hook/cmake_prefix_path.sh'
[12.107s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/lib'
[12.107s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/bin'
[12.107s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/lib/pkgconfig/arm_controller_pkg.pc'
[12.107s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/lib/python3.10/site-packages'
[12.107s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/bin'
[12.107s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.ps1'
[12.107s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.dsv'
[12.108s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.sh'
[12.108s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.bash'
[12.108s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.zsh'
[12.108s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/colcon-core/packages/arm_controller_pkg)
[12.108s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[12.114s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[12.114s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[12.114s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[12.135s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[12.135s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[12.135s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[12.205s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[12.205s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Code/ws_0/install/local_setup.ps1'
[12.206s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/Code/ws_0/install/_local_setup_util_ps1.py'
[12.208s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Code/ws_0/install/setup.ps1'
[12.213s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Code/ws_0/install/local_setup.sh'
[12.213s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/Code/ws_0/install/_local_setup_util_sh.py'
[12.214s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Code/ws_0/install/setup.sh'
[12.216s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Code/ws_0/install/local_setup.bash'
[12.216s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Code/ws_0/install/setup.bash'
[12.220s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Code/ws_0/install/local_setup.zsh'
[12.223s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Code/ws_0/install/setup.zsh'
