[0.032s] Invoking command in '/home/<USER>/Code/ws_0/build/engineer_vision_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/engineer_vision_pkg -- -j8 -l8
[0.120s] [ 25%] Built target serial_node
[0.121s] [ 50%] Built target video_capturer_node
[0.124s] [ 62%] [32mBuilding CXX object CMakeFiles/RB_detector_node.dir/src/RB_detector_node.cpp.o[0m
[0.130s] [ 87%] Built target video_saver_node
[10.375s] [100%] [32m[1mLinking CXX executable RB_detector_node[0m
[10.820s] /usr/bin/ld: warning: libopencv_imgcodecs.so.4.5d, needed by /opt/ros/humble/lib/libcv_bridge.so, may conflict with libopencv_imgcodecs.so.411
[10.825s] /usr/bin/ld: warning: libopencv_imgproc.so.4.5d, needed by /opt/ros/humble/lib/libcv_bridge.so, may conflict with libopencv_imgproc.so.411
[10.837s] /usr/bin/ld: warning: libopencv_core.so.4.5d, needed by /opt/ros/humble/lib/libcv_bridge.so, may conflict with libopencv_core.so.411
[11.607s] [100%] Built target RB_detector_node
[11.628s] Invoked command in '/home/<USER>/Code/ws_0/build/engineer_vision_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/engineer_vision_pkg -- -j8 -l8
[11.629s] Invoking command in '/home/<USER>/Code/ws_0/build/engineer_vision_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/engineer_vision_pkg
[11.635s] -- Install configuration: ""
[11.637s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/engineer_vision_pkg/video_capturer_node
[11.638s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/engineer_vision_pkg/serial_node
[11.639s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/engineer_vision_pkg/video_saver_node
[11.641s] -- Installing: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/engineer_vision_pkg/RB_detector_node
[11.644s] -- Set non-toolchain portion of runtime path of "/home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/engineer_vision_pkg/RB_detector_node" to ""
[11.644s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/launch
[11.644s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/launch/launch.py
[11.644s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/ament_index/resource_index/package_run_dependencies/engineer_vision_pkg
[11.644s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/ament_index/resource_index/parent_prefix_path/engineer_vision_pkg
[11.644s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/environment/ament_prefix_path.sh
[11.644s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/environment/ament_prefix_path.dsv
[11.644s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/environment/path.sh
[11.645s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/environment/path.dsv
[11.645s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/local_setup.bash
[11.645s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/local_setup.sh
[11.645s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/local_setup.zsh
[11.645s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/local_setup.dsv
[11.645s] -- Installing: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.dsv
[11.645s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/ament_index/resource_index/packages/engineer_vision_pkg
[11.645s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/cmake/engineer_vision_pkgConfig.cmake
[11.645s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/cmake/engineer_vision_pkgConfig-version.cmake
[11.645s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.xml
[11.650s] Invoked command in '/home/<USER>/Code/ws_0/build/engineer_vision_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/engineer_vision_pkg
