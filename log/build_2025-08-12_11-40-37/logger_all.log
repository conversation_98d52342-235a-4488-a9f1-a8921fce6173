[0.199s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.199s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=8, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0xffff99782b90>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0xffff99782680>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0xffff99782680>>, mixin_verb=('build',))
[0.494s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.494s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.494s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.494s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.494s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.494s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.494s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/Code/ws_0'
[0.494s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.494s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.495s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.495s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.495s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.495s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.495s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.495s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.495s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.512s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.512s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.512s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.512s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.513s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.513s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.513s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.513s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.513s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.513s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.513s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.513s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.513s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.513s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.513s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.513s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.513s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.513s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.513s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.513s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.513s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.513s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.513s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.514s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.514s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.514s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.514s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.514s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.514s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extensions ['ignore', 'ignore_ament_install']
[0.514s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extension 'ignore'
[0.514s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extension 'ignore_ament_install'
[0.514s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extensions ['colcon_pkg']
[0.514s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extension 'colcon_pkg'
[0.514s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extensions ['colcon_meta']
[0.514s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extension 'colcon_meta'
[0.514s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extensions ['ros']
[0.514s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extension 'ros'
[0.519s] DEBUG:colcon.colcon_core.package_identification:Package 'src/arm_controller_pkg' with type 'ros.ament_cmake' and name 'arm_controller_pkg'
[0.519s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extensions ['ignore', 'ignore_ament_install']
[0.519s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extension 'ignore'
[0.519s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extension 'ignore_ament_install'
[0.519s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extensions ['colcon_pkg']
[0.519s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extension 'colcon_pkg'
[0.519s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extensions ['colcon_meta']
[0.519s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extension 'colcon_meta'
[0.519s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extensions ['ros']
[0.519s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extension 'ros'
[0.521s] DEBUG:colcon.colcon_core.package_identification:Package 'src/engineer_vision_pkg' with type 'ros.ament_cmake' and name 'engineer_vision_pkg'
[0.521s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extensions ['ignore', 'ignore_ament_install']
[0.521s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extension 'ignore'
[0.521s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extension 'ignore_ament_install'
[0.521s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extensions ['colcon_pkg']
[0.521s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extension 'colcon_pkg'
[0.521s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extensions ['colcon_meta']
[0.521s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extension 'colcon_meta'
[0.521s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extensions ['ros']
[0.521s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extension 'ros'
[0.522s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rm_vision' with type 'ros.ament_cmake' and name 'rm_vision'
[0.522s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extensions ['ignore', 'ignore_ament_install']
[0.522s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'ignore'
[0.522s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'ignore_ament_install'
[0.522s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extensions ['colcon_pkg']
[0.522s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'colcon_pkg'
[0.522s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extensions ['colcon_meta']
[0.522s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'colcon_meta'
[0.522s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extensions ['ros']
[0.522s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'ros'
[0.523s] DEBUG:colcon.colcon_core.package_identification:Package 'src/robot_description' with type 'ros.ament_cmake' and name 'robot_description'
[0.524s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extensions ['ignore', 'ignore_ament_install']
[0.524s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extension 'ignore'
[0.524s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extension 'ignore_ament_install'
[0.524s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extensions ['colcon_pkg']
[0.524s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extension 'colcon_pkg'
[0.524s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extensions ['colcon_meta']
[0.524s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extension 'colcon_meta'
[0.524s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extensions ['ros']
[0.524s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extension 'ros'
[0.527s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ros2_mindvision_camera' with type 'ros.ament_cmake' and name 'mindvision_camera'
[0.527s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.527s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.527s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.527s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.527s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.566s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.566s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.572s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 5 installed packages in /home/<USER>/Code/ws_0/install
[0.577s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 57 installed packages in /home/<USER>/moveit2_ws/install
[0.578s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 323 installed packages in /opt/ros/humble
[0.579s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.938s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'cmake_args' from command line to 'None'
[0.938s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'cmake_target' from command line to 'None'
[0.938s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.938s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'cmake_clean_cache' from command line to 'False'
[0.938s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'cmake_clean_first' from command line to 'False'
[0.938s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'cmake_force_configure' from command line to 'False'
[0.938s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'ament_cmake_args' from command line to 'None'
[0.938s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'catkin_cmake_args' from command line to 'None'
[0.938s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.939s] DEBUG:colcon.colcon_core.verb:Building package 'arm_controller_pkg' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Code/ws_0/build/arm_controller_pkg', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Code/ws_0/install/arm_controller_pkg', 'merge_install': False, 'path': '/home/<USER>/Code/ws_0/src/arm_controller_pkg', 'symlink_install': False, 'test_result_base': None}
[0.939s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'cmake_args' from command line to 'None'
[0.939s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'cmake_target' from command line to 'None'
[0.939s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.939s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'cmake_clean_cache' from command line to 'False'
[0.939s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'cmake_clean_first' from command line to 'False'
[0.939s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'cmake_force_configure' from command line to 'False'
[0.939s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'ament_cmake_args' from command line to 'None'
[0.939s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'catkin_cmake_args' from command line to 'None'
[0.939s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.939s] DEBUG:colcon.colcon_core.verb:Building package 'engineer_vision_pkg' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Code/ws_0/build/engineer_vision_pkg', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Code/ws_0/install/engineer_vision_pkg', 'merge_install': False, 'path': '/home/<USER>/Code/ws_0/src/engineer_vision_pkg', 'symlink_install': False, 'test_result_base': None}
[0.940s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'cmake_args' from command line to 'None'
[0.940s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'cmake_target' from command line to 'None'
[0.940s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.940s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'cmake_clean_cache' from command line to 'False'
[0.940s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'cmake_clean_first' from command line to 'False'
[0.940s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'cmake_force_configure' from command line to 'False'
[0.940s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'ament_cmake_args' from command line to 'None'
[0.940s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'catkin_cmake_args' from command line to 'None'
[0.940s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.940s] DEBUG:colcon.colcon_core.verb:Building package 'mindvision_camera' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Code/ws_0/build/mindvision_camera', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Code/ws_0/install/mindvision_camera', 'merge_install': False, 'path': '/home/<USER>/Code/ws_0/src/ros2_mindvision_camera', 'symlink_install': False, 'test_result_base': None}
[0.941s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'cmake_args' from command line to 'None'
[0.941s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'cmake_target' from command line to 'None'
[0.941s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.941s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'cmake_clean_cache' from command line to 'False'
[0.941s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'cmake_clean_first' from command line to 'False'
[0.941s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'cmake_force_configure' from command line to 'False'
[0.941s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'ament_cmake_args' from command line to 'None'
[0.941s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'catkin_cmake_args' from command line to 'None'
[0.941s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.941s] DEBUG:colcon.colcon_core.verb:Building package 'rm_vision' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Code/ws_0/build/rm_vision', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Code/ws_0/install/rm_vision', 'merge_install': False, 'path': '/home/<USER>/Code/ws_0/src/rm_vision', 'symlink_install': False, 'test_result_base': None}
[0.941s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_args' from command line to 'None'
[0.941s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_target' from command line to 'None'
[0.941s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.941s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_clean_cache' from command line to 'False'
[0.941s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_clean_first' from command line to 'False'
[0.941s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_force_configure' from command line to 'False'
[0.941s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'ament_cmake_args' from command line to 'None'
[0.941s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'catkin_cmake_args' from command line to 'None'
[0.941s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.941s] DEBUG:colcon.colcon_core.verb:Building package 'robot_description' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Code/ws_0/build/robot_description', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Code/ws_0/install/robot_description', 'merge_install': False, 'path': '/home/<USER>/Code/ws_0/src/robot_description', 'symlink_install': False, 'test_result_base': None}
[0.942s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.944s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.944s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/Code/ws_0/src/arm_controller_pkg' with build type 'ament_cmake'
[0.944s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Code/ws_0/src/arm_controller_pkg'
[0.949s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.949s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.949s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.958s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/Code/ws_0/src/engineer_vision_pkg' with build type 'ament_cmake'
[0.958s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Code/ws_0/src/engineer_vision_pkg'
[0.959s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.959s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.975s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/Code/ws_0/src/ros2_mindvision_camera' with build type 'ament_cmake'
[0.976s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Code/ws_0/src/ros2_mindvision_camera'
[0.977s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.977s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.995s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/Code/ws_0/src/rm_vision' with build type 'ament_cmake'
[0.995s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Code/ws_0/src/rm_vision'
[0.999s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.999s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.006s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/Code/ws_0/src/robot_description' with build type 'ament_cmake'
[1.006s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Code/ws_0/src/robot_description'
[1.006s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[1.006s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[1.031s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/arm_controller_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/arm_controller_pkg -- -j8 -l8
[1.043s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/engineer_vision_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/engineer_vision_pkg -- -j8 -l8
[1.048s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/mindvision_camera': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/mindvision_camera -- -j8 -l8
[1.060s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/rm_vision': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/rm_vision -- -j8 -l8
[1.074s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/robot_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/robot_description -- -j8 -l8
[1.128s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/robot_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/robot_description -- -j8 -l8
[1.138s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/robot_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/robot_description
[1.155s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/rm_vision' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/rm_vision -- -j8 -l8
[1.156s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/rm_vision': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/rm_vision
[1.167s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(robot_description)
[1.170s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description' for CMake module files
[1.170s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description' for CMake config files
[1.171s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/robot_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/robot_description
[1.172s] Level 1:colcon.colcon_core.shell:create_environment_hook('robot_description', 'cmake_prefix_path')
[1.173s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/hook/cmake_prefix_path.ps1'
[1.176s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/hook/cmake_prefix_path.dsv'
[1.176s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/hook/cmake_prefix_path.sh'
[1.177s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description/bin'
[1.177s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description/lib/pkgconfig/robot_description.pc'
[1.177s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description/lib/python3.10/site-packages'
[1.177s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description/bin'
[1.177s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.ps1'
[1.178s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.dsv'
[1.179s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.sh'
[1.179s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.bash'
[1.181s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.zsh'
[1.186s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/robot_description/share/colcon-core/packages/robot_description)
[1.197s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(robot_description)
[1.197s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description' for CMake module files
[1.197s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description' for CMake config files
[1.198s] Level 1:colcon.colcon_core.shell:create_environment_hook('robot_description', 'cmake_prefix_path')
[1.198s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/hook/cmake_prefix_path.ps1'
[1.198s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/hook/cmake_prefix_path.dsv'
[1.198s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/hook/cmake_prefix_path.sh'
[1.199s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description/bin'
[1.199s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description/lib/pkgconfig/robot_description.pc'
[1.199s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description/lib/python3.10/site-packages'
[1.199s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description/bin'
[1.199s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.ps1'
[1.200s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.dsv'
[1.201s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.sh'
[1.202s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.bash'
[1.203s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.zsh'
[1.205s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/robot_description/share/colcon-core/packages/robot_description)
[1.206s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rm_vision)
[1.207s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision' for CMake module files
[1.207s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/rm_vision' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/rm_vision
[1.207s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision' for CMake config files
[1.208s] Level 1:colcon.colcon_core.shell:create_environment_hook('rm_vision', 'cmake_prefix_path')
[1.208s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/hook/cmake_prefix_path.ps1'
[1.208s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/hook/cmake_prefix_path.dsv'
[1.209s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/hook/cmake_prefix_path.sh'
[1.210s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision/bin'
[1.210s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision/lib/pkgconfig/rm_vision.pc'
[1.211s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision/lib/python3.10/site-packages'
[1.211s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision/bin'
[1.212s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.ps1'
[1.215s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.dsv'
[1.216s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.sh'
[1.217s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.bash'
[1.218s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.zsh'
[1.219s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/rm_vision/share/colcon-core/packages/rm_vision)
[1.219s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rm_vision)
[1.219s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision' for CMake module files
[1.219s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision' for CMake config files
[1.220s] Level 1:colcon.colcon_core.shell:create_environment_hook('rm_vision', 'cmake_prefix_path')
[1.220s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/hook/cmake_prefix_path.ps1'
[1.220s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/hook/cmake_prefix_path.dsv'
[1.220s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/hook/cmake_prefix_path.sh'
[1.221s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision/bin'
[1.221s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision/lib/pkgconfig/rm_vision.pc'
[1.221s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision/lib/python3.10/site-packages'
[1.221s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision/bin'
[1.222s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.ps1'
[1.222s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.dsv'
[1.222s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.sh'
[1.223s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.bash'
[1.223s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.zsh'
[1.224s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/rm_vision/share/colcon-core/packages/rm_vision)
[1.226s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/mindvision_camera' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/mindvision_camera -- -j8 -l8
[1.228s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/mindvision_camera': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/mindvision_camera
[1.241s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/engineer_vision_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/engineer_vision_pkg -- -j8 -l8
[1.241s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/engineer_vision_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/engineer_vision_pkg
[1.264s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(mindvision_camera)
[1.265s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera' for CMake module files
[1.269s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/mindvision_camera' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/mindvision_camera
[1.269s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera' for CMake config files
[1.269s] Level 1:colcon.colcon_core.shell:create_environment_hook('mindvision_camera', 'cmake_prefix_path')
[1.269s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/cmake_prefix_path.ps1'
[1.269s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/cmake_prefix_path.dsv'
[1.270s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/cmake_prefix_path.sh'
[1.271s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/lib'
[1.271s] Level 1:colcon.colcon_core.shell:create_environment_hook('mindvision_camera', 'ld_library_path_lib')
[1.271s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/ld_library_path_lib.ps1'
[1.271s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/ld_library_path_lib.dsv'
[1.271s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/ld_library_path_lib.sh'
[1.271s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/bin'
[1.271s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/lib/pkgconfig/mindvision_camera.pc'
[1.272s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/lib/python3.10/site-packages'
[1.272s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/bin'
[1.272s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.ps1'
[1.272s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.dsv'
[1.273s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.sh'
[1.274s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.bash'
[1.275s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.zsh'
[1.276s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/mindvision_camera/share/colcon-core/packages/mindvision_camera)
[1.277s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(mindvision_camera)
[1.277s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera' for CMake module files
[1.277s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera' for CMake config files
[1.278s] Level 1:colcon.colcon_core.shell:create_environment_hook('mindvision_camera', 'cmake_prefix_path')
[1.278s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/cmake_prefix_path.ps1'
[1.278s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/cmake_prefix_path.dsv'
[1.278s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/cmake_prefix_path.sh'
[1.279s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/lib'
[1.279s] Level 1:colcon.colcon_core.shell:create_environment_hook('mindvision_camera', 'ld_library_path_lib')
[1.279s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/ld_library_path_lib.ps1'
[1.279s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/ld_library_path_lib.dsv'
[1.279s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/ld_library_path_lib.sh'
[1.280s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/bin'
[1.280s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/lib/pkgconfig/mindvision_camera.pc'
[1.280s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/lib/python3.10/site-packages'
[1.280s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/bin'
[1.280s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.ps1'
[1.281s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.dsv'
[1.283s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.sh'
[1.284s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.bash'
[1.285s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.zsh'
[1.285s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/mindvision_camera/share/colcon-core/packages/mindvision_camera)
[1.286s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(engineer_vision_pkg)
[1.286s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg' for CMake module files
[1.287s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/engineer_vision_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/engineer_vision_pkg
[1.287s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg' for CMake config files
[1.287s] Level 1:colcon.colcon_core.shell:create_environment_hook('engineer_vision_pkg', 'cmake_prefix_path')
[1.287s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/hook/cmake_prefix_path.ps1'
[1.288s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/hook/cmake_prefix_path.dsv'
[1.288s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/hook/cmake_prefix_path.sh'
[1.288s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib'
[1.288s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/bin'
[1.288s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/pkgconfig/engineer_vision_pkg.pc'
[1.288s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/python3.10/site-packages'
[1.288s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/bin'
[1.289s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.ps1'
[1.289s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.dsv'
[1.289s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.sh'
[1.289s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.bash'
[1.290s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.zsh'
[1.290s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/colcon-core/packages/engineer_vision_pkg)
[1.291s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(engineer_vision_pkg)
[1.291s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg' for CMake module files
[1.291s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg' for CMake config files
[1.291s] Level 1:colcon.colcon_core.shell:create_environment_hook('engineer_vision_pkg', 'cmake_prefix_path')
[1.291s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/hook/cmake_prefix_path.ps1'
[1.292s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/hook/cmake_prefix_path.dsv'
[1.293s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/hook/cmake_prefix_path.sh'
[1.294s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib'
[1.294s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/bin'
[1.294s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/pkgconfig/engineer_vision_pkg.pc'
[1.294s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/python3.10/site-packages'
[1.295s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/bin'
[1.295s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.ps1'
[1.296s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.dsv'
[1.297s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.sh'
[1.299s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.bash'
[1.301s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.zsh'
[1.303s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/colcon-core/packages/engineer_vision_pkg)
[13.354s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/arm_controller_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/arm_controller_pkg -- -j8 -l8
[13.355s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/arm_controller_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/arm_controller_pkg
[13.366s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(arm_controller_pkg)
[13.367s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg' for CMake module files
[13.368s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg' for CMake config files
[13.368s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/arm_controller_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/arm_controller_pkg
[13.369s] Level 1:colcon.colcon_core.shell:create_environment_hook('arm_controller_pkg', 'cmake_prefix_path')
[13.369s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/hook/cmake_prefix_path.ps1'
[13.370s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/hook/cmake_prefix_path.dsv'
[13.370s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/hook/cmake_prefix_path.sh'
[13.371s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/lib'
[13.371s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/bin'
[13.372s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/lib/pkgconfig/arm_controller_pkg.pc'
[13.372s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/lib/python3.10/site-packages'
[13.372s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/bin'
[13.372s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.ps1'
[13.373s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.dsv'
[13.373s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.sh'
[13.374s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.bash'
[13.374s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.zsh'
[13.374s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/colcon-core/packages/arm_controller_pkg)
[13.375s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(arm_controller_pkg)
[13.375s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg' for CMake module files
[13.375s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg' for CMake config files
[13.375s] Level 1:colcon.colcon_core.shell:create_environment_hook('arm_controller_pkg', 'cmake_prefix_path')
[13.376s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/hook/cmake_prefix_path.ps1'
[13.376s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/hook/cmake_prefix_path.dsv'
[13.376s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/hook/cmake_prefix_path.sh'
[13.377s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/lib'
[13.377s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/bin'
[13.377s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/lib/pkgconfig/arm_controller_pkg.pc'
[13.377s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/lib/python3.10/site-packages'
[13.377s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/bin'
[13.377s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.ps1'
[13.378s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.dsv'
[13.378s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.sh'
[13.378s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.bash'
[13.379s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.zsh'
[13.379s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/colcon-core/packages/arm_controller_pkg)
[13.380s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[13.380s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[13.380s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[13.380s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[13.395s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[13.395s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[13.395s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[13.442s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[13.442s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Code/ws_0/install/local_setup.ps1'
[13.443s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/Code/ws_0/install/_local_setup_util_ps1.py'
[13.445s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Code/ws_0/install/setup.ps1'
[13.456s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Code/ws_0/install/local_setup.sh'
[13.457s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/Code/ws_0/install/_local_setup_util_sh.py'
[13.458s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Code/ws_0/install/setup.sh'
[13.460s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Code/ws_0/install/local_setup.bash'
[13.461s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Code/ws_0/install/setup.bash'
[13.463s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Code/ws_0/install/local_setup.zsh'
[13.463s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Code/ws_0/install/setup.zsh'
