[0.244s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.244s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=8, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0xffff94f12b90>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0xffff94f12680>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0xffff94f12680>>, mixin_verb=('build',))
[0.547s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.547s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.547s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.547s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.547s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.547s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.547s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/Code/ws_0'
[0.547s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.547s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.547s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.547s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.547s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.547s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.547s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.547s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.547s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.567s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.567s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.567s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.567s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.567s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.567s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.567s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.567s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.567s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.568s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.568s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.568s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.568s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.568s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.568s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.568s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.568s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.568s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.568s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.568s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.568s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.568s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.568s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.568s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.568s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.568s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.568s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.568s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.568s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extensions ['ignore', 'ignore_ament_install']
[0.568s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extension 'ignore'
[0.568s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extension 'ignore_ament_install'
[0.568s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extensions ['colcon_pkg']
[0.568s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extension 'colcon_pkg'
[0.568s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extensions ['colcon_meta']
[0.568s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extension 'colcon_meta'
[0.568s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extensions ['ros']
[0.568s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extension 'ros'
[0.571s] DEBUG:colcon.colcon_core.package_identification:Package 'src/arm_controller_pkg' with type 'ros.ament_cmake' and name 'arm_controller_pkg'
[0.571s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extensions ['ignore', 'ignore_ament_install']
[0.571s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extension 'ignore'
[0.572s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extension 'ignore_ament_install'
[0.572s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extensions ['colcon_pkg']
[0.572s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extension 'colcon_pkg'
[0.572s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extensions ['colcon_meta']
[0.572s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extension 'colcon_meta'
[0.572s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extensions ['ros']
[0.572s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extension 'ros'
[0.581s] DEBUG:colcon.colcon_core.package_identification:Package 'src/engineer_vision_pkg' with type 'ros.ament_cmake' and name 'engineer_vision_pkg'
[0.581s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extensions ['ignore', 'ignore_ament_install']
[0.582s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extension 'ignore'
[0.582s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extension 'ignore_ament_install'
[0.582s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extensions ['colcon_pkg']
[0.582s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extension 'colcon_pkg'
[0.582s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extensions ['colcon_meta']
[0.582s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extension 'colcon_meta'
[0.582s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extensions ['ros']
[0.582s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extension 'ros'
[0.583s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rm_vision' with type 'ros.ament_cmake' and name 'rm_vision'
[0.583s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extensions ['ignore', 'ignore_ament_install']
[0.583s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'ignore'
[0.583s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'ignore_ament_install'
[0.583s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extensions ['colcon_pkg']
[0.583s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'colcon_pkg'
[0.583s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extensions ['colcon_meta']
[0.584s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'colcon_meta'
[0.584s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extensions ['ros']
[0.584s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'ros'
[0.584s] DEBUG:colcon.colcon_core.package_identification:Package 'src/robot_description' with type 'ros.ament_cmake' and name 'robot_description'
[0.585s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extensions ['ignore', 'ignore_ament_install']
[0.585s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extension 'ignore'
[0.585s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extension 'ignore_ament_install'
[0.585s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extensions ['colcon_pkg']
[0.585s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extension 'colcon_pkg'
[0.585s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extensions ['colcon_meta']
[0.585s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extension 'colcon_meta'
[0.585s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extensions ['ros']
[0.585s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extension 'ros'
[0.586s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ros2_mindvision_camera' with type 'ros.ament_cmake' and name 'mindvision_camera'
[0.586s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.586s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.586s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.586s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.586s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.618s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.618s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.646s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 5 installed packages in /home/<USER>/Code/ws_0/install
[0.648s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 57 installed packages in /home/<USER>/moveit2_ws/install
[0.650s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 323 installed packages in /opt/ros/humble
[0.651s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.856s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'cmake_args' from command line to 'None'
[0.856s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'cmake_target' from command line to 'None'
[0.856s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.856s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'cmake_clean_cache' from command line to 'False'
[0.856s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'cmake_clean_first' from command line to 'False'
[0.857s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'cmake_force_configure' from command line to 'False'
[0.857s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'ament_cmake_args' from command line to 'None'
[0.857s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'catkin_cmake_args' from command line to 'None'
[0.857s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.857s] DEBUG:colcon.colcon_core.verb:Building package 'arm_controller_pkg' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Code/ws_0/build/arm_controller_pkg', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Code/ws_0/install/arm_controller_pkg', 'merge_install': False, 'path': '/home/<USER>/Code/ws_0/src/arm_controller_pkg', 'symlink_install': False, 'test_result_base': None}
[0.857s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'cmake_args' from command line to 'None'
[0.857s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'cmake_target' from command line to 'None'
[0.857s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.857s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'cmake_clean_cache' from command line to 'False'
[0.857s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'cmake_clean_first' from command line to 'False'
[0.857s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'cmake_force_configure' from command line to 'False'
[0.857s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'ament_cmake_args' from command line to 'None'
[0.857s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'catkin_cmake_args' from command line to 'None'
[0.857s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.857s] DEBUG:colcon.colcon_core.verb:Building package 'engineer_vision_pkg' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Code/ws_0/build/engineer_vision_pkg', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Code/ws_0/install/engineer_vision_pkg', 'merge_install': False, 'path': '/home/<USER>/Code/ws_0/src/engineer_vision_pkg', 'symlink_install': False, 'test_result_base': None}
[0.857s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'cmake_args' from command line to 'None'
[0.857s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'cmake_target' from command line to 'None'
[0.857s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.857s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'cmake_clean_cache' from command line to 'False'
[0.857s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'cmake_clean_first' from command line to 'False'
[0.857s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'cmake_force_configure' from command line to 'False'
[0.857s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'ament_cmake_args' from command line to 'None'
[0.857s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'catkin_cmake_args' from command line to 'None'
[0.857s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.858s] DEBUG:colcon.colcon_core.verb:Building package 'mindvision_camera' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Code/ws_0/build/mindvision_camera', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Code/ws_0/install/mindvision_camera', 'merge_install': False, 'path': '/home/<USER>/Code/ws_0/src/ros2_mindvision_camera', 'symlink_install': False, 'test_result_base': None}
[0.858s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'cmake_args' from command line to 'None'
[0.858s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'cmake_target' from command line to 'None'
[0.858s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.858s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'cmake_clean_cache' from command line to 'False'
[0.858s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'cmake_clean_first' from command line to 'False'
[0.858s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'cmake_force_configure' from command line to 'False'
[0.858s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'ament_cmake_args' from command line to 'None'
[0.858s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'catkin_cmake_args' from command line to 'None'
[0.858s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.858s] DEBUG:colcon.colcon_core.verb:Building package 'rm_vision' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Code/ws_0/build/rm_vision', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Code/ws_0/install/rm_vision', 'merge_install': False, 'path': '/home/<USER>/Code/ws_0/src/rm_vision', 'symlink_install': False, 'test_result_base': None}
[0.858s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_args' from command line to 'None'
[0.858s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_target' from command line to 'None'
[0.858s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.858s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_clean_cache' from command line to 'False'
[0.858s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_clean_first' from command line to 'False'
[0.858s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_force_configure' from command line to 'False'
[0.858s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'ament_cmake_args' from command line to 'None'
[0.858s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'catkin_cmake_args' from command line to 'None'
[0.858s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.858s] DEBUG:colcon.colcon_core.verb:Building package 'robot_description' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Code/ws_0/build/robot_description', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Code/ws_0/install/robot_description', 'merge_install': False, 'path': '/home/<USER>/Code/ws_0/src/robot_description', 'symlink_install': False, 'test_result_base': None}
[0.858s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.859s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.860s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/Code/ws_0/src/arm_controller_pkg' with build type 'ament_cmake'
[0.860s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Code/ws_0/src/arm_controller_pkg'
[0.864s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.864s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.865s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.872s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/Code/ws_0/src/engineer_vision_pkg' with build type 'ament_cmake'
[0.872s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Code/ws_0/src/engineer_vision_pkg'
[0.873s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.873s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.880s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/Code/ws_0/src/ros2_mindvision_camera' with build type 'ament_cmake'
[0.880s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Code/ws_0/src/ros2_mindvision_camera'
[0.880s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.881s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.885s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/Code/ws_0/src/rm_vision' with build type 'ament_cmake'
[0.885s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Code/ws_0/src/rm_vision'
[0.886s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.886s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.890s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/Code/ws_0/src/robot_description' with build type 'ament_cmake'
[0.891s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Code/ws_0/src/robot_description'
[0.891s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.891s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.908s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/arm_controller_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/arm_controller_pkg -- -j8 -l8
[0.916s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/engineer_vision_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/engineer_vision_pkg -- -j8 -l8
[0.919s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/mindvision_camera': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/mindvision_camera -- -j8 -l8
[0.924s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/rm_vision': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/rm_vision -- -j8 -l8
[0.930s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/robot_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/robot_description -- -j8 -l8
[0.999s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/robot_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/robot_description -- -j8 -l8
[1.012s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/robot_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/robot_description
[1.023s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/rm_vision' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/rm_vision -- -j8 -l8
[1.031s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/rm_vision': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/rm_vision
[1.034s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(robot_description)
[1.039s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/robot_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/robot_description
[1.051s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description' for CMake module files
[1.051s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description' for CMake config files
[1.054s] Level 1:colcon.colcon_core.shell:create_environment_hook('robot_description', 'cmake_prefix_path')
[1.054s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/hook/cmake_prefix_path.ps1'
[1.067s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/hook/cmake_prefix_path.dsv'
[1.071s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/hook/cmake_prefix_path.sh'
[1.137s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description/bin'
[1.137s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description/lib/pkgconfig/robot_description.pc'
[1.138s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description/lib/python3.10/site-packages'
[1.138s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description/bin'
[1.150s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.ps1'
[1.157s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.dsv'
[1.157s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.sh'
[1.159s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.bash'
[1.160s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.zsh'
[1.162s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/robot_description/share/colcon-core/packages/robot_description)
[1.162s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(robot_description)
[1.163s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description' for CMake module files
[1.163s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description' for CMake config files
[1.163s] Level 1:colcon.colcon_core.shell:create_environment_hook('robot_description', 'cmake_prefix_path')
[1.164s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/hook/cmake_prefix_path.ps1'
[1.164s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/hook/cmake_prefix_path.dsv'
[1.164s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/hook/cmake_prefix_path.sh'
[1.165s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description/bin'
[1.165s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description/lib/pkgconfig/robot_description.pc'
[1.165s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description/lib/python3.10/site-packages'
[1.165s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description/bin'
[1.165s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.ps1'
[1.166s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.dsv'
[1.166s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.sh'
[1.167s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.bash'
[1.167s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.zsh'
[1.167s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/robot_description/share/colcon-core/packages/robot_description)
[1.169s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rm_vision)
[1.169s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision' for CMake module files
[1.169s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision' for CMake config files
[1.169s] Level 1:colcon.colcon_core.shell:create_environment_hook('rm_vision', 'cmake_prefix_path')
[1.169s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/hook/cmake_prefix_path.ps1'
[1.170s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/hook/cmake_prefix_path.dsv'
[1.170s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/hook/cmake_prefix_path.sh'
[1.171s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision/bin'
[1.171s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision/lib/pkgconfig/rm_vision.pc'
[1.171s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision/lib/python3.10/site-packages'
[1.171s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision/bin'
[1.171s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.ps1'
[1.172s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/rm_vision' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/rm_vision
[1.172s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.dsv'
[1.173s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.sh'
[1.173s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.bash'
[1.174s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.zsh'
[1.174s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/rm_vision/share/colcon-core/packages/rm_vision)
[1.174s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rm_vision)
[1.174s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision' for CMake module files
[1.175s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision' for CMake config files
[1.175s] Level 1:colcon.colcon_core.shell:create_environment_hook('rm_vision', 'cmake_prefix_path')
[1.175s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/hook/cmake_prefix_path.ps1'
[1.175s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/hook/cmake_prefix_path.dsv'
[1.175s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/hook/cmake_prefix_path.sh'
[1.176s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision/bin'
[1.176s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision/lib/pkgconfig/rm_vision.pc'
[1.176s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision/lib/python3.10/site-packages'
[1.176s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision/bin'
[1.177s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.ps1'
[1.177s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.dsv'
[1.178s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.sh'
[1.178s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.bash'
[1.179s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.zsh'
[1.179s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/rm_vision/share/colcon-core/packages/rm_vision)
[1.180s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/mindvision_camera' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/mindvision_camera -- -j8 -l8
[1.181s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/mindvision_camera': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/mindvision_camera
[1.184s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/engineer_vision_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/engineer_vision_pkg -- -j8 -l8
[1.187s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/engineer_vision_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/engineer_vision_pkg
[1.201s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(mindvision_camera)
[1.202s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera' for CMake module files
[1.202s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/mindvision_camera' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/mindvision_camera
[1.203s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera' for CMake config files
[1.203s] Level 1:colcon.colcon_core.shell:create_environment_hook('mindvision_camera', 'cmake_prefix_path')
[1.203s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/cmake_prefix_path.ps1'
[1.204s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/cmake_prefix_path.dsv'
[1.207s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/cmake_prefix_path.sh'
[1.208s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/lib'
[1.208s] Level 1:colcon.colcon_core.shell:create_environment_hook('mindvision_camera', 'ld_library_path_lib')
[1.208s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/ld_library_path_lib.ps1'
[1.209s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/ld_library_path_lib.dsv'
[1.209s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/ld_library_path_lib.sh'
[1.209s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/bin'
[1.209s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/lib/pkgconfig/mindvision_camera.pc'
[1.209s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/lib/python3.10/site-packages'
[1.209s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/bin'
[1.213s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.ps1'
[1.214s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.dsv'
[1.214s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.sh'
[1.214s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.bash'
[1.215s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.zsh'
[1.215s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/mindvision_camera/share/colcon-core/packages/mindvision_camera)
[1.215s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(mindvision_camera)
[1.215s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera' for CMake module files
[1.219s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera' for CMake config files
[1.219s] Level 1:colcon.colcon_core.shell:create_environment_hook('mindvision_camera', 'cmake_prefix_path')
[1.219s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/cmake_prefix_path.ps1'
[1.219s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/cmake_prefix_path.dsv'
[1.220s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/cmake_prefix_path.sh'
[1.220s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/lib'
[1.220s] Level 1:colcon.colcon_core.shell:create_environment_hook('mindvision_camera', 'ld_library_path_lib')
[1.220s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/ld_library_path_lib.ps1'
[1.221s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/ld_library_path_lib.dsv'
[1.221s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/ld_library_path_lib.sh'
[1.221s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/bin'
[1.221s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/lib/pkgconfig/mindvision_camera.pc'
[1.221s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/lib/python3.10/site-packages'
[1.221s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/bin'
[1.221s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.ps1'
[1.225s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.dsv'
[1.226s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.sh'
[1.226s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.bash'
[1.227s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.zsh'
[1.231s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/mindvision_camera/share/colcon-core/packages/mindvision_camera)
[1.236s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(engineer_vision_pkg)
[1.237s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg' for CMake module files
[1.238s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg' for CMake config files
[1.238s] Level 1:colcon.colcon_core.shell:create_environment_hook('engineer_vision_pkg', 'cmake_prefix_path')
[1.238s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/hook/cmake_prefix_path.ps1'
[1.238s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/hook/cmake_prefix_path.dsv'
[1.238s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/hook/cmake_prefix_path.sh'
[1.241s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/engineer_vision_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/engineer_vision_pkg
[1.241s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib'
[1.241s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/bin'
[1.241s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/pkgconfig/engineer_vision_pkg.pc'
[1.242s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/python3.10/site-packages'
[1.242s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/bin'
[1.242s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.ps1'
[1.243s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.dsv'
[1.243s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.sh'
[1.246s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.bash'
[1.246s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.zsh'
[1.247s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/colcon-core/packages/engineer_vision_pkg)
[1.247s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(engineer_vision_pkg)
[1.247s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg' for CMake module files
[1.248s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg' for CMake config files
[1.248s] Level 1:colcon.colcon_core.shell:create_environment_hook('engineer_vision_pkg', 'cmake_prefix_path')
[1.248s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/hook/cmake_prefix_path.ps1'
[1.248s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/hook/cmake_prefix_path.dsv'
[1.249s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/hook/cmake_prefix_path.sh'
[1.250s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib'
[1.250s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/bin'
[1.250s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/pkgconfig/engineer_vision_pkg.pc'
[1.250s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/python3.10/site-packages'
[1.250s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/bin'
[1.250s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.ps1'
[1.251s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.dsv'
[1.251s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.sh'
[1.252s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.bash'
[1.252s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.zsh'
[1.253s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/colcon-core/packages/engineer_vision_pkg)
[12.906s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/arm_controller_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/arm_controller_pkg -- -j8 -l8
[12.921s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/arm_controller_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/arm_controller_pkg
[12.939s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(arm_controller_pkg)
[12.939s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg' for CMake module files
[12.940s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg' for CMake config files
[12.940s] Level 1:colcon.colcon_core.shell:create_environment_hook('arm_controller_pkg', 'cmake_prefix_path')
[12.940s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/hook/cmake_prefix_path.ps1'
[12.939s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/arm_controller_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/arm_controller_pkg
[12.941s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/hook/cmake_prefix_path.dsv'
[12.942s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/hook/cmake_prefix_path.sh'
[12.943s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/lib'
[12.943s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/bin'
[12.943s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/lib/pkgconfig/arm_controller_pkg.pc'
[12.943s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/lib/python3.10/site-packages'
[12.944s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/bin'
[12.944s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.ps1'
[12.944s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.dsv'
[12.945s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.sh'
[12.945s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.bash'
[12.945s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.zsh'
[12.945s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/colcon-core/packages/arm_controller_pkg)
[12.946s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(arm_controller_pkg)
[12.946s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg' for CMake module files
[12.946s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg' for CMake config files
[12.946s] Level 1:colcon.colcon_core.shell:create_environment_hook('arm_controller_pkg', 'cmake_prefix_path')
[12.946s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/hook/cmake_prefix_path.ps1'
[12.946s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/hook/cmake_prefix_path.dsv'
[12.947s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/hook/cmake_prefix_path.sh'
[12.947s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/lib'
[12.947s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/bin'
[12.948s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/lib/pkgconfig/arm_controller_pkg.pc'
[12.948s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/lib/python3.10/site-packages'
[12.948s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/bin'
[12.948s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.ps1'
[12.949s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.dsv'
[12.949s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.sh'
[12.949s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.bash'
[12.950s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.zsh'
[12.950s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/colcon-core/packages/arm_controller_pkg)
[12.950s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[12.951s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[12.951s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[12.951s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[12.969s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[12.969s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[12.970s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[13.072s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[13.072s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Code/ws_0/install/local_setup.ps1'
[13.073s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/Code/ws_0/install/_local_setup_util_ps1.py'
[13.075s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Code/ws_0/install/setup.ps1'
[13.083s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Code/ws_0/install/local_setup.sh'
[13.086s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/Code/ws_0/install/_local_setup_util_sh.py'
[13.087s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Code/ws_0/install/setup.sh'
[13.090s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Code/ws_0/install/local_setup.bash'
[13.090s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Code/ws_0/install/setup.bash'
[13.092s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Code/ws_0/install/local_setup.zsh'
[13.093s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Code/ws_0/install/setup.zsh'
