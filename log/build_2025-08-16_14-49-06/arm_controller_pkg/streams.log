[0.014s] Invoking command in '/home/<USER>/Code/ws_0/build/arm_controller_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/arm_controller_pkg -- -j8 -l8
[0.107s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.269s] -- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
[0.316s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.321s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.327s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.339s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.353s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.389s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.392s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.661s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.748s] -- Found tf2: 0.25.15 (/opt/ros/humble/share/tf2/cmake)
[0.765s] -- Found tf2_geometry_msgs: 0.25.15 (/opt/ros/humble/share/tf2_geometry_msgs/cmake)
[0.769s] -- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
[0.771s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[0.845s] -- Found moveit_ros_planning_interface: 2.5.9 (/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake)
[0.846s] [33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ConfigExtras.cmake:9 (find_package):
[0.846s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[0.846s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[0.846s]   set the policy and suppress this warning.
[0.847s] 
[0.847s] Call Stack (most recent call first):
[0.847s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
[0.847s]   CMakeLists.txt:16 (find_package)
[0.847s] This warning is for project developers.  Use -Wno-dev to suppress it.
[0.847s] [0m
[0.859s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: date_time filesystem program_options system thread
[0.973s] [33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_core/share/moveit_core/cmake/ConfigExtras.cmake:3 (find_package):
[0.973s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[0.973s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[0.973s]   set the policy and suppress this warning.
[0.973s] 
[0.973s] Call Stack (most recent call first):
[0.973s]   /home/<USER>/moveit2_ws/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)
[0.973s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[0.973s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
[0.973s]   CMakeLists.txt:16 (find_package)
[0.973s] This warning is for project developers.  Use -Wno-dev to suppress it.
[0.973s] [0m
[0.981s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: chrono date_time filesystem iostreams program_options regex serialization system thread
[1.016s] -- Found parameter_traits: 0.5.0 (/opt/ros/humble/share/parameter_traits/cmake)
[1.025s] -- Found rclcpp_lifecycle: 16.0.14 (/opt/ros/humble/share/rclcpp_lifecycle/cmake)
[1.047s] [33mCMake Warning (dev) at /opt/ros/humble/share/geometric_shapes/cmake/ConfigExtras.cmake:31 (find_package):
[1.047s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.047s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.047s]   set the policy and suppress this warning.
[1.047s] 
[1.047s] Call Stack (most recent call first):
[1.047s]   /opt/ros/humble/share/geometric_shapes/cmake/geometric_shapesConfig.cmake:41 (include)
[1.047s]   /home/<USER>/moveit2_ws/install/moveit_core/share/moveit_core/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.047s]   /home/<USER>/moveit2_ws/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)
[1.047s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.047s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
[1.047s]   CMakeLists.txt:16 (find_package)
[1.047s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.047s] [0m
[1.052s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: filesystem
[1.096s] -- library: /usr/lib/aarch64-linux-gnu/libcurl.so
[1.166s] [33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_ros_planning/share/moveit_ros_planning/cmake/ConfigExtras.cmake:3 (find_package):
[1.166s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.166s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.166s]   set the policy and suppress this warning.
[1.166s] 
[1.166s] Call Stack (most recent call first):
[1.166s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning/share/moveit_ros_planning/cmake/moveit_ros_planningConfig.cmake:41 (include)
[1.166s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.166s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
[1.166s]   CMakeLists.txt:16 (find_package)
[1.166s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.166s] [0m
[1.170s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem date_time program_options thread chrono
[1.196s] [33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_ros_warehouse/share/moveit_ros_warehouse/cmake/ConfigExtras.cmake:3 (find_package):
[1.196s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.197s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.197s]   set the policy and suppress this warning.
[1.197s] 
[1.197s] Call Stack (most recent call first):
[1.197s]   /home/<USER>/moveit2_ws/install/moveit_ros_warehouse/share/moveit_ros_warehouse/cmake/moveit_ros_warehouseConfig.cmake:41 (include)
[1.197s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.197s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
[1.197s]   CMakeLists.txt:16 (find_package)
[1.197s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.198s] [0m
[1.200s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: thread system filesystem regex date_time program_options
[1.214s] [33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_ros_move_group/share/moveit_ros_move_group/cmake/ConfigExtras.cmake:3 (find_package):
[1.214s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.214s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.215s]   set the policy and suppress this warning.
[1.215s] 
[1.215s] Call Stack (most recent call first):
[1.215s]   /home/<USER>/moveit2_ws/install/moveit_ros_move_group/share/moveit_ros_move_group/cmake/moveit_ros_move_groupConfig.cmake:41 (include)
[1.215s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.215s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
[1.215s]   CMakeLists.txt:16 (find_package)
[1.215s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.215s] [0m
[1.219s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem date_time program_options thread
[1.266s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[1.321s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[1.321s] -- Configured cppcheck include dirs: /home/<USER>/Code/ws_0/src/arm_controller_pkg/include
[1.321s] -- Configured cppcheck exclude dirs and/or files: 
[1.322s] -- Added test 'lint_cmake' to check CMake code style
[1.322s] -- Added test 'uncrustify' to check C / C++ code style
[1.322s] -- Configured uncrustify additional arguments: 
[1.322s] -- Added test 'xmllint' to check XML markup files
[1.324s] -- Configuring done (1.2s)
[1.390s] -- Generating done (0.1s)
[1.396s] -- Build files have been written to: /home/<USER>/Code/ws_0/build/arm_controller_pkg
[1.433s] [ 25%] [32mBuilding CXX object CMakeFiles/RB_builder_node.dir/src/RB_builder_node.cpp.o[0m
[1.433s] [ 75%] Built target path_planning_node
[11.559s] [100%] [32m[1mLinking CXX executable RB_builder_node[0m
[12.269s] [100%] Built target RB_builder_node
[12.288s] Invoked command in '/home/<USER>/Code/ws_0/build/arm_controller_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/arm_controller_pkg -- -j8 -l8
[12.294s] Invoking command in '/home/<USER>/Code/ws_0/build/arm_controller_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/arm_controller_pkg
[12.299s] -- Install configuration: ""
[12.300s] -- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/lib/arm_controller_pkg/path_planning_node
[12.301s] -- Installing: /home/<USER>/Code/ws_0/install/arm_controller_pkg/lib/arm_controller_pkg/RB_builder_node
[12.303s] -- Set non-toolchain portion of runtime path of "/home/<USER>/Code/ws_0/install/arm_controller_pkg/lib/arm_controller_pkg/RB_builder_node" to ""
[12.303s] -- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/ament_index/resource_index/package_run_dependencies/arm_controller_pkg
[12.303s] -- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/ament_index/resource_index/parent_prefix_path/arm_controller_pkg
[12.303s] -- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/environment/ament_prefix_path.sh
[12.303s] -- Installing: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/environment/ament_prefix_path.dsv
[12.303s] -- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/environment/path.sh
[12.303s] -- Installing: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/environment/path.dsv
[12.304s] -- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/local_setup.bash
[12.304s] -- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/local_setup.sh
[12.304s] -- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/local_setup.zsh
[12.304s] -- Installing: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/local_setup.dsv
[12.304s] -- Installing: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.dsv
[12.305s] -- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/ament_index/resource_index/packages/arm_controller_pkg
[12.305s] -- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/cmake/arm_controller_pkgConfig.cmake
[12.305s] -- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/cmake/arm_controller_pkgConfig-version.cmake
[12.305s] -- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.xml
[12.306s] Invoked command in '/home/<USER>/Code/ws_0/build/arm_controller_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/arm_controller_pkg
