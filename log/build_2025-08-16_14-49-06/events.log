[0.000000] (-) TimerEvent: {}
[0.000151] (-) JobUnselected: {'identifier': 'engineer_vision_pkg'}
[0.000191] (-) JobUnselected: {'identifier': 'mindvision_camera'}
[0.000217] (-) JobUnselected: {'identifier': 'rm_vision'}
[0.000228] (-) JobUnselected: {'identifier': 'robot_description'}
[0.000248] (arm_controller_pkg) JobQueued: {'identifier': 'arm_controller_pkg', 'dependencies': OrderedDict()}
[0.000279] (arm_controller_pkg) JobStarted: {'identifier': 'arm_controller_pkg'}
[0.009546] (arm_controller_pkg) JobProgress: {'identifier': 'arm_controller_pkg', 'progress': 'cmake'}
[0.010723] (arm_controller_pkg) JobProgress: {'identifier': 'arm_controller_pkg', 'progress': 'build'}
[0.011216] (arm_controller_pkg) Command: {'cmd': ['/usr/local/cmake/bin/cmake', '--build', '/home/<USER>/Code/ws_0/build/arm_controller_pkg', '--', '-j8', '-l8'], 'cwd': '/home/<USER>/Code/ws_0/build/arm_controller_pkg', 'env': OrderedDict([('USER', 'mac'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner_testutils/lib:/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner/lib:/home/<USER>/moveit2_ws/install/moveit_visual_tools/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_visualization/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_demo/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_capabilities/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_core/lib:/home/<USER>/moveit2_ws/install/moveit_ros_control_interface/lib:/home/<USER>/moveit2_ws/install/moveit_simple_controller_manager/lib:/home/<USER>/moveit2_ws/install/moveit_setup_assistant/lib:/home/<USER>/moveit2_ws/install/moveit_setup_srdf_plugins/lib:/home/<USER>/moveit2_ws/install/moveit_setup_core_plugins/lib:/home/<USER>/moveit2_ws/install/moveit_setup_controllers/lib:/home/<USER>/moveit2_ws/install/moveit_setup_app_plugins/lib:/home/<USER>/moveit2_ws/install/moveit_setup_framework/lib:/home/<USER>/moveit2_ws/install/moveit_servo/lib:/home/<USER>/moveit2_ws/install/moveit_ros_visualization/lib:/home/<USER>/moveit2_ws/install/moveit_hybrid_planning/lib:/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/lib:/home/<USER>/moveit2_ws/install/moveit_ros_benchmarks/lib:/home/<USER>/moveit2_ws/install/moveit_ros_warehouse/lib:/home/<USER>/moveit2_ws/install/moveit_ros_robot_interaction/lib:/home/<USER>/moveit2_ws/install/moveit_ros_perception/lib:/home/<USER>/moveit2_ws/install/moveit_ros_move_group/lib:/home/<USER>/moveit2_ws/install/moveit_planners_ompl/lib:/home/<USER>/moveit2_ws/install/moveit_ros_planning/lib:/home/<USER>/moveit2_ws/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/moveit2_ws/install/moveit_planners_chomp/lib:/home/<USER>/moveit2_ws/install/moveit_kinematics/lib:/home/<USER>/moveit2_ws/install/moveit_chomp_optimizer_adapter/lib:/home/<USER>/moveit2_ws/install/chomp_motion_planner/lib:/home/<USER>/moveit2_ws/install/moveit_core/lib:/home/<USER>/moveit2_ws/install/srdfdom/lib:/home/<USER>/moveit2_ws/install/rviz_marker_tools/lib:/home/<USER>/moveit2_ws/install/rosparam_shortcuts/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_msgs/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/bin/helpers/browser.sh'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/Code/ws_0'), ('TERM_PROGRAM_VERSION', '1.103.1'), ('VSCODE_IPC_HOOK_CLI', '/run/user/502/vscode-ipc-a99ed460-a2fd-4c9f-a1f6-4965dc064233.sock'), ('ROS_PYTHON_VERSION', '3'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/502/bus'), ('COLORTERM', 'truecolor'), ('COLCON_PREFIX_PATH', '/home/<USER>/moveit2_ws/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mac'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/home/<USER>/moveit2_ws/install/moveit_core/bin:/opt/ros/humble/bin:/usr/local/cmake/bin:/opt/orbstack-guest/bin-hiprio:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/opt/orbstack-guest/bin:/opt/orbstack-guest/data/bin/cmdlinks'), ('PYTHON_EXECUTABLE', '/usr/bin/python3.10'), ('XDG_RUNTIME_DIR', '/run/user/502'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-ec2f90e73ca2d53c.txt'), ('LANG', 'C.UTF-8'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/502/vscode-git-4bcffef955.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AUTH_SOCK', '/run/user/502/vscode-ssh-auth-sock-93286558'), ('AMENT_PREFIX_PATH', '/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner_testutils:/home/<USER>/moveit2_ws/install/moveit_runtime:/home/<USER>/moveit2_ws/install/moveit2_tutorials:/home/<USER>/moveit2_ws/install/moveit:/home/<USER>/moveit2_ws/install/moveit_planners:/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner:/home/<USER>/moveit2_ws/install/moveit_test:/home/<USER>/moveit2_ws/install/moveit_visual_tools:/home/<USER>/moveit2_ws/install/moveit_task_constructor_visualization:/home/<USER>/moveit2_ws/install/moveit_task_constructor_demo:/home/<USER>/moveit2_ws/install/moveit_task_constructor_capabilities:/home/<USER>/moveit2_ws/install/moveit_task_constructor_core:/home/<USER>/moveit2_ws/install/moveit_ros_control_interface:/home/<USER>/moveit2_ws/install/moveit_plugins:/home/<USER>/moveit2_ws/install/moveit_simple_controller_manager:/home/<USER>/moveit2_ws/install/moveit_setup_assistant:/home/<USER>/moveit2_ws/install/moveit_setup_srdf_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_core_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_controllers:/home/<USER>/moveit2_ws/install/moveit_setup_app_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_framework:/home/<USER>/moveit2_ws/install/moveit_servo:/home/<USER>/moveit2_ws/install/moveit_ros:/home/<USER>/moveit2_ws/install/moveit_ros_visualization:/home/<USER>/moveit2_ws/install/moveit_hybrid_planning:/home/<USER>/moveit2_ws/install/move_program:/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface:/home/<USER>/moveit2_ws/install/moveit_ros_benchmarks:/home/<USER>/moveit2_ws/install/moveit_ros_warehouse:/home/<USER>/moveit2_ws/install/moveit_ros_robot_interaction:/home/<USER>/moveit2_ws/install/moveit_ros_perception:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_pg70_support:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_moveit_config:/home/<USER>/moveit2_ws/install/moveit_ros_move_group:/home/<USER>/moveit2_ws/install/moveit_planners_ompl:/home/<USER>/moveit2_ws/install/moveit_ros_planning:/home/<USER>/moveit2_ws/install/moveit_ros_occupancy_map_monitor:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/moveit2_ws/install/moveit_planners_chomp:/home/<USER>/moveit2_ws/install/moveit_kinematics:/home/<USER>/moveit2_ws/install/moveit_chomp_optimizer_adapter:/home/<USER>/moveit2_ws/install/chomp_motion_planner:/home/<USER>/moveit2_ws/install/moveit_core:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/srdfdom:/home/<USER>/moveit2_ws/install/rviz_marker_tools:/home/<USER>/moveit2_ws/install/rosparam_shortcuts:/home/<USER>/moveit2_ws/install/moveit_task_constructor_msgs:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_support:/home/<USER>/moveit2_ws/install/moveit_resources:/home/<USER>/moveit2_ws/install/moveit_resources_pr2_description:/home/<USER>/moveit2_ws/install/moveit_resources_panda_moveit_config:/home/<USER>/moveit2_ws/install/moveit_resources_panda_description:/home/<USER>/moveit2_ws/install/moveit_resources_fanuc_moveit_config:/home/<USER>/moveit2_ws/install/moveit_resources_fanuc_description:/home/<USER>/moveit2_ws/install/moveit_common:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('CMAKE_ARGS', '-DPYTHON_EXECUTABLE=/usr/bin/python3.10 -DPython3_EXECUTABLE=/usr/bin/python3.10'), ('CMAKE_MODULE_PATH', '/home/<USER>/moveit2_ws/install/moveit_task_constructor_core/share/moveit_task_constructor_core/cmake'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('PWD', '/home/<USER>/Code/ws_0/build/arm_controller_pkg'), ('SSH_CONNECTION', '::1 0 ::1 22'), ('XDG_DATA_DIRS', '/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/moveit2_ws/install/moveit_task_constructor_core/local/lib/python3.10/dist-packages:/home/<USER>/moveit2_ws/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/moveit2_ws/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/moveit2_ws/install/moveit_task_constructor_msgs/local/lib/python3.10/dist-packages:/home/<USER>/moveit2_ws/install/launch_param_builder/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('Python3_EXECUTABLE', '/usr/bin/python3.10'), ('CMAKE_PREFIX_PATH', '/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner_testutils:/home/<USER>/moveit2_ws/install/moveit_runtime:/home/<USER>/moveit2_ws/install/moveit2_tutorials:/home/<USER>/moveit2_ws/install/moveit:/home/<USER>/moveit2_ws/install/moveit_planners:/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner:/home/<USER>/moveit2_ws/install/moveit_test:/home/<USER>/moveit2_ws/install/moveit_visual_tools:/home/<USER>/moveit2_ws/install/moveit_task_constructor_visualization:/home/<USER>/moveit2_ws/install/moveit_task_constructor_demo:/home/<USER>/moveit2_ws/install/moveit_task_constructor_capabilities:/home/<USER>/moveit2_ws/install/moveit_task_constructor_core:/home/<USER>/moveit2_ws/install/moveit_ros_control_interface:/home/<USER>/moveit2_ws/install/moveit_plugins:/home/<USER>/moveit2_ws/install/moveit_simple_controller_manager:/home/<USER>/moveit2_ws/install/moveit_setup_assistant:/home/<USER>/moveit2_ws/install/moveit_setup_srdf_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_core_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_controllers:/home/<USER>/moveit2_ws/install/moveit_setup_app_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_framework:/home/<USER>/moveit2_ws/install/moveit_servo:/home/<USER>/moveit2_ws/install/moveit_ros:/home/<USER>/moveit2_ws/install/moveit_ros_visualization:/home/<USER>/moveit2_ws/install/moveit_hybrid_planning:/home/<USER>/moveit2_ws/install/move_program:/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface:/home/<USER>/moveit2_ws/install/moveit_ros_benchmarks:/home/<USER>/moveit2_ws/install/moveit_ros_warehouse:/home/<USER>/moveit2_ws/install/moveit_ros_robot_interaction:/home/<USER>/moveit2_ws/install/moveit_ros_perception:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_pg70_support:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_moveit_config:/home/<USER>/moveit2_ws/install/moveit_ros_move_group:/home/<USER>/moveit2_ws/install/moveit_planners_ompl:/home/<USER>/moveit2_ws/install/moveit_ros_planning:/home/<USER>/moveit2_ws/install/moveit_ros_occupancy_map_monitor:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/moveit2_ws/install/moveit_planners_chomp:/home/<USER>/moveit2_ws/install/moveit_kinematics:/home/<USER>/moveit2_ws/install/moveit_chomp_optimizer_adapter:/home/<USER>/moveit2_ws/install/chomp_motion_planner:/home/<USER>/moveit2_ws/install/moveit_core:/home/<USER>/moveit2_ws/install/srdfdom:/home/<USER>/moveit2_ws/install/rviz_marker_tools:/home/<USER>/moveit2_ws/install/rosparam_shortcuts:/home/<USER>/moveit2_ws/install/moveit_task_constructor_msgs:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_support:/home/<USER>/moveit2_ws/install/moveit_resources:/home/<USER>/moveit2_ws/install/moveit_resources_pr2_description:/home/<USER>/moveit2_ws/install/moveit_resources_panda_moveit_config:/home/<USER>/moveit2_ws/install/moveit_resources_panda_description:/home/<USER>/moveit2_ws/install/moveit_resources_fanuc_moveit_config:/home/<USER>/moveit2_ws/install/moveit_resources_fanuc_description:/home/<USER>/moveit2_ws/install/moveit_common:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble')]), 'shell': False}
[0.102162] (-) TimerEvent: {}
[0.107477] (arm_controller_pkg) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.204999] (-) TimerEvent: {}
[0.269558] (arm_controller_pkg) StdoutLine: {'line': b'-- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)\n'}
[0.305206] (-) TimerEvent: {}
[0.316272] (arm_controller_pkg) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[0.320865] (arm_controller_pkg) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[0.326827] (arm_controller_pkg) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[0.339143] (arm_controller_pkg) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.353615] (arm_controller_pkg) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.389213] (arm_controller_pkg) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[0.392577] (arm_controller_pkg) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[0.405702] (-) TimerEvent: {}
[0.506305] (-) TimerEvent: {}
[0.609822] (-) TimerEvent: {}
[0.660874] (arm_controller_pkg) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[0.710289] (-) TimerEvent: {}
[0.748615] (arm_controller_pkg) StdoutLine: {'line': b'-- Found tf2: 0.25.15 (/opt/ros/humble/share/tf2/cmake)\n'}
[0.764886] (arm_controller_pkg) StdoutLine: {'line': b'-- Found tf2_geometry_msgs: 0.25.15 (/opt/ros/humble/share/tf2_geometry_msgs/cmake)\n'}
[0.769152] (arm_controller_pkg) StdoutLine: {'line': b'-- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)\n'}
[0.770848] (arm_controller_pkg) StdoutLine: {'line': b'-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target\n'}
[0.812252] (-) TimerEvent: {}
[0.845640] (arm_controller_pkg) StdoutLine: {'line': b'-- Found moveit_ros_planning_interface: 2.5.9 (/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake)\n'}
[0.846565] (arm_controller_pkg) StderrLine: {'line': b'\x1b[33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ConfigExtras.cmake:9 (find_package):\n'}
[0.846676] (arm_controller_pkg) StderrLine: {'line': b'  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake\n'}
[0.846712] (arm_controller_pkg) StderrLine: {'line': b'  --help-policy CMP0167" for policy details.  Use the cmake_policy command to\n'}
[0.846739] (arm_controller_pkg) StderrLine: {'line': b'  set the policy and suppress this warning.\n'}
[0.846765] (arm_controller_pkg) StderrLine: {'line': b'\n'}
[0.846790] (arm_controller_pkg) StderrLine: {'line': b'Call Stack (most recent call first):\n'}
[0.846815] (arm_controller_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)\n'}
[0.846841] (arm_controller_pkg) StderrLine: {'line': b'  CMakeLists.txt:16 (find_package)\n'}
[0.846865] (arm_controller_pkg) StderrLine: {'line': b'This warning is for project developers.  Use -Wno-dev to suppress it.\n'}
[0.846891] (arm_controller_pkg) StderrLine: {'line': b'\x1b[0m\n'}
[0.859336] (arm_controller_pkg) StdoutLine: {'line': b'-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: date_time filesystem program_options system thread\n'}
[0.913246] (-) TimerEvent: {}
[0.973181] (arm_controller_pkg) StderrLine: {'line': b'\x1b[33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_core/share/moveit_core/cmake/ConfigExtras.cmake:3 (find_package):\n'}
[0.973313] (arm_controller_pkg) StderrLine: {'line': b'  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake\n'}
[0.973344] (arm_controller_pkg) StderrLine: {'line': b'  --help-policy CMP0167" for policy details.  Use the cmake_policy command to\n'}
[0.973372] (arm_controller_pkg) StderrLine: {'line': b'  set the policy and suppress this warning.\n'}
[0.973397] (arm_controller_pkg) StderrLine: {'line': b'\n'}
[0.973422] (arm_controller_pkg) StderrLine: {'line': b'Call Stack (most recent call first):\n'}
[0.973446] (arm_controller_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)\n'}
[0.973472] (arm_controller_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)\n'}
[0.973498] (arm_controller_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)\n'}
[0.973523] (arm_controller_pkg) StderrLine: {'line': b'  CMakeLists.txt:16 (find_package)\n'}
[0.973547] (arm_controller_pkg) StderrLine: {'line': b'This warning is for project developers.  Use -Wno-dev to suppress it.\n'}
[0.973572] (arm_controller_pkg) StderrLine: {'line': b'\x1b[0m\n'}
[0.980911] (arm_controller_pkg) StdoutLine: {'line': b'-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: chrono date_time filesystem iostreams program_options regex serialization system thread\n'}
[1.015551] (-) TimerEvent: {}
[1.015758] (arm_controller_pkg) StdoutLine: {'line': b'-- Found parameter_traits: 0.5.0 (/opt/ros/humble/share/parameter_traits/cmake)\n'}
[1.025232] (arm_controller_pkg) StdoutLine: {'line': b'-- Found rclcpp_lifecycle: 16.0.14 (/opt/ros/humble/share/rclcpp_lifecycle/cmake)\n'}
[1.047189] (arm_controller_pkg) StderrLine: {'line': b'\x1b[33mCMake Warning (dev) at /opt/ros/humble/share/geometric_shapes/cmake/ConfigExtras.cmake:31 (find_package):\n'}
[1.047328] (arm_controller_pkg) StderrLine: {'line': b'  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake\n'}
[1.047363] (arm_controller_pkg) StderrLine: {'line': b'  --help-policy CMP0167" for policy details.  Use the cmake_policy command to\n'}
[1.047392] (arm_controller_pkg) StderrLine: {'line': b'  set the policy and suppress this warning.\n'}
[1.047418] (arm_controller_pkg) StderrLine: {'line': b'\n'}
[1.047443] (arm_controller_pkg) StderrLine: {'line': b'Call Stack (most recent call first):\n'}
[1.047468] (arm_controller_pkg) StderrLine: {'line': b'  /opt/ros/humble/share/geometric_shapes/cmake/geometric_shapesConfig.cmake:41 (include)\n'}
[1.047493] (arm_controller_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_core/share/moveit_core/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)\n'}
[1.047519] (arm_controller_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)\n'}
[1.047544] (arm_controller_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)\n'}
[1.047576] (arm_controller_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)\n'}
[1.047604] (arm_controller_pkg) StderrLine: {'line': b'  CMakeLists.txt:16 (find_package)\n'}
[1.047630] (arm_controller_pkg) StderrLine: {'line': b'This warning is for project developers.  Use -Wno-dev to suppress it.\n'}
[1.047656] (arm_controller_pkg) StderrLine: {'line': b'\x1b[0m\n'}
[1.052215] (arm_controller_pkg) StdoutLine: {'line': b'-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: filesystem\n'}
[1.096345] (arm_controller_pkg) StdoutLine: {'line': b'-- library: /usr/lib/aarch64-linux-gnu/libcurl.so\n'}
[1.116258] (-) TimerEvent: {}
[1.165925] (arm_controller_pkg) StderrLine: {'line': b'\x1b[33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_ros_planning/share/moveit_ros_planning/cmake/ConfigExtras.cmake:3 (find_package):\n'}
[1.166059] (arm_controller_pkg) StderrLine: {'line': b'  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake\n'}
[1.166091] (arm_controller_pkg) StderrLine: {'line': b'  --help-policy CMP0167" for policy details.  Use the cmake_policy command to\n'}
[1.166119] (arm_controller_pkg) StderrLine: {'line': b'  set the policy and suppress this warning.\n'}
[1.166145] (arm_controller_pkg) StderrLine: {'line': b'\n'}
[1.166171] (arm_controller_pkg) StderrLine: {'line': b'Call Stack (most recent call first):\n'}
[1.166202] (arm_controller_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_planning/share/moveit_ros_planning/cmake/moveit_ros_planningConfig.cmake:41 (include)\n'}
[1.166229] (arm_controller_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)\n'}
[1.166256] (arm_controller_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)\n'}
[1.166281] (arm_controller_pkg) StderrLine: {'line': b'  CMakeLists.txt:16 (find_package)\n'}
[1.166306] (arm_controller_pkg) StderrLine: {'line': b'This warning is for project developers.  Use -Wno-dev to suppress it.\n'}
[1.166330] (arm_controller_pkg) StderrLine: {'line': b'\x1b[0m\n'}
[1.170214] (arm_controller_pkg) StdoutLine: {'line': b'-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem date_time program_options thread chrono\n'}
[1.196397] (arm_controller_pkg) StderrLine: {'line': b'\x1b[33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_ros_warehouse/share/moveit_ros_warehouse/cmake/ConfigExtras.cmake:3 (find_package):\n'}
[1.196691] (arm_controller_pkg) StderrLine: {'line': b'  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake\n'}
[1.196790] (arm_controller_pkg) StderrLine: {'line': b'  --help-policy CMP0167" for policy details.  Use the cmake_policy command to\n'}
[1.196883] (arm_controller_pkg) StderrLine: {'line': b'  set the policy and suppress this warning.\n'}
[1.196974] (arm_controller_pkg) StderrLine: {'line': b'\n'}
[1.197063] (arm_controller_pkg) StderrLine: {'line': b'Call Stack (most recent call first):\n'}
[1.197154] (arm_controller_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_warehouse/share/moveit_ros_warehouse/cmake/moveit_ros_warehouseConfig.cmake:41 (include)\n'}
[1.197298] (arm_controller_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)\n'}
[1.197404] (arm_controller_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)\n'}
[1.197504] (arm_controller_pkg) StderrLine: {'line': b'  CMakeLists.txt:16 (find_package)\n'}
[1.197601] (arm_controller_pkg) StderrLine: {'line': b'This warning is for project developers.  Use -Wno-dev to suppress it.\n'}
[1.197734] (arm_controller_pkg) StderrLine: {'line': b'\x1b[0m\n'}
[1.200095] (arm_controller_pkg) StdoutLine: {'line': b'-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: thread system filesystem regex date_time program_options\n'}
[1.214606] (arm_controller_pkg) StderrLine: {'line': b'\x1b[33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_ros_move_group/share/moveit_ros_move_group/cmake/ConfigExtras.cmake:3 (find_package):\n'}
[1.214709] (arm_controller_pkg) StderrLine: {'line': b'  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake\n'}
[1.214753] (arm_controller_pkg) StderrLine: {'line': b'  --help-policy CMP0167" for policy details.  Use the cmake_policy command to\n'}
[1.214783] (arm_controller_pkg) StderrLine: {'line': b'  set the policy and suppress this warning.\n'}
[1.214809] (arm_controller_pkg) StderrLine: {'line': b'\n'}
[1.214835] (arm_controller_pkg) StderrLine: {'line': b'Call Stack (most recent call first):\n'}
[1.214860] (arm_controller_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_move_group/share/moveit_ros_move_group/cmake/moveit_ros_move_groupConfig.cmake:41 (include)\n'}
[1.214886] (arm_controller_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)\n'}
[1.214915] (arm_controller_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)\n'}
[1.214940] (arm_controller_pkg) StderrLine: {'line': b'  CMakeLists.txt:16 (find_package)\n'}
[1.214964] (arm_controller_pkg) StderrLine: {'line': b'This warning is for project developers.  Use -Wno-dev to suppress it.\n'}
[1.214989] (arm_controller_pkg) StderrLine: {'line': b'\x1b[0m\n'}
[1.219357] (-) TimerEvent: {}
[1.219566] (arm_controller_pkg) StdoutLine: {'line': b'-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem date_time program_options thread\n'}
[1.266050] (arm_controller_pkg) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)\n'}
[1.319481] (-) TimerEvent: {}
[1.321085] (arm_controller_pkg) StdoutLine: {'line': b"-- Added test 'cppcheck' to perform static code analysis on C / C++ code\n"}
[1.321419] (arm_controller_pkg) StdoutLine: {'line': b'-- Configured cppcheck include dirs: /home/<USER>/Code/ws_0/src/arm_controller_pkg/include\n'}
[1.321483] (arm_controller_pkg) StdoutLine: {'line': b'-- Configured cppcheck exclude dirs and/or files: \n'}
[1.321836] (arm_controller_pkg) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[1.322373] (arm_controller_pkg) StdoutLine: {'line': b"-- Added test 'uncrustify' to check C / C++ code style\n"}
[1.322410] (arm_controller_pkg) StdoutLine: {'line': b'-- Configured uncrustify additional arguments: \n'}
[1.322591] (arm_controller_pkg) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[1.324019] (arm_controller_pkg) StdoutLine: {'line': b'-- Configuring done (1.2s)\n'}
[1.390162] (arm_controller_pkg) StdoutLine: {'line': b'-- Generating done (0.1s)\n'}
[1.396617] (arm_controller_pkg) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/Code/ws_0/build/arm_controller_pkg\n'}
[1.423202] (-) TimerEvent: {}
[1.433463] (arm_controller_pkg) StdoutLine: {'line': b'[ 25%] \x1b[32mBuilding CXX object CMakeFiles/RB_builder_node.dir/src/RB_builder_node.cpp.o\x1b[0m\n'}
[1.433589] (arm_controller_pkg) StdoutLine: {'line': b'[ 75%] Built target path_planning_node\n'}
[1.523437] (-) TimerEvent: {}
[1.625147] (-) TimerEvent: {}
[1.725607] (-) TimerEvent: {}
[1.828373] (-) TimerEvent: {}
[1.929263] (-) TimerEvent: {}
[2.031372] (-) TimerEvent: {}
[2.132251] (-) TimerEvent: {}
[2.232576] (-) TimerEvent: {}
[2.333351] (-) TimerEvent: {}
[2.433717] (-) TimerEvent: {}
[2.536160] (-) TimerEvent: {}
[2.638267] (-) TimerEvent: {}
[2.738571] (-) TimerEvent: {}
[2.839769] (-) TimerEvent: {}
[2.940459] (-) TimerEvent: {}
[3.044358] (-) TimerEvent: {}
[3.147263] (-) TimerEvent: {}
[3.247626] (-) TimerEvent: {}
[3.351158] (-) TimerEvent: {}
[3.451758] (-) TimerEvent: {}
[3.552782] (-) TimerEvent: {}
[3.655426] (-) TimerEvent: {}
[3.759274] (-) TimerEvent: {}
[3.860284] (-) TimerEvent: {}
[3.961007] (-) TimerEvent: {}
[4.063829] (-) TimerEvent: {}
[4.165786] (-) TimerEvent: {}
[4.269287] (-) TimerEvent: {}
[4.369588] (-) TimerEvent: {}
[4.469839] (-) TimerEvent: {}
[4.570970] (-) TimerEvent: {}
[4.672113] (-) TimerEvent: {}
[4.773636] (-) TimerEvent: {}
[4.874737] (-) TimerEvent: {}
[4.981375] (-) TimerEvent: {}
[5.081926] (-) TimerEvent: {}
[5.183524] (-) TimerEvent: {}
[5.284493] (-) TimerEvent: {}
[5.387846] (-) TimerEvent: {}
[5.489466] (-) TimerEvent: {}
[5.593349] (-) TimerEvent: {}
[5.696257] (-) TimerEvent: {}
[5.796547] (-) TimerEvent: {}
[5.897245] (-) TimerEvent: {}
[5.997826] (-) TimerEvent: {}
[6.098504] (-) TimerEvent: {}
[6.201385] (-) TimerEvent: {}
[6.303600] (-) TimerEvent: {}
[6.404424] (-) TimerEvent: {}
[6.504878] (-) TimerEvent: {}
[6.606500] (-) TimerEvent: {}
[6.707279] (-) TimerEvent: {}
[6.808256] (-) TimerEvent: {}
[6.909626] (-) TimerEvent: {}
[7.014961] (-) TimerEvent: {}
[7.116254] (-) TimerEvent: {}
[7.216589] (-) TimerEvent: {}
[7.316921] (-) TimerEvent: {}
[7.420267] (-) TimerEvent: {}
[7.522179] (-) TimerEvent: {}
[7.631419] (-) TimerEvent: {}
[7.733836] (-) TimerEvent: {}
[7.835361] (-) TimerEvent: {}
[7.937279] (-) TimerEvent: {}
[8.040144] (-) TimerEvent: {}
[8.142475] (-) TimerEvent: {}
[8.248067] (-) TimerEvent: {}
[8.349307] (-) TimerEvent: {}
[8.449914] (-) TimerEvent: {}
[8.554586] (-) TimerEvent: {}
[8.657298] (-) TimerEvent: {}
[8.757984] (-) TimerEvent: {}
[8.862926] (-) TimerEvent: {}
[8.964258] (-) TimerEvent: {}
[9.066925] (-) TimerEvent: {}
[9.167397] (-) TimerEvent: {}
[9.268486] (-) TimerEvent: {}
[9.369412] (-) TimerEvent: {}
[9.472255] (-) TimerEvent: {}
[9.573687] (-) TimerEvent: {}
[9.676267] (-) TimerEvent: {}
[9.781113] (-) TimerEvent: {}
[9.881676] (-) TimerEvent: {}
[9.984226] (-) TimerEvent: {}
[10.085308] (-) TimerEvent: {}
[10.185862] (-) TimerEvent: {}
[10.286492] (-) TimerEvent: {}
[10.388524] (-) TimerEvent: {}
[10.489001] (-) TimerEvent: {}
[10.592125] (-) TimerEvent: {}
[10.693081] (-) TimerEvent: {}
[10.794523] (-) TimerEvent: {}
[10.895380] (-) TimerEvent: {}
[10.999251] (-) TimerEvent: {}
[11.101697] (-) TimerEvent: {}
[11.203017] (-) TimerEvent: {}
[11.306473] (-) TimerEvent: {}
[11.406777] (-) TimerEvent: {}
[11.507036] (-) TimerEvent: {}
[11.559030] (arm_controller_pkg) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX executable RB_builder_node\x1b[0m\n'}
[11.607394] (-) TimerEvent: {}
[11.707838] (-) TimerEvent: {}
[11.809384] (-) TimerEvent: {}
[11.911053] (-) TimerEvent: {}
[12.015367] (-) TimerEvent: {}
[12.116171] (-) TimerEvent: {}
[12.217311] (-) TimerEvent: {}
[12.269180] (arm_controller_pkg) StdoutLine: {'line': b'[100%] Built target RB_builder_node\n'}
[12.288041] (arm_controller_pkg) CommandEnded: {'returncode': 0}
[12.289180] (arm_controller_pkg) JobProgress: {'identifier': 'arm_controller_pkg', 'progress': 'install'}
[12.293629] (arm_controller_pkg) Command: {'cmd': ['/usr/local/cmake/bin/cmake', '--install', '/home/<USER>/Code/ws_0/build/arm_controller_pkg'], 'cwd': '/home/<USER>/Code/ws_0/build/arm_controller_pkg', 'env': OrderedDict([('USER', 'mac'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner_testutils/lib:/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner/lib:/home/<USER>/moveit2_ws/install/moveit_visual_tools/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_visualization/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_demo/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_capabilities/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_core/lib:/home/<USER>/moveit2_ws/install/moveit_ros_control_interface/lib:/home/<USER>/moveit2_ws/install/moveit_simple_controller_manager/lib:/home/<USER>/moveit2_ws/install/moveit_setup_assistant/lib:/home/<USER>/moveit2_ws/install/moveit_setup_srdf_plugins/lib:/home/<USER>/moveit2_ws/install/moveit_setup_core_plugins/lib:/home/<USER>/moveit2_ws/install/moveit_setup_controllers/lib:/home/<USER>/moveit2_ws/install/moveit_setup_app_plugins/lib:/home/<USER>/moveit2_ws/install/moveit_setup_framework/lib:/home/<USER>/moveit2_ws/install/moveit_servo/lib:/home/<USER>/moveit2_ws/install/moveit_ros_visualization/lib:/home/<USER>/moveit2_ws/install/moveit_hybrid_planning/lib:/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/lib:/home/<USER>/moveit2_ws/install/moveit_ros_benchmarks/lib:/home/<USER>/moveit2_ws/install/moveit_ros_warehouse/lib:/home/<USER>/moveit2_ws/install/moveit_ros_robot_interaction/lib:/home/<USER>/moveit2_ws/install/moveit_ros_perception/lib:/home/<USER>/moveit2_ws/install/moveit_ros_move_group/lib:/home/<USER>/moveit2_ws/install/moveit_planners_ompl/lib:/home/<USER>/moveit2_ws/install/moveit_ros_planning/lib:/home/<USER>/moveit2_ws/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/moveit2_ws/install/moveit_planners_chomp/lib:/home/<USER>/moveit2_ws/install/moveit_kinematics/lib:/home/<USER>/moveit2_ws/install/moveit_chomp_optimizer_adapter/lib:/home/<USER>/moveit2_ws/install/chomp_motion_planner/lib:/home/<USER>/moveit2_ws/install/moveit_core/lib:/home/<USER>/moveit2_ws/install/srdfdom/lib:/home/<USER>/moveit2_ws/install/rviz_marker_tools/lib:/home/<USER>/moveit2_ws/install/rosparam_shortcuts/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_msgs/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/bin/helpers/browser.sh'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/Code/ws_0'), ('TERM_PROGRAM_VERSION', '1.103.1'), ('VSCODE_IPC_HOOK_CLI', '/run/user/502/vscode-ipc-a99ed460-a2fd-4c9f-a1f6-4965dc064233.sock'), ('ROS_PYTHON_VERSION', '3'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/502/bus'), ('COLORTERM', 'truecolor'), ('COLCON_PREFIX_PATH', '/home/<USER>/moveit2_ws/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mac'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/home/<USER>/moveit2_ws/install/moveit_core/bin:/opt/ros/humble/bin:/usr/local/cmake/bin:/opt/orbstack-guest/bin-hiprio:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/opt/orbstack-guest/bin:/opt/orbstack-guest/data/bin/cmdlinks'), ('PYTHON_EXECUTABLE', '/usr/bin/python3.10'), ('XDG_RUNTIME_DIR', '/run/user/502'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-ec2f90e73ca2d53c.txt'), ('LANG', 'C.UTF-8'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/502/vscode-git-4bcffef955.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AUTH_SOCK', '/run/user/502/vscode-ssh-auth-sock-93286558'), ('AMENT_PREFIX_PATH', '/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner_testutils:/home/<USER>/moveit2_ws/install/moveit_runtime:/home/<USER>/moveit2_ws/install/moveit2_tutorials:/home/<USER>/moveit2_ws/install/moveit:/home/<USER>/moveit2_ws/install/moveit_planners:/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner:/home/<USER>/moveit2_ws/install/moveit_test:/home/<USER>/moveit2_ws/install/moveit_visual_tools:/home/<USER>/moveit2_ws/install/moveit_task_constructor_visualization:/home/<USER>/moveit2_ws/install/moveit_task_constructor_demo:/home/<USER>/moveit2_ws/install/moveit_task_constructor_capabilities:/home/<USER>/moveit2_ws/install/moveit_task_constructor_core:/home/<USER>/moveit2_ws/install/moveit_ros_control_interface:/home/<USER>/moveit2_ws/install/moveit_plugins:/home/<USER>/moveit2_ws/install/moveit_simple_controller_manager:/home/<USER>/moveit2_ws/install/moveit_setup_assistant:/home/<USER>/moveit2_ws/install/moveit_setup_srdf_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_core_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_controllers:/home/<USER>/moveit2_ws/install/moveit_setup_app_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_framework:/home/<USER>/moveit2_ws/install/moveit_servo:/home/<USER>/moveit2_ws/install/moveit_ros:/home/<USER>/moveit2_ws/install/moveit_ros_visualization:/home/<USER>/moveit2_ws/install/moveit_hybrid_planning:/home/<USER>/moveit2_ws/install/move_program:/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface:/home/<USER>/moveit2_ws/install/moveit_ros_benchmarks:/home/<USER>/moveit2_ws/install/moveit_ros_warehouse:/home/<USER>/moveit2_ws/install/moveit_ros_robot_interaction:/home/<USER>/moveit2_ws/install/moveit_ros_perception:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_pg70_support:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_moveit_config:/home/<USER>/moveit2_ws/install/moveit_ros_move_group:/home/<USER>/moveit2_ws/install/moveit_planners_ompl:/home/<USER>/moveit2_ws/install/moveit_ros_planning:/home/<USER>/moveit2_ws/install/moveit_ros_occupancy_map_monitor:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/moveit2_ws/install/moveit_planners_chomp:/home/<USER>/moveit2_ws/install/moveit_kinematics:/home/<USER>/moveit2_ws/install/moveit_chomp_optimizer_adapter:/home/<USER>/moveit2_ws/install/chomp_motion_planner:/home/<USER>/moveit2_ws/install/moveit_core:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/srdfdom:/home/<USER>/moveit2_ws/install/rviz_marker_tools:/home/<USER>/moveit2_ws/install/rosparam_shortcuts:/home/<USER>/moveit2_ws/install/moveit_task_constructor_msgs:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_support:/home/<USER>/moveit2_ws/install/moveit_resources:/home/<USER>/moveit2_ws/install/moveit_resources_pr2_description:/home/<USER>/moveit2_ws/install/moveit_resources_panda_moveit_config:/home/<USER>/moveit2_ws/install/moveit_resources_panda_description:/home/<USER>/moveit2_ws/install/moveit_resources_fanuc_moveit_config:/home/<USER>/moveit2_ws/install/moveit_resources_fanuc_description:/home/<USER>/moveit2_ws/install/moveit_common:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('CMAKE_ARGS', '-DPYTHON_EXECUTABLE=/usr/bin/python3.10 -DPython3_EXECUTABLE=/usr/bin/python3.10'), ('CMAKE_MODULE_PATH', '/home/<USER>/moveit2_ws/install/moveit_task_constructor_core/share/moveit_task_constructor_core/cmake'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('PWD', '/home/<USER>/Code/ws_0/build/arm_controller_pkg'), ('SSH_CONNECTION', '::1 0 ::1 22'), ('XDG_DATA_DIRS', '/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/moveit2_ws/install/moveit_task_constructor_core/local/lib/python3.10/dist-packages:/home/<USER>/moveit2_ws/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/moveit2_ws/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/moveit2_ws/install/moveit_task_constructor_msgs/local/lib/python3.10/dist-packages:/home/<USER>/moveit2_ws/install/launch_param_builder/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('Python3_EXECUTABLE', '/usr/bin/python3.10'), ('CMAKE_PREFIX_PATH', '/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner_testutils:/home/<USER>/moveit2_ws/install/moveit_runtime:/home/<USER>/moveit2_ws/install/moveit2_tutorials:/home/<USER>/moveit2_ws/install/moveit:/home/<USER>/moveit2_ws/install/moveit_planners:/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner:/home/<USER>/moveit2_ws/install/moveit_test:/home/<USER>/moveit2_ws/install/moveit_visual_tools:/home/<USER>/moveit2_ws/install/moveit_task_constructor_visualization:/home/<USER>/moveit2_ws/install/moveit_task_constructor_demo:/home/<USER>/moveit2_ws/install/moveit_task_constructor_capabilities:/home/<USER>/moveit2_ws/install/moveit_task_constructor_core:/home/<USER>/moveit2_ws/install/moveit_ros_control_interface:/home/<USER>/moveit2_ws/install/moveit_plugins:/home/<USER>/moveit2_ws/install/moveit_simple_controller_manager:/home/<USER>/moveit2_ws/install/moveit_setup_assistant:/home/<USER>/moveit2_ws/install/moveit_setup_srdf_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_core_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_controllers:/home/<USER>/moveit2_ws/install/moveit_setup_app_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_framework:/home/<USER>/moveit2_ws/install/moveit_servo:/home/<USER>/moveit2_ws/install/moveit_ros:/home/<USER>/moveit2_ws/install/moveit_ros_visualization:/home/<USER>/moveit2_ws/install/moveit_hybrid_planning:/home/<USER>/moveit2_ws/install/move_program:/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface:/home/<USER>/moveit2_ws/install/moveit_ros_benchmarks:/home/<USER>/moveit2_ws/install/moveit_ros_warehouse:/home/<USER>/moveit2_ws/install/moveit_ros_robot_interaction:/home/<USER>/moveit2_ws/install/moveit_ros_perception:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_pg70_support:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_moveit_config:/home/<USER>/moveit2_ws/install/moveit_ros_move_group:/home/<USER>/moveit2_ws/install/moveit_planners_ompl:/home/<USER>/moveit2_ws/install/moveit_ros_planning:/home/<USER>/moveit2_ws/install/moveit_ros_occupancy_map_monitor:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/moveit2_ws/install/moveit_planners_chomp:/home/<USER>/moveit2_ws/install/moveit_kinematics:/home/<USER>/moveit2_ws/install/moveit_chomp_optimizer_adapter:/home/<USER>/moveit2_ws/install/chomp_motion_planner:/home/<USER>/moveit2_ws/install/moveit_core:/home/<USER>/moveit2_ws/install/srdfdom:/home/<USER>/moveit2_ws/install/rviz_marker_tools:/home/<USER>/moveit2_ws/install/rosparam_shortcuts:/home/<USER>/moveit2_ws/install/moveit_task_constructor_msgs:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_support:/home/<USER>/moveit2_ws/install/moveit_resources:/home/<USER>/moveit2_ws/install/moveit_resources_pr2_description:/home/<USER>/moveit2_ws/install/moveit_resources_panda_moveit_config:/home/<USER>/moveit2_ws/install/moveit_resources_panda_description:/home/<USER>/moveit2_ws/install/moveit_resources_fanuc_moveit_config:/home/<USER>/moveit2_ws/install/moveit_resources_fanuc_description:/home/<USER>/moveit2_ws/install/moveit_common:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble')]), 'shell': False}
[12.299404] (arm_controller_pkg) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[12.300621] (arm_controller_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/lib/arm_controller_pkg/path_planning_node\n'}
[12.300962] (arm_controller_pkg) StdoutLine: {'line': b'-- Installing: /home/<USER>/Code/ws_0/install/arm_controller_pkg/lib/arm_controller_pkg/RB_builder_node\n'}
[12.302948] (arm_controller_pkg) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/Code/ws_0/install/arm_controller_pkg/lib/arm_controller_pkg/RB_builder_node" to ""\n'}
[12.303100] (arm_controller_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/ament_index/resource_index/package_run_dependencies/arm_controller_pkg\n'}
[12.303168] (arm_controller_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/ament_index/resource_index/parent_prefix_path/arm_controller_pkg\n'}
[12.303264] (arm_controller_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/environment/ament_prefix_path.sh\n'}
[12.303319] (arm_controller_pkg) StdoutLine: {'line': b'-- Installing: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/environment/ament_prefix_path.dsv\n'}
[12.303363] (arm_controller_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/environment/path.sh\n'}
[12.303391] (arm_controller_pkg) StdoutLine: {'line': b'-- Installing: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/environment/path.dsv\n'}
[12.304066] (arm_controller_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/local_setup.bash\n'}
[12.304167] (arm_controller_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/local_setup.sh\n'}
[12.304233] (arm_controller_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/local_setup.zsh\n'}
[12.304297] (arm_controller_pkg) StdoutLine: {'line': b'-- Installing: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/local_setup.dsv\n'}
[12.304345] (arm_controller_pkg) StdoutLine: {'line': b'-- Installing: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.dsv\n'}
[12.304772] (arm_controller_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/ament_index/resource_index/packages/arm_controller_pkg\n'}
[12.304839] (arm_controller_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/cmake/arm_controller_pkgConfig.cmake\n'}
[12.304892] (arm_controller_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/cmake/arm_controller_pkgConfig-version.cmake\n'}
[12.304931] (arm_controller_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.xml\n'}
[12.306301] (arm_controller_pkg) CommandEnded: {'returncode': 0}
[12.319161] (-) TimerEvent: {}
[12.331250] (arm_controller_pkg) JobEnded: {'identifier': 'arm_controller_pkg', 'rc': 0}
[12.332027] (-) EventReactorShutdown: {}
