[0.073s] Invoking command in '/home/<USER>/Code/ws_0/build/engineer_vision_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/engineer_vision_pkg -- -j8 -l8
[0.136s] [ 25%] Built target video_saver_node
[0.146s] [ 50%] Built target serial_node
[0.150s] [ 75%] Built target video_capturer_node
[0.153s] [ 87%] [32mBuilding CXX object CMakeFiles/RB_detector_node.dir/src/RB_detector_node.cpp.o[0m
[8.504s] [100%] [32m[1mLinking CXX executable RB_detector_node[0m
[8.655s] /usr/bin/ld: warning: libopencv_imgcodecs.so.4.5d, needed by /opt/ros/humble/lib/libcv_bridge.so, may conflict with libopencv_imgcodecs.so.411
[8.655s] /usr/bin/ld: warning: libopencv_imgproc.so.4.5d, needed by /opt/ros/humble/lib/libcv_bridge.so, may conflict with libopencv_imgproc.so.411
[8.657s] /usr/bin/ld: warning: libopencv_core.so.4.5d, needed by /opt/ros/humble/lib/libcv_bridge.so, may conflict with libopencv_core.so.411
[9.067s] [100%] Built target RB_detector_node
[9.078s] Invoked command in '/home/<USER>/Code/ws_0/build/engineer_vision_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/engineer_vision_pkg -- -j8 -l8
[9.081s] Invoking command in '/home/<USER>/Code/ws_0/build/engineer_vision_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/engineer_vision_pkg
[9.085s] -- Install configuration: ""
[9.085s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/engineer_vision_pkg/video_capturer_node
[9.085s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/engineer_vision_pkg/serial_node
[9.086s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/engineer_vision_pkg/video_saver_node
[9.087s] -- Installing: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/engineer_vision_pkg/RB_detector_node
[9.089s] -- Set non-toolchain portion of runtime path of "/home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/engineer_vision_pkg/RB_detector_node" to ""
[9.090s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/launch
[9.090s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/launch/launch.py
[9.091s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/ament_index/resource_index/package_run_dependencies/engineer_vision_pkg
[9.091s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/ament_index/resource_index/parent_prefix_path/engineer_vision_pkg
[9.091s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/environment/ament_prefix_path.sh
[9.091s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/environment/ament_prefix_path.dsv
[9.091s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/environment/path.sh
[9.091s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/environment/path.dsv
[9.091s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/local_setup.bash
[9.091s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/local_setup.sh
[9.091s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/local_setup.zsh
[9.092s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/local_setup.dsv
[9.092s] -- Installing: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.dsv
[9.093s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/ament_index/resource_index/packages/engineer_vision_pkg
[9.093s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/cmake/engineer_vision_pkgConfig.cmake
[9.093s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/cmake/engineer_vision_pkgConfig-version.cmake
[9.093s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.xml
[9.098s] Invoked command in '/home/<USER>/Code/ws_0/build/engineer_vision_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/engineer_vision_pkg
