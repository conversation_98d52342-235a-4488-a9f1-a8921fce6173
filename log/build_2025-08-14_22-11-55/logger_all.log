[0.164s] DEBUG:colcon:Command line arguments: ['/usr/bin/colcon', 'build']
[0.164s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=False, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=8, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=None, packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, allow_overriding=[], cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, mixin_files=None, mixin=None, verb_parser=<colcon_mixin.mixin.mixin_argument.MixinArgumentDecorator object at 0xffffab782b60>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0xffffab782650>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0xffffab782650>>, mixin_verb=('build',))
[0.430s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.430s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.430s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.430s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.430s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.430s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.430s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/Code/ws_0'
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.430s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.443s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.443s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.443s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.443s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.443s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.443s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.443s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.443s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.443s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.443s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.443s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ignore', 'ignore_ament_install']
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore'
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ignore_ament_install'
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_pkg']
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_pkg'
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['colcon_meta']
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'colcon_meta'
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['ros']
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'ros'
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['cmake', 'python']
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'cmake'
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python'
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extensions ['python_setup_py']
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src) by extension 'python_setup_py'
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extensions ['ignore', 'ignore_ament_install']
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extension 'ignore'
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extension 'ignore_ament_install'
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extensions ['colcon_pkg']
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extension 'colcon_pkg'
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extensions ['colcon_meta']
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extension 'colcon_meta'
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extensions ['ros']
[0.444s] Level 1:colcon.colcon_core.package_identification:_identify(src/arm_controller_pkg) by extension 'ros'
[0.448s] DEBUG:colcon.colcon_core.package_identification:Package 'src/arm_controller_pkg' with type 'ros.ament_cmake' and name 'arm_controller_pkg'
[0.449s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extensions ['ignore', 'ignore_ament_install']
[0.449s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extension 'ignore'
[0.449s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extension 'ignore_ament_install'
[0.449s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extensions ['colcon_pkg']
[0.449s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extension 'colcon_pkg'
[0.449s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extensions ['colcon_meta']
[0.449s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extension 'colcon_meta'
[0.449s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extensions ['ros']
[0.449s] Level 1:colcon.colcon_core.package_identification:_identify(src/engineer_vision_pkg) by extension 'ros'
[0.450s] DEBUG:colcon.colcon_core.package_identification:Package 'src/engineer_vision_pkg' with type 'ros.ament_cmake' and name 'engineer_vision_pkg'
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extensions ['ignore', 'ignore_ament_install']
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extension 'ignore'
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extension 'ignore_ament_install'
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extensions ['colcon_pkg']
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extension 'colcon_pkg'
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extensions ['colcon_meta']
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extension 'colcon_meta'
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extensions ['ros']
[0.450s] Level 1:colcon.colcon_core.package_identification:_identify(src/rm_vision) by extension 'ros'
[0.451s] DEBUG:colcon.colcon_core.package_identification:Package 'src/rm_vision' with type 'ros.ament_cmake' and name 'rm_vision'
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extensions ['ignore', 'ignore_ament_install']
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'ignore'
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'ignore_ament_install'
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extensions ['colcon_pkg']
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'colcon_pkg'
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extensions ['colcon_meta']
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'colcon_meta'
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extensions ['ros']
[0.452s] Level 1:colcon.colcon_core.package_identification:_identify(src/robot_description) by extension 'ros'
[0.453s] DEBUG:colcon.colcon_core.package_identification:Package 'src/robot_description' with type 'ros.ament_cmake' and name 'robot_description'
[0.453s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extensions ['ignore', 'ignore_ament_install']
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extension 'ignore'
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extension 'ignore_ament_install'
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extensions ['colcon_pkg']
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extension 'colcon_pkg'
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extensions ['colcon_meta']
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extension 'colcon_meta'
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extensions ['ros']
[0.454s] Level 1:colcon.colcon_core.package_identification:_identify(src/ros2_mindvision_camera) by extension 'ros'
[0.455s] DEBUG:colcon.colcon_core.package_identification:Package 'src/ros2_mindvision_camera' with type 'ros.ament_cmake' and name 'mindvision_camera'
[0.455s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.455s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.455s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.455s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.456s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.488s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) check parameters
[0.489s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) discover
[0.495s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 5 installed packages in /home/<USER>/Code/ws_0/install
[0.503s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 57 installed packages in /home/<USER>/moveit2_ws/install
[0.504s] DEBUG:colcon.colcon_installed_package_information.package_discovery:Found 323 installed packages in /opt/ros/humble
[0.505s] Level 1:colcon.colcon_core.package_discovery:discover_packages(prefix_path) using defaults
[0.723s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'cmake_args' from command line to 'None'
[0.723s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'cmake_target' from command line to 'None'
[0.723s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.723s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'cmake_clean_cache' from command line to 'False'
[0.723s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'cmake_clean_first' from command line to 'False'
[0.723s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'cmake_force_configure' from command line to 'False'
[0.723s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'ament_cmake_args' from command line to 'None'
[0.723s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'catkin_cmake_args' from command line to 'None'
[0.723s] Level 5:colcon.colcon_core.verb:set package 'arm_controller_pkg' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.723s] DEBUG:colcon.colcon_core.verb:Building package 'arm_controller_pkg' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Code/ws_0/build/arm_controller_pkg', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Code/ws_0/install/arm_controller_pkg', 'merge_install': False, 'path': '/home/<USER>/Code/ws_0/src/arm_controller_pkg', 'symlink_install': False, 'test_result_base': None}
[0.723s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'cmake_args' from command line to 'None'
[0.724s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'cmake_target' from command line to 'None'
[0.724s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.724s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'cmake_clean_cache' from command line to 'False'
[0.724s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'cmake_clean_first' from command line to 'False'
[0.724s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'cmake_force_configure' from command line to 'False'
[0.724s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'ament_cmake_args' from command line to 'None'
[0.724s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'catkin_cmake_args' from command line to 'None'
[0.724s] Level 5:colcon.colcon_core.verb:set package 'engineer_vision_pkg' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.724s] DEBUG:colcon.colcon_core.verb:Building package 'engineer_vision_pkg' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Code/ws_0/build/engineer_vision_pkg', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Code/ws_0/install/engineer_vision_pkg', 'merge_install': False, 'path': '/home/<USER>/Code/ws_0/src/engineer_vision_pkg', 'symlink_install': False, 'test_result_base': None}
[0.724s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'cmake_args' from command line to 'None'
[0.724s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'cmake_target' from command line to 'None'
[0.724s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.724s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'cmake_clean_cache' from command line to 'False'
[0.724s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'cmake_clean_first' from command line to 'False'
[0.724s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'cmake_force_configure' from command line to 'False'
[0.724s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'ament_cmake_args' from command line to 'None'
[0.724s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'catkin_cmake_args' from command line to 'None'
[0.724s] Level 5:colcon.colcon_core.verb:set package 'mindvision_camera' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.724s] DEBUG:colcon.colcon_core.verb:Building package 'mindvision_camera' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Code/ws_0/build/mindvision_camera', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Code/ws_0/install/mindvision_camera', 'merge_install': False, 'path': '/home/<USER>/Code/ws_0/src/ros2_mindvision_camera', 'symlink_install': False, 'test_result_base': None}
[0.724s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'cmake_args' from command line to 'None'
[0.724s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'cmake_target' from command line to 'None'
[0.724s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.724s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'cmake_clean_cache' from command line to 'False'
[0.724s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'cmake_clean_first' from command line to 'False'
[0.724s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'cmake_force_configure' from command line to 'False'
[0.724s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'ament_cmake_args' from command line to 'None'
[0.724s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'catkin_cmake_args' from command line to 'None'
[0.724s] Level 5:colcon.colcon_core.verb:set package 'rm_vision' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.724s] DEBUG:colcon.colcon_core.verb:Building package 'rm_vision' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Code/ws_0/build/rm_vision', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Code/ws_0/install/rm_vision', 'merge_install': False, 'path': '/home/<USER>/Code/ws_0/src/rm_vision', 'symlink_install': False, 'test_result_base': None}
[0.724s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_args' from command line to 'None'
[0.724s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_target' from command line to 'None'
[0.724s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.724s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_clean_cache' from command line to 'False'
[0.725s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_clean_first' from command line to 'False'
[0.725s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'cmake_force_configure' from command line to 'False'
[0.725s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'ament_cmake_args' from command line to 'None'
[0.725s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'catkin_cmake_args' from command line to 'None'
[0.725s] Level 5:colcon.colcon_core.verb:set package 'robot_description' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.725s] DEBUG:colcon.colcon_core.verb:Building package 'robot_description' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/Code/ws_0/build/robot_description', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/Code/ws_0/install/robot_description', 'merge_install': False, 'path': '/home/<USER>/Code/ws_0/src/robot_description', 'symlink_install': False, 'test_result_base': None}
[0.725s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.726s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.726s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/Code/ws_0/src/arm_controller_pkg' with build type 'ament_cmake'
[0.726s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Code/ws_0/src/arm_controller_pkg'
[0.728s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.728s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.728s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.734s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/Code/ws_0/src/engineer_vision_pkg' with build type 'ament_cmake'
[0.734s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Code/ws_0/src/engineer_vision_pkg'
[0.735s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.735s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.740s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/Code/ws_0/src/ros2_mindvision_camera' with build type 'ament_cmake'
[0.740s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Code/ws_0/src/ros2_mindvision_camera'
[0.741s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.741s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.746s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/Code/ws_0/src/rm_vision' with build type 'ament_cmake'
[0.746s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Code/ws_0/src/rm_vision'
[0.746s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.746s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.753s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/Code/ws_0/src/robot_description' with build type 'ament_cmake'
[0.754s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/Code/ws_0/src/robot_description'
[0.754s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.755s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.767s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/arm_controller_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/arm_controller_pkg -- -j8 -l8
[0.773s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/engineer_vision_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/engineer_vision_pkg -- -j8 -l8
[0.774s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/mindvision_camera': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/mindvision_camera -- -j8 -l8
[0.777s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/rm_vision': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/rm_vision -- -j8 -l8
[0.788s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/robot_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/robot_description -- -j8 -l8
[0.817s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/rm_vision' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/rm_vision -- -j8 -l8
[0.834s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/rm_vision': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/rm_vision
[0.841s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/robot_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/robot_description -- -j8 -l8
[0.846s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/robot_description': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/robot_description
[0.848s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rm_vision)
[0.851s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/rm_vision' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/rm_vision
[0.857s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision' for CMake module files
[0.858s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision' for CMake config files
[0.858s] Level 1:colcon.colcon_core.shell:create_environment_hook('rm_vision', 'cmake_prefix_path')
[0.858s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/hook/cmake_prefix_path.ps1'
[0.860s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/hook/cmake_prefix_path.dsv'
[0.860s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/hook/cmake_prefix_path.sh'
[0.861s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision/bin'
[0.861s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision/lib/pkgconfig/rm_vision.pc'
[0.862s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision/lib/python3.10/site-packages'
[0.862s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision/bin'
[0.863s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.ps1'
[0.864s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.dsv'
[0.865s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.sh'
[0.866s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.bash'
[0.867s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.zsh'
[0.867s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/rm_vision/share/colcon-core/packages/rm_vision)
[0.868s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(rm_vision)
[0.868s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision' for CMake module files
[0.868s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision' for CMake config files
[0.868s] Level 1:colcon.colcon_core.shell:create_environment_hook('rm_vision', 'cmake_prefix_path')
[0.868s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/hook/cmake_prefix_path.ps1'
[0.868s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/hook/cmake_prefix_path.dsv'
[0.869s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/hook/cmake_prefix_path.sh'
[0.870s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision/bin'
[0.870s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision/lib/pkgconfig/rm_vision.pc'
[0.870s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision/lib/python3.10/site-packages'
[0.870s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/rm_vision/bin'
[0.871s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.ps1'
[0.871s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.dsv'
[0.871s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.sh'
[0.872s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.bash'
[0.872s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/rm_vision/share/rm_vision/package.zsh'
[0.873s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/rm_vision/share/colcon-core/packages/rm_vision)
[0.874s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(robot_description)
[0.874s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description' for CMake module files
[0.874s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/robot_description' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/robot_description
[0.875s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description' for CMake config files
[0.875s] Level 1:colcon.colcon_core.shell:create_environment_hook('robot_description', 'cmake_prefix_path')
[0.876s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/hook/cmake_prefix_path.ps1'
[0.876s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/hook/cmake_prefix_path.dsv'
[0.876s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/hook/cmake_prefix_path.sh'
[0.876s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description/bin'
[0.877s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description/lib/pkgconfig/robot_description.pc'
[0.877s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description/lib/python3.10/site-packages'
[0.877s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description/bin'
[0.877s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.ps1'
[0.877s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.dsv'
[0.878s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.sh'
[0.878s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.bash'
[0.878s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.zsh'
[0.878s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/robot_description/share/colcon-core/packages/robot_description)
[0.879s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(robot_description)
[0.879s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description' for CMake module files
[0.879s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description' for CMake config files
[0.879s] Level 1:colcon.colcon_core.shell:create_environment_hook('robot_description', 'cmake_prefix_path')
[0.879s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/hook/cmake_prefix_path.ps1'
[0.880s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/hook/cmake_prefix_path.dsv'
[0.880s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/hook/cmake_prefix_path.sh'
[0.880s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description/bin'
[0.880s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description/lib/pkgconfig/robot_description.pc'
[0.881s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description/lib/python3.10/site-packages'
[0.881s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/robot_description/bin'
[0.881s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.ps1'
[0.882s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.dsv'
[0.882s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.sh'
[0.883s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.bash'
[0.889s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/robot_description/share/robot_description/package.zsh'
[0.927s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/robot_description/share/colcon-core/packages/robot_description)
[0.943s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/mindvision_camera' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/mindvision_camera -- -j8 -l8
[0.944s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/mindvision_camera': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/mindvision_camera
[0.944s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/arm_controller_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/arm_controller_pkg -- -j8 -l8
[0.944s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/arm_controller_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/arm_controller_pkg
[0.957s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(mindvision_camera)
[0.957s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera' for CMake module files
[0.957s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera' for CMake config files
[0.957s] Level 1:colcon.colcon_core.shell:create_environment_hook('mindvision_camera', 'cmake_prefix_path')
[0.957s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/cmake_prefix_path.ps1'
[0.958s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/mindvision_camera' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/mindvision_camera
[0.959s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/cmake_prefix_path.dsv'
[0.959s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/cmake_prefix_path.sh'
[0.960s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/lib'
[0.961s] Level 1:colcon.colcon_core.shell:create_environment_hook('mindvision_camera', 'ld_library_path_lib')
[0.961s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/ld_library_path_lib.ps1'
[0.961s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/ld_library_path_lib.dsv'
[0.961s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/ld_library_path_lib.sh'
[0.961s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/bin'
[0.961s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/lib/pkgconfig/mindvision_camera.pc'
[0.962s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/lib/python3.10/site-packages'
[0.962s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/bin'
[0.962s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.ps1'
[0.963s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.dsv'
[0.963s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.sh'
[0.963s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.bash'
[0.963s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.zsh'
[0.964s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/mindvision_camera/share/colcon-core/packages/mindvision_camera)
[0.964s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(mindvision_camera)
[0.964s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera' for CMake module files
[0.964s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera' for CMake config files
[0.964s] Level 1:colcon.colcon_core.shell:create_environment_hook('mindvision_camera', 'cmake_prefix_path')
[0.965s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/cmake_prefix_path.ps1'
[0.966s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/cmake_prefix_path.dsv'
[0.967s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/cmake_prefix_path.sh'
[0.968s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/lib'
[0.968s] Level 1:colcon.colcon_core.shell:create_environment_hook('mindvision_camera', 'ld_library_path_lib')
[0.969s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/ld_library_path_lib.ps1'
[0.969s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/ld_library_path_lib.dsv'
[0.970s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/hook/ld_library_path_lib.sh'
[0.970s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/bin'
[0.970s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/lib/pkgconfig/mindvision_camera.pc'
[0.970s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/lib/python3.10/site-packages'
[0.970s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/mindvision_camera/bin'
[0.971s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.ps1'
[0.971s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.dsv'
[0.972s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.sh'
[0.972s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.bash'
[0.972s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.zsh'
[0.972s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/mindvision_camera/share/colcon-core/packages/mindvision_camera)
[0.974s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(arm_controller_pkg)
[0.974s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/arm_controller_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/arm_controller_pkg
[0.974s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg' for CMake module files
[0.975s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg' for CMake config files
[0.975s] Level 1:colcon.colcon_core.shell:create_environment_hook('arm_controller_pkg', 'cmake_prefix_path')
[0.975s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/hook/cmake_prefix_path.ps1'
[0.976s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/hook/cmake_prefix_path.dsv'
[0.976s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/hook/cmake_prefix_path.sh'
[0.978s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/lib'
[0.978s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/bin'
[0.978s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/lib/pkgconfig/arm_controller_pkg.pc'
[0.978s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/lib/python3.10/site-packages'
[0.978s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/bin'
[0.979s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.ps1'
[0.980s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.dsv'
[0.981s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.sh'
[0.981s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.bash'
[0.982s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.zsh'
[0.982s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/colcon-core/packages/arm_controller_pkg)
[0.982s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(arm_controller_pkg)
[0.982s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg' for CMake module files
[0.983s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg' for CMake config files
[0.983s] Level 1:colcon.colcon_core.shell:create_environment_hook('arm_controller_pkg', 'cmake_prefix_path')
[0.983s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/hook/cmake_prefix_path.ps1'
[0.984s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/hook/cmake_prefix_path.dsv'
[0.985s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/hook/cmake_prefix_path.sh'
[0.985s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/lib'
[0.985s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/bin'
[0.985s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/lib/pkgconfig/arm_controller_pkg.pc'
[0.985s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/lib/python3.10/site-packages'
[0.986s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/arm_controller_pkg/bin'
[0.986s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.ps1'
[0.986s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.dsv'
[0.988s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.sh'
[0.989s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.bash'
[0.989s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.zsh'
[0.991s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/arm_controller_pkg/share/colcon-core/packages/arm_controller_pkg)
[16.520s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/engineer_vision_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/engineer_vision_pkg -- -j8 -l8
[16.522s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoking command in '/home/<USER>/Code/ws_0/build/engineer_vision_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/engineer_vision_pkg
[16.536s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(engineer_vision_pkg)
[16.536s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg' for CMake module files
[16.537s] DEBUG:colcon.colcon_core.event_handler.log_command:Invoked command in '/home/<USER>/Code/ws_0/build/engineer_vision_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/engineer_vision_pkg
[16.537s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg' for CMake config files
[16.537s] Level 1:colcon.colcon_core.shell:create_environment_hook('engineer_vision_pkg', 'cmake_prefix_path')
[16.537s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/hook/cmake_prefix_path.ps1'
[16.538s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/hook/cmake_prefix_path.dsv'
[16.539s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/hook/cmake_prefix_path.sh'
[16.540s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib'
[16.540s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/bin'
[16.540s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/pkgconfig/engineer_vision_pkg.pc'
[16.540s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/python3.10/site-packages'
[16.540s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/bin'
[16.541s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.ps1'
[16.541s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.dsv'
[16.542s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.sh'
[16.542s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.bash'
[16.543s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.zsh'
[16.544s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/colcon-core/packages/engineer_vision_pkg)
[16.544s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(engineer_vision_pkg)
[16.544s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg' for CMake module files
[16.545s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg' for CMake config files
[16.545s] Level 1:colcon.colcon_core.shell:create_environment_hook('engineer_vision_pkg', 'cmake_prefix_path')
[16.545s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/hook/cmake_prefix_path.ps1'
[16.545s] INFO:colcon.colcon_core.shell:Creating environment descriptor '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/hook/cmake_prefix_path.dsv'
[16.546s] INFO:colcon.colcon_core.shell:Creating environment hook '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/hook/cmake_prefix_path.sh'
[16.546s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib'
[16.547s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/bin'
[16.547s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/pkgconfig/engineer_vision_pkg.pc'
[16.547s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/python3.10/site-packages'
[16.547s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/bin'
[16.547s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.ps1'
[16.550s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.dsv'
[16.551s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.sh'
[16.552s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.bash'
[16.553s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.zsh'
[16.562s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/colcon-core/packages/engineer_vision_pkg)
[16.565s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[16.566s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[16.567s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '0'
[16.567s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[16.582s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[16.582s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[16.582s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify2'
[16.753s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[16.754s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Code/ws_0/install/local_setup.ps1'
[16.755s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/Code/ws_0/install/_local_setup_util_ps1.py'
[16.759s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Code/ws_0/install/setup.ps1'
[16.766s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Code/ws_0/install/local_setup.sh'
[16.768s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/Code/ws_0/install/_local_setup_util_sh.py'
[16.770s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Code/ws_0/install/setup.sh'
[16.775s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Code/ws_0/install/local_setup.bash'
[16.776s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Code/ws_0/install/setup.bash'
[16.784s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/Code/ws_0/install/local_setup.zsh'
[16.785s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/Code/ws_0/install/setup.zsh'
