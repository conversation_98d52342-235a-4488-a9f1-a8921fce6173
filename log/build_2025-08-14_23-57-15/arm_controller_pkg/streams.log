[0.043s] Invoking command in '/home/<USER>/Code/ws_0/build/arm_controller_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/arm_controller_pkg -- -j8 -l8
[0.255s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.688s] -- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
[0.747s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.753s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.761s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.782s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.828s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.887s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.894s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[1.329s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[1.403s] -- Found tf2: 0.25.15 (/opt/ros/humble/share/tf2/cmake)
[1.431s] -- Found tf2_geometry_msgs: 0.25.15 (/opt/ros/humble/share/tf2_geometry_msgs/cmake)
[1.436s] -- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
[1.438s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[1.523s] -- Found moveit_ros_planning_interface: 2.5.9 (/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake)
[1.524s] [33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ConfigExtras.cmake:9 (find_package):
[1.524s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.524s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.524s]   set the policy and suppress this warning.
[1.524s] 
[1.524s] Call Stack (most recent call first):
[1.524s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
[1.524s]   CMakeLists.txt:16 (find_package)
[1.524s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.524s] [0m
[1.552s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: date_time filesystem program_options system thread
[1.676s] [33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_core/share/moveit_core/cmake/ConfigExtras.cmake:3 (find_package):
[1.676s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.676s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.676s]   set the policy and suppress this warning.
[1.676s] 
[1.676s] Call Stack (most recent call first):
[1.676s]   /home/<USER>/moveit2_ws/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)
[1.676s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.676s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
[1.676s]   CMakeLists.txt:16 (find_package)
[1.676s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.676s] [0m
[1.687s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: chrono date_time filesystem iostreams program_options regex serialization system thread
[1.734s] -- Found parameter_traits: 0.5.0 (/opt/ros/humble/share/parameter_traits/cmake)
[1.744s] -- Found rclcpp_lifecycle: 16.0.14 (/opt/ros/humble/share/rclcpp_lifecycle/cmake)
[1.760s] [33mCMake Warning (dev) at /opt/ros/humble/share/geometric_shapes/cmake/ConfigExtras.cmake:31 (find_package):
[1.760s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.761s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.761s]   set the policy and suppress this warning.
[1.761s] 
[1.761s] Call Stack (most recent call first):
[1.761s]   /opt/ros/humble/share/geometric_shapes/cmake/geometric_shapesConfig.cmake:41 (include)
[1.761s]   /home/<USER>/moveit2_ws/install/moveit_core/share/moveit_core/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.761s]   /home/<USER>/moveit2_ws/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)
[1.761s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.761s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
[1.761s]   CMakeLists.txt:16 (find_package)
[1.761s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.761s] [0m
[1.763s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: filesystem
[1.808s] -- library: /usr/lib/aarch64-linux-gnu/libcurl.so
[1.889s] [33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_ros_planning/share/moveit_ros_planning/cmake/ConfigExtras.cmake:3 (find_package):
[1.889s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.890s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.890s]   set the policy and suppress this warning.
[1.890s] 
[1.890s] Call Stack (most recent call first):
[1.890s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning/share/moveit_ros_planning/cmake/moveit_ros_planningConfig.cmake:41 (include)
[1.890s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.890s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
[1.890s]   CMakeLists.txt:16 (find_package)
[1.890s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.890s] [0m
[1.894s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem date_time program_options thread chrono
[1.927s] [33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_ros_warehouse/share/moveit_ros_warehouse/cmake/ConfigExtras.cmake:3 (find_package):
[1.927s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.927s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.927s]   set the policy and suppress this warning.
[1.927s] 
[1.927s] Call Stack (most recent call first):
[1.927s]   /home/<USER>/moveit2_ws/install/moveit_ros_warehouse/share/moveit_ros_warehouse/cmake/moveit_ros_warehouseConfig.cmake:41 (include)
[1.927s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.927s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
[1.927s]   CMakeLists.txt:16 (find_package)
[1.927s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.927s] [0m
[1.931s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: thread system filesystem regex date_time program_options
[1.949s] [33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_ros_move_group/share/moveit_ros_move_group/cmake/ConfigExtras.cmake:3 (find_package):
[1.949s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.949s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.949s]   set the policy and suppress this warning.
[1.949s] 
[1.949s] Call Stack (most recent call first):
[1.949s]   /home/<USER>/moveit2_ws/install/moveit_ros_move_group/share/moveit_ros_move_group/cmake/moveit_ros_move_groupConfig.cmake:41 (include)
[1.949s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.949s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
[1.949s]   CMakeLists.txt:16 (find_package)
[1.949s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.949s] [0m
[1.957s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem date_time program_options thread
[2.008s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[2.078s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[2.078s] -- Configured cppcheck include dirs: /home/<USER>/Code/ws_0/src/arm_controller_pkg/include
[2.078s] -- Configured cppcheck exclude dirs and/or files: 
[2.078s] -- Added test 'lint_cmake' to check CMake code style
[2.079s] -- Added test 'uncrustify' to check C / C++ code style
[2.079s] -- Configured uncrustify additional arguments: 
[2.079s] -- Added test 'xmllint' to check XML markup files
[2.081s] -- Configuring done (1.9s)
[2.154s] -- Generating done (0.1s)
[2.161s] -- Build files have been written to: /home/<USER>/Code/ws_0/build/arm_controller_pkg
[2.189s] [ 25%] [32mBuilding CXX object CMakeFiles/RB_builder_node.dir/src/RB_builder_node.cpp.o[0m
[2.205s] [ 75%] Built target path_planning_node
[2.450s] [01m[K/home/<USER>/Code/ws_0/src/arm_controller_pkg/src/RB_builder_node.cpp:8:10:[m[K [01;31m[Kfatal error: [m[Kmoveit/planning_scene_interface/planning_scene_interface.h: No such file or directory
[2.450s]     8 | #include [01;31m[K<moveit/planning_scene_interface/planning_scene_interface.h>[m[K
[2.450s]       |          [01;31m[K^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~[m[K
[2.451s] compilation terminated.
[2.452s] gmake[2]: *** [CMakeFiles/RB_builder_node.dir/build.make:79: CMakeFiles/RB_builder_node.dir/src/RB_builder_node.cpp.o] Error 1
[2.453s] gmake[1]: *** [CMakeFiles/Makefile2:188: CMakeFiles/RB_builder_node.dir/all] Error 2
[2.456s] gmake: *** [Makefile:146: all] Error 2
[2.460s] Invoked command in '/home/<USER>/Code/ws_0/build/arm_controller_pkg' returned '2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/arm_controller_pkg -- -j8 -l8
