[0.000000] (-) TimerEvent: {}
[0.000996] (-) JobUnselected: {'identifier': 'engineer_vision_pkg'}
[0.001096] (-) JobUnselected: {'identifier': 'mindvision_camera'}
[0.001142] (-) JobUnselected: {'identifier': 'rm_vision'}
[0.001176] (-) JobUnselected: {'identifier': 'robot_description'}
[0.001213] (arm_controller_pkg) JobQueued: {'identifier': 'arm_controller_pkg', 'dependencies': OrderedDict()}
[0.003239] (arm_controller_pkg) JobStarted: {'identifier': 'arm_controller_pkg'}
[0.033739] (arm_controller_pkg) JobProgress: {'identifier': 'arm_controller_pkg', 'progress': 'cmake'}
[0.038663] (arm_controller_pkg) JobProgress: {'identifier': 'arm_controller_pkg', 'progress': 'build'}
[0.040837] (arm_controller_pkg) Command: {'cmd': ['/usr/local/cmake/bin/cmake', '--build', '/home/<USER>/Code/ws_0/build/arm_controller_pkg', '--', '-j8', '-l8'], 'cwd': '/home/<USER>/Code/ws_0/build/arm_controller_pkg', 'env': OrderedDict([('USER', 'mac'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner_testutils/lib:/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner/lib:/home/<USER>/moveit2_ws/install/moveit_visual_tools/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_visualization/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_demo/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_capabilities/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_core/lib:/home/<USER>/moveit2_ws/install/moveit_ros_control_interface/lib:/home/<USER>/moveit2_ws/install/moveit_simple_controller_manager/lib:/home/<USER>/moveit2_ws/install/moveit_setup_assistant/lib:/home/<USER>/moveit2_ws/install/moveit_setup_srdf_plugins/lib:/home/<USER>/moveit2_ws/install/moveit_setup_core_plugins/lib:/home/<USER>/moveit2_ws/install/moveit_setup_controllers/lib:/home/<USER>/moveit2_ws/install/moveit_setup_app_plugins/lib:/home/<USER>/moveit2_ws/install/moveit_setup_framework/lib:/home/<USER>/moveit2_ws/install/moveit_servo/lib:/home/<USER>/moveit2_ws/install/moveit_ros_visualization/lib:/home/<USER>/moveit2_ws/install/moveit_hybrid_planning/lib:/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/lib:/home/<USER>/moveit2_ws/install/moveit_ros_benchmarks/lib:/home/<USER>/moveit2_ws/install/moveit_ros_warehouse/lib:/home/<USER>/moveit2_ws/install/moveit_ros_robot_interaction/lib:/home/<USER>/moveit2_ws/install/moveit_ros_perception/lib:/home/<USER>/moveit2_ws/install/moveit_ros_move_group/lib:/home/<USER>/moveit2_ws/install/moveit_planners_ompl/lib:/home/<USER>/moveit2_ws/install/moveit_ros_planning/lib:/home/<USER>/moveit2_ws/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/moveit2_ws/install/moveit_planners_chomp/lib:/home/<USER>/moveit2_ws/install/moveit_kinematics/lib:/home/<USER>/moveit2_ws/install/moveit_chomp_optimizer_adapter/lib:/home/<USER>/moveit2_ws/install/chomp_motion_planner/lib:/home/<USER>/moveit2_ws/install/moveit_core/lib:/home/<USER>/moveit2_ws/install/srdfdom/lib:/home/<USER>/moveit2_ws/install/rviz_marker_tools/lib:/home/<USER>/moveit2_ws/install/rosparam_shortcuts/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_msgs/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/bin/helpers/browser.sh'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/Code/ws_0'), ('TERM_PROGRAM_VERSION', '1.103.1'), ('VSCODE_IPC_HOOK_CLI', '/run/user/502/vscode-ipc-89356d9e-2569-497e-8a14-9200925310d6.sock'), ('ROS_PYTHON_VERSION', '3'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/502/bus'), ('COLORTERM', 'truecolor'), ('COLCON_PREFIX_PATH', '/home/<USER>/moveit2_ws/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mac'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/home/<USER>/moveit2_ws/install/moveit_core/bin:/opt/ros/humble/bin:/usr/local/cmake/bin:/opt/orbstack-guest/bin-hiprio:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/opt/orbstack-guest/bin:/opt/orbstack-guest/data/bin/cmdlinks'), ('PYTHON_EXECUTABLE', '/usr/bin/python3.10'), ('XDG_RUNTIME_DIR', '/run/user/502'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-ec2f90e73ca2d53c.txt'), ('LANG', 'C.UTF-8'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/502/vscode-git-4bcffef955.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AUTH_SOCK', '/run/user/502/vscode-ssh-auth-sock-589875342'), ('AMENT_PREFIX_PATH', '/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner_testutils:/home/<USER>/moveit2_ws/install/moveit_runtime:/home/<USER>/moveit2_ws/install/moveit2_tutorials:/home/<USER>/moveit2_ws/install/moveit:/home/<USER>/moveit2_ws/install/moveit_planners:/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner:/home/<USER>/moveit2_ws/install/moveit_test:/home/<USER>/moveit2_ws/install/moveit_visual_tools:/home/<USER>/moveit2_ws/install/moveit_task_constructor_visualization:/home/<USER>/moveit2_ws/install/moveit_task_constructor_demo:/home/<USER>/moveit2_ws/install/moveit_task_constructor_capabilities:/home/<USER>/moveit2_ws/install/moveit_task_constructor_core:/home/<USER>/moveit2_ws/install/moveit_ros_control_interface:/home/<USER>/moveit2_ws/install/moveit_plugins:/home/<USER>/moveit2_ws/install/moveit_simple_controller_manager:/home/<USER>/moveit2_ws/install/moveit_setup_assistant:/home/<USER>/moveit2_ws/install/moveit_setup_srdf_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_core_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_controllers:/home/<USER>/moveit2_ws/install/moveit_setup_app_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_framework:/home/<USER>/moveit2_ws/install/moveit_servo:/home/<USER>/moveit2_ws/install/moveit_ros:/home/<USER>/moveit2_ws/install/moveit_ros_visualization:/home/<USER>/moveit2_ws/install/moveit_hybrid_planning:/home/<USER>/moveit2_ws/install/move_program:/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface:/home/<USER>/moveit2_ws/install/moveit_ros_benchmarks:/home/<USER>/moveit2_ws/install/moveit_ros_warehouse:/home/<USER>/moveit2_ws/install/moveit_ros_robot_interaction:/home/<USER>/moveit2_ws/install/moveit_ros_perception:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_pg70_support:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_moveit_config:/home/<USER>/moveit2_ws/install/moveit_ros_move_group:/home/<USER>/moveit2_ws/install/moveit_planners_ompl:/home/<USER>/moveit2_ws/install/moveit_ros_planning:/home/<USER>/moveit2_ws/install/moveit_ros_occupancy_map_monitor:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/moveit2_ws/install/moveit_planners_chomp:/home/<USER>/moveit2_ws/install/moveit_kinematics:/home/<USER>/moveit2_ws/install/moveit_chomp_optimizer_adapter:/home/<USER>/moveit2_ws/install/chomp_motion_planner:/home/<USER>/moveit2_ws/install/moveit_core:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/srdfdom:/home/<USER>/moveit2_ws/install/rviz_marker_tools:/home/<USER>/moveit2_ws/install/rosparam_shortcuts:/home/<USER>/moveit2_ws/install/moveit_task_constructor_msgs:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_support:/home/<USER>/moveit2_ws/install/moveit_resources:/home/<USER>/moveit2_ws/install/moveit_resources_pr2_description:/home/<USER>/moveit2_ws/install/moveit_resources_panda_moveit_config:/home/<USER>/moveit2_ws/install/moveit_resources_panda_description:/home/<USER>/moveit2_ws/install/moveit_resources_fanuc_moveit_config:/home/<USER>/moveit2_ws/install/moveit_resources_fanuc_description:/home/<USER>/moveit2_ws/install/moveit_common:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('CMAKE_ARGS', '-DPYTHON_EXECUTABLE=/usr/bin/python3.10 -DPython3_EXECUTABLE=/usr/bin/python3.10'), ('CMAKE_MODULE_PATH', '/home/<USER>/moveit2_ws/install/moveit_task_constructor_core/share/moveit_task_constructor_core/cmake'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('PWD', '/home/<USER>/Code/ws_0/build/arm_controller_pkg'), ('SSH_CONNECTION', '::1 0 ::1 22'), ('XDG_DATA_DIRS', '/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/moveit2_ws/install/moveit_task_constructor_core/local/lib/python3.10/dist-packages:/home/<USER>/moveit2_ws/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/moveit2_ws/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/moveit2_ws/install/moveit_task_constructor_msgs/local/lib/python3.10/dist-packages:/home/<USER>/moveit2_ws/install/launch_param_builder/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('Python3_EXECUTABLE', '/usr/bin/python3.10'), ('CMAKE_PREFIX_PATH', '/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner_testutils:/home/<USER>/moveit2_ws/install/moveit_runtime:/home/<USER>/moveit2_ws/install/moveit2_tutorials:/home/<USER>/moveit2_ws/install/moveit:/home/<USER>/moveit2_ws/install/moveit_planners:/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner:/home/<USER>/moveit2_ws/install/moveit_test:/home/<USER>/moveit2_ws/install/moveit_visual_tools:/home/<USER>/moveit2_ws/install/moveit_task_constructor_visualization:/home/<USER>/moveit2_ws/install/moveit_task_constructor_demo:/home/<USER>/moveit2_ws/install/moveit_task_constructor_capabilities:/home/<USER>/moveit2_ws/install/moveit_task_constructor_core:/home/<USER>/moveit2_ws/install/moveit_ros_control_interface:/home/<USER>/moveit2_ws/install/moveit_plugins:/home/<USER>/moveit2_ws/install/moveit_simple_controller_manager:/home/<USER>/moveit2_ws/install/moveit_setup_assistant:/home/<USER>/moveit2_ws/install/moveit_setup_srdf_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_core_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_controllers:/home/<USER>/moveit2_ws/install/moveit_setup_app_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_framework:/home/<USER>/moveit2_ws/install/moveit_servo:/home/<USER>/moveit2_ws/install/moveit_ros:/home/<USER>/moveit2_ws/install/moveit_ros_visualization:/home/<USER>/moveit2_ws/install/moveit_hybrid_planning:/home/<USER>/moveit2_ws/install/move_program:/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface:/home/<USER>/moveit2_ws/install/moveit_ros_benchmarks:/home/<USER>/moveit2_ws/install/moveit_ros_warehouse:/home/<USER>/moveit2_ws/install/moveit_ros_robot_interaction:/home/<USER>/moveit2_ws/install/moveit_ros_perception:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_pg70_support:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_moveit_config:/home/<USER>/moveit2_ws/install/moveit_ros_move_group:/home/<USER>/moveit2_ws/install/moveit_planners_ompl:/home/<USER>/moveit2_ws/install/moveit_ros_planning:/home/<USER>/moveit2_ws/install/moveit_ros_occupancy_map_monitor:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/moveit2_ws/install/moveit_planners_chomp:/home/<USER>/moveit2_ws/install/moveit_kinematics:/home/<USER>/moveit2_ws/install/moveit_chomp_optimizer_adapter:/home/<USER>/moveit2_ws/install/chomp_motion_planner:/home/<USER>/moveit2_ws/install/moveit_core:/home/<USER>/moveit2_ws/install/srdfdom:/home/<USER>/moveit2_ws/install/rviz_marker_tools:/home/<USER>/moveit2_ws/install/rosparam_shortcuts:/home/<USER>/moveit2_ws/install/moveit_task_constructor_msgs:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_support:/home/<USER>/moveit2_ws/install/moveit_resources:/home/<USER>/moveit2_ws/install/moveit_resources_pr2_description:/home/<USER>/moveit2_ws/install/moveit_resources_panda_moveit_config:/home/<USER>/moveit2_ws/install/moveit_resources_panda_description:/home/<USER>/moveit2_ws/install/moveit_resources_fanuc_moveit_config:/home/<USER>/moveit2_ws/install/moveit_resources_fanuc_description:/home/<USER>/moveit2_ws/install/moveit_common:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble')]), 'shell': False}
[0.097596] (-) TimerEvent: {}
[0.201135] (-) TimerEvent: {}
[0.218050] (arm_controller_pkg) StdoutLine: {'line': b'[ 25%] \x1b[32mBuilding CXX object CMakeFiles/RB_builder_node.dir/src/RB_builder_node.cpp.o\x1b[0m\n'}
[0.242407] (arm_controller_pkg) StdoutLine: {'line': b'[ 75%] Built target path_planning_node\n'}
[0.301427] (-) TimerEvent: {}
[0.405109] (-) TimerEvent: {}
[0.505381] (-) TimerEvent: {}
[0.605855] (-) TimerEvent: {}
[0.706244] (-) TimerEvent: {}
[0.810376] (-) TimerEvent: {}
[0.915798] (-) TimerEvent: {}
[1.016510] (-) TimerEvent: {}
[1.121782] (-) TimerEvent: {}
[1.222367] (-) TimerEvent: {}
[1.324444] (-) TimerEvent: {}
[1.425379] (-) TimerEvent: {}
[1.527584] (-) TimerEvent: {}
[1.628444] (-) TimerEvent: {}
[1.730252] (-) TimerEvent: {}
[1.830614] (-) TimerEvent: {}
[1.930881] (-) TimerEvent: {}
[2.032233] (-) TimerEvent: {}
[2.136133] (-) TimerEvent: {}
[2.237420] (-) TimerEvent: {}
[2.338442] (-) TimerEvent: {}
[2.448555] (-) TimerEvent: {}
[2.556616] (-) TimerEvent: {}
[2.762270] (-) TimerEvent: {}
[2.863724] (-) TimerEvent: {}
[2.967723] (-) TimerEvent: {}
[3.068191] (-) TimerEvent: {}
[3.171984] (-) TimerEvent: {}
[3.272933] (-) TimerEvent: {}
[3.373734] (-) TimerEvent: {}
[3.476620] (-) TimerEvent: {}
[3.580816] (-) TimerEvent: {}
[3.681973] (-) TimerEvent: {}
[3.783942] (-) TimerEvent: {}
[3.886576] (-) TimerEvent: {}
[3.987048] (-) TimerEvent: {}
[4.087577] (-) TimerEvent: {}
[4.194899] (-) TimerEvent: {}
[4.296019] (-) TimerEvent: {}
[4.398297] (-) TimerEvent: {}
[4.499507] (-) TimerEvent: {}
[4.601082] (-) TimerEvent: {}
[4.701822] (-) TimerEvent: {}
[4.802883] (-) TimerEvent: {}
[4.903493] (-) TimerEvent: {}
[5.004283] (-) TimerEvent: {}
[5.107890] (-) TimerEvent: {}
[5.208962] (-) TimerEvent: {}
[5.336624] (-) TimerEvent: {}
[5.439744] (-) TimerEvent: {}
[5.541263] (-) TimerEvent: {}
[5.643549] (-) TimerEvent: {}
[5.744966] (-) TimerEvent: {}
[5.846334] (-) TimerEvent: {}
[5.949111] (-) TimerEvent: {}
[6.051633] (-) TimerEvent: {}
[6.152491] (-) TimerEvent: {}
[6.253089] (-) TimerEvent: {}
[6.320807] (arm_controller_pkg) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/Code/ws_0/src/arm_controller_pkg/src/RB_builder_node.cpp:\x1b[m\x1b[K In member function \xe2\x80\x98\x1b[01m\x1b[Kvoid RB_builder_node::create_object()\x1b[m\x1b[K\xe2\x80\x99:\n'}
[6.323898] (arm_controller_pkg) StderrLine: {'line': b'\x1b[01m\x1b[K/home/<USER>/Code/ws_0/src/arm_controller_pkg/src/RB_builder_node.cpp:100:73:\x1b[m\x1b[K \x1b[01;31m\x1b[Kerror: \x1b[m\x1b[Kno match for \xe2\x80\x98\x1b[01m\x1b[Koperator=\x1b[m\x1b[K\xe2\x80\x99 (operand types are \xe2\x80\x98\x1b[01m\x1b[Kshape_msgs::msg::SolidPrimitive_<std::allocator<void> >::_dimensions_type\x1b[m\x1b[K\xe2\x80\x99 {aka \xe2\x80\x98\x1b[01m\x1b[Krosidl_runtime_cpp::BoundedVector<double, 3, std::allocator<double> >\x1b[m\x1b[K\xe2\x80\x99} and \xe2\x80\x98\x1b[01m\x1b[K__gnu_cxx::__alloc_traits<std::allocator<std::vector<double, std::allocator<double> > >, std::vector<double, std::allocator<double> > >::value_type\x1b[m\x1b[K\xe2\x80\x99 {aka \xe2\x80\x98\x1b[01m\x1b[Kstd::vector<double, std::allocator<double> >\x1b[m\x1b[K\xe2\x80\x99})\n'}
[6.324105] (arm_controller_pkg) StderrLine: {'line': b'  100 |             collision_object.primitives[i].dimensions = box_dimensions[i\x1b[01;31m\x1b[K]\x1b[m\x1b[K;\n'}
[6.324229] (arm_controller_pkg) StderrLine: {'line': b'      |                                                                         \x1b[01;31m\x1b[K^\x1b[m\x1b[K\n'}
[6.324276] (arm_controller_pkg) StderrLine: {'line': b'In file included from \x1b[01m\x1b[K/opt/ros/humble/include/builtin_interfaces/builtin_interfaces/msg/detail/duration__struct.hpp:14\x1b[m\x1b[K,\n'}
[6.324359] (arm_controller_pkg) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/builtin_interfaces/builtin_interfaces/msg/duration.hpp:7\x1b[m\x1b[K,\n'}
[6.324457] (arm_controller_pkg) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/duration.hpp:20\x1b[m\x1b[K,\n'}
[6.324543] (arm_controller_pkg) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/qos.hpp:20\x1b[m\x1b[K,\n'}
[6.324584] (arm_controller_pkg) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/node_interfaces/node_graph_interface.hpp:32\x1b[m\x1b[K,\n'}
[6.324635] (arm_controller_pkg) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/client.hpp:42\x1b[m\x1b[K,\n'}
[6.326432] (arm_controller_pkg) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/callback_group.hpp:24\x1b[m\x1b[K,\n'}
[6.326535] (arm_controller_pkg) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/any_executable.hpp:20\x1b[m\x1b[K,\n'}
[6.326629] (arm_controller_pkg) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategy.hpp:25\x1b[m\x1b[K,\n'}
[6.326692] (arm_controller_pkg) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategies.hpp:18\x1b[m\x1b[K,\n'}
[6.326742] (arm_controller_pkg) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executor_options.hpp:20\x1b[m\x1b[K,\n'}
[6.326795] (arm_controller_pkg) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executor.hpp:37\x1b[m\x1b[K,\n'}
[6.326841] (arm_controller_pkg) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executors/multi_threaded_executor.hpp:25\x1b[m\x1b[K,\n'}
[6.326875] (arm_controller_pkg) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/executors.hpp:21\x1b[m\x1b[K,\n'}
[6.326911] (arm_controller_pkg) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/opt/ros/humble/include/rclcpp/rclcpp/rclcpp.hpp:155\x1b[m\x1b[K,\n'}
[6.326968] (arm_controller_pkg) StderrLine: {'line': b'                 from \x1b[01m\x1b[K/home/<USER>/Code/ws_0/src/arm_controller_pkg/src/RB_builder_node.cpp:1\x1b[m\x1b[K:\n'}
[6.327041] (arm_controller_pkg) StderrLine: {'line': b'\x1b[01m\x1b[K/opt/ros/humble/include/rosidl_runtime_cpp/rosidl_runtime_cpp/bounded_vector.hpp:208:3:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[Kcandidate: \xe2\x80\x98\x1b[01m\x1b[Krosidl_runtime_cpp::BoundedVector<Tp, UpperBound, Alloc>& rosidl_runtime_cpp::BoundedVector<Tp, UpperBound, Alloc>::operator=(const rosidl_runtime_cpp::BoundedVector<Tp, UpperBound, Alloc>&) [with Tp = double; long unsigned int UpperBound = 3; Alloc = std::allocator<double>]\x1b[m\x1b[K\xe2\x80\x99\n'}
[6.327090] (arm_controller_pkg) StderrLine: {'line': b'  208 |   \x1b[01;36m\x1b[Koperator\x1b[m\x1b[K=(const BoundedVector & x)\n'}
[6.327148] (arm_controller_pkg) StderrLine: {'line': b'      |   \x1b[01;36m\x1b[K^~~~~~~~\x1b[m\x1b[K\n'}
[6.327197] (arm_controller_pkg) StderrLine: {'line': b'\x1b[01m\x1b[K/opt/ros/humble/include/rosidl_runtime_cpp/rosidl_runtime_cpp/bounded_vector.hpp:208:35:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[K  no known conversion for argument 1 from \xe2\x80\x98\x1b[01m\x1b[K__gnu_cxx::__alloc_traits<std::allocator<std::vector<double, std::allocator<double> > >, std::vector<double, std::allocator<double> > >::value_type\x1b[m\x1b[K\xe2\x80\x99 {aka \xe2\x80\x98\x1b[01m\x1b[Kstd::vector<double, std::allocator<double> >\x1b[m\x1b[K\xe2\x80\x99} to \xe2\x80\x98\x1b[01m\x1b[Kconst rosidl_runtime_cpp::BoundedVector<double, 3, std::allocator<double> >&\x1b[m\x1b[K\xe2\x80\x99\n'}
[6.327287] (arm_controller_pkg) StderrLine: {'line': b'  208 |   operator=(\x1b[01;36m\x1b[Kconst BoundedVector & x\x1b[m\x1b[K)\n'}
[6.327354] (arm_controller_pkg) StderrLine: {'line': b'      |             \x1b[01;36m\x1b[K~~~~~~~~~~~~~~~~~~~~~~^\x1b[m\x1b[K\n'}
[6.327398] (arm_controller_pkg) StderrLine: {'line': b'\x1b[01m\x1b[K/opt/ros/humble/include/rosidl_runtime_cpp/rosidl_runtime_cpp/bounded_vector.hpp:219:3:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[Kcandidate: \xe2\x80\x98\x1b[01m\x1b[Krosidl_runtime_cpp::BoundedVector<Tp, UpperBound, Alloc>& rosidl_runtime_cpp::BoundedVector<Tp, UpperBound, Alloc>::operator=(rosidl_runtime_cpp::BoundedVector<Tp, UpperBound, Alloc>&&) [with Tp = double; long unsigned int UpperBound = 3; Alloc = std::allocator<double>]\x1b[m\x1b[K\xe2\x80\x99\n'}
[6.327491] (arm_controller_pkg) StderrLine: {'line': b'  219 |   \x1b[01;36m\x1b[Koperator\x1b[m\x1b[K=(BoundedVector && x)\n'}
[6.327546] (arm_controller_pkg) StderrLine: {'line': b'      |   \x1b[01;36m\x1b[K^~~~~~~~\x1b[m\x1b[K\n'}
[6.327606] (arm_controller_pkg) StderrLine: {'line': b'\x1b[01m\x1b[K/opt/ros/humble/include/rosidl_runtime_cpp/rosidl_runtime_cpp/bounded_vector.hpp:219:30:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[K  no known conversion for argument 1 from \xe2\x80\x98\x1b[01m\x1b[K__gnu_cxx::__alloc_traits<std::allocator<std::vector<double, std::allocator<double> > >, std::vector<double, std::allocator<double> > >::value_type\x1b[m\x1b[K\xe2\x80\x99 {aka \xe2\x80\x98\x1b[01m\x1b[Kstd::vector<double, std::allocator<double> >\x1b[m\x1b[K\xe2\x80\x99} to \xe2\x80\x98\x1b[01m\x1b[Krosidl_runtime_cpp::BoundedVector<double, 3, std::allocator<double> >&&\x1b[m\x1b[K\xe2\x80\x99\n'}
[6.327665] (arm_controller_pkg) StderrLine: {'line': b'  219 |   operator=(\x1b[01;36m\x1b[KBoundedVector && x\x1b[m\x1b[K)\n'}
[6.327713] (arm_controller_pkg) StderrLine: {'line': b'      |             \x1b[01;36m\x1b[K~~~~~~~~~~~~~~~~~^\x1b[m\x1b[K\n'}
[6.327740] (arm_controller_pkg) StderrLine: {'line': b'\x1b[01m\x1b[K/opt/ros/humble/include/rosidl_runtime_cpp/rosidl_runtime_cpp/bounded_vector.hpp:238:3:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[Kcandidate: \xe2\x80\x98\x1b[01m\x1b[Krosidl_runtime_cpp::BoundedVector<Tp, UpperBound, Alloc>& rosidl_runtime_cpp::BoundedVector<Tp, UpperBound, Alloc>::operator=(std::initializer_list<typename std::vector<Tp, Alloc>::value_type>) [with Tp = double; long unsigned int UpperBound = 3; Alloc = std::allocator<double>; typename std::vector<Tp, Alloc>::value_type = double]\x1b[m\x1b[K\xe2\x80\x99\n'}
[6.327774] (arm_controller_pkg) StderrLine: {'line': b'  238 |   \x1b[01;36m\x1b[Koperator\x1b[m\x1b[K=(std::initializer_list<typename Base::value_type> l)\n'}
[6.327818] (arm_controller_pkg) StderrLine: {'line': b'      |   \x1b[01;36m\x1b[K^~~~~~~~\x1b[m\x1b[K\n'}
[6.327860] (arm_controller_pkg) StderrLine: {'line': b'\x1b[01m\x1b[K/opt/ros/humble/include/rosidl_runtime_cpp/rosidl_runtime_cpp/bounded_vector.hpp:238:62:\x1b[m\x1b[K \x1b[01;36m\x1b[Knote: \x1b[m\x1b[K  no known conversion for argument 1 from \xe2\x80\x98\x1b[01m\x1b[K__gnu_cxx::__alloc_traits<std::allocator<std::vector<double, std::allocator<double> > >, std::vector<double, std::allocator<double> > >::value_type\x1b[m\x1b[K\xe2\x80\x99 {aka \xe2\x80\x98\x1b[01m\x1b[Kstd::vector<double, std::allocator<double> >\x1b[m\x1b[K\xe2\x80\x99} to \xe2\x80\x98\x1b[01m\x1b[Kstd::initializer_list<double>\x1b[m\x1b[K\xe2\x80\x99\n'}
[6.327914] (arm_controller_pkg) StderrLine: {'line': b'  238 |   operator=(\x1b[01;36m\x1b[Kstd::initializer_list<typename Base::value_type> l\x1b[m\x1b[K)\n'}
[6.327942] (arm_controller_pkg) StderrLine: {'line': b'      |             \x1b[01;36m\x1b[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^\x1b[m\x1b[K\n'}
[6.354830] (-) TimerEvent: {}
[6.455294] (-) TimerEvent: {}
[6.559226] (-) TimerEvent: {}
[6.661901] (-) TimerEvent: {}
[6.762611] (-) TimerEvent: {}
[6.874250] (-) TimerEvent: {}
[7.010646] (-) TimerEvent: {}
[7.111778] (-) TimerEvent: {}
[7.212060] (-) TimerEvent: {}
[7.312711] (-) TimerEvent: {}
[7.414741] (-) TimerEvent: {}
[7.516922] (-) TimerEvent: {}
[7.617374] (-) TimerEvent: {}
[7.720510] (-) TimerEvent: {}
[7.821001] (-) TimerEvent: {}
[7.924734] (-) TimerEvent: {}
[8.027093] (-) TimerEvent: {}
[8.127581] (-) TimerEvent: {}
[8.228272] (-) TimerEvent: {}
[8.328806] (-) TimerEvent: {}
[8.430676] (-) TimerEvent: {}
[8.534858] (-) TimerEvent: {}
[8.636429] (-) TimerEvent: {}
[8.738200] (-) TimerEvent: {}
[8.840472] (-) TimerEvent: {}
[8.942210] (-) TimerEvent: {}
[9.044803] (-) TimerEvent: {}
[9.145252] (-) TimerEvent: {}
[9.248036] (-) TimerEvent: {}
[9.353719] (-) TimerEvent: {}
[9.453987] (-) TimerEvent: {}
[9.554866] (-) TimerEvent: {}
[9.656359] (-) TimerEvent: {}
[9.757010] (-) TimerEvent: {}
[9.860133] (-) TimerEvent: {}
[9.960827] (-) TimerEvent: {}
[10.061717] (-) TimerEvent: {}
[10.166008] (-) TimerEvent: {}
[10.267465] (-) TimerEvent: {}
[10.367798] (-) TimerEvent: {}
[10.468176] (-) TimerEvent: {}
[10.570943] (-) TimerEvent: {}
[10.671216] (-) TimerEvent: {}
[10.771484] (-) TimerEvent: {}
[10.873875] (-) TimerEvent: {}
[10.974451] (-) TimerEvent: {}
[10.982263] (arm_controller_pkg) StderrLine: {'line': b'gmake[2]: *** [CMakeFiles/RB_builder_node.dir/build.make:79: CMakeFiles/RB_builder_node.dir/src/RB_builder_node.cpp.o] Error 1\n'}
[10.983836] (arm_controller_pkg) StderrLine: {'line': b'gmake[1]: *** [CMakeFiles/Makefile2:188: CMakeFiles/RB_builder_node.dir/all] Error 2\n'}
[10.983940] (arm_controller_pkg) StderrLine: {'line': b'gmake: *** [Makefile:146: all] Error 2\n'}
[11.003957] (arm_controller_pkg) CommandEnded: {'returncode': 2}
[11.040033] (arm_controller_pkg) JobEnded: {'identifier': 'arm_controller_pkg', 'rc': 2}
[11.052117] (-) EventReactorShutdown: {}
