[ 25%] [32mBuilding CXX object CMakeFiles/RB_builder_node.dir/src/RB_builder_node.cpp.o[0m
[ 75%] Built target path_planning_node
[01m[K/home/<USER>/Code/ws_0/src/arm_controller_pkg/src/RB_builder_node.cpp:[m[K In member function ‘[01m[Kvoid RB_builder_node::create_object()[m[K’:
[01m[K/home/<USER>/Code/ws_0/src/arm_controller_pkg/src/RB_builder_node.cpp:100:73:[m[K [01;31m[Kerror: [m[Kno match for ‘[01m[Koperator=[m[K’ (operand types are ‘[01m[Kshape_msgs::msg::SolidPrimitive_<std::allocator<void> >::_dimensions_type[m[K’ {aka ‘[01m[Krosidl_runtime_cpp::BoundedVector<double, 3, std::allocator<double> >[m[K’} and ‘[01m[K__gnu_cxx::__alloc_traits<std::allocator<std::vector<double, std::allocator<double> > >, std::vector<double, std::allocator<double> > >::value_type[m[K’ {aka ‘[01m[Kstd::vector<double, std::allocator<double> >[m[K’})
  100 |             collision_object.primitives[i].dimensions = box_dimensions[i[01;31m[K][m[K;
      |                                                                         [01;31m[K^[m[K
In file included from [01m[K/opt/ros/humble/include/builtin_interfaces/builtin_interfaces/msg/detail/duration__struct.hpp:14[m[K,
                 from [01m[K/opt/ros/humble/include/builtin_interfaces/builtin_interfaces/msg/duration.hpp:7[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/duration.hpp:20[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/qos.hpp:20[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/node_interfaces/node_graph_interface.hpp:32[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/client.hpp:42[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/callback_group.hpp:24[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/any_executable.hpp:20[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategy.hpp:25[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/memory_strategies.hpp:18[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executor_options.hpp:20[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executor.hpp:37[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executors/multi_threaded_executor.hpp:25[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/executors.hpp:21[m[K,
                 from [01m[K/opt/ros/humble/include/rclcpp/rclcpp/rclcpp.hpp:155[m[K,
                 from [01m[K/home/<USER>/Code/ws_0/src/arm_controller_pkg/src/RB_builder_node.cpp:1[m[K:
[01m[K/opt/ros/humble/include/rosidl_runtime_cpp/rosidl_runtime_cpp/bounded_vector.hpp:208:3:[m[K [01;36m[Knote: [m[Kcandidate: ‘[01m[Krosidl_runtime_cpp::BoundedVector<Tp, UpperBound, Alloc>& rosidl_runtime_cpp::BoundedVector<Tp, UpperBound, Alloc>::operator=(const rosidl_runtime_cpp::BoundedVector<Tp, UpperBound, Alloc>&) [with Tp = double; long unsigned int UpperBound = 3; Alloc = std::allocator<double>][m[K’
  208 |   [01;36m[Koperator[m[K=(const BoundedVector & x)
      |   [01;36m[K^~~~~~~~[m[K
[01m[K/opt/ros/humble/include/rosidl_runtime_cpp/rosidl_runtime_cpp/bounded_vector.hpp:208:35:[m[K [01;36m[Knote: [m[K  no known conversion for argument 1 from ‘[01m[K__gnu_cxx::__alloc_traits<std::allocator<std::vector<double, std::allocator<double> > >, std::vector<double, std::allocator<double> > >::value_type[m[K’ {aka ‘[01m[Kstd::vector<double, std::allocator<double> >[m[K’} to ‘[01m[Kconst rosidl_runtime_cpp::BoundedVector<double, 3, std::allocator<double> >&[m[K’
  208 |   operator=([01;36m[Kconst BoundedVector & x[m[K)
      |             [01;36m[K~~~~~~~~~~~~~~~~~~~~~~^[m[K
[01m[K/opt/ros/humble/include/rosidl_runtime_cpp/rosidl_runtime_cpp/bounded_vector.hpp:219:3:[m[K [01;36m[Knote: [m[Kcandidate: ‘[01m[Krosidl_runtime_cpp::BoundedVector<Tp, UpperBound, Alloc>& rosidl_runtime_cpp::BoundedVector<Tp, UpperBound, Alloc>::operator=(rosidl_runtime_cpp::BoundedVector<Tp, UpperBound, Alloc>&&) [with Tp = double; long unsigned int UpperBound = 3; Alloc = std::allocator<double>][m[K’
  219 |   [01;36m[Koperator[m[K=(BoundedVector && x)
      |   [01;36m[K^~~~~~~~[m[K
[01m[K/opt/ros/humble/include/rosidl_runtime_cpp/rosidl_runtime_cpp/bounded_vector.hpp:219:30:[m[K [01;36m[Knote: [m[K  no known conversion for argument 1 from ‘[01m[K__gnu_cxx::__alloc_traits<std::allocator<std::vector<double, std::allocator<double> > >, std::vector<double, std::allocator<double> > >::value_type[m[K’ {aka ‘[01m[Kstd::vector<double, std::allocator<double> >[m[K’} to ‘[01m[Krosidl_runtime_cpp::BoundedVector<double, 3, std::allocator<double> >&&[m[K’
  219 |   operator=([01;36m[KBoundedVector && x[m[K)
      |             [01;36m[K~~~~~~~~~~~~~~~~~^[m[K
[01m[K/opt/ros/humble/include/rosidl_runtime_cpp/rosidl_runtime_cpp/bounded_vector.hpp:238:3:[m[K [01;36m[Knote: [m[Kcandidate: ‘[01m[Krosidl_runtime_cpp::BoundedVector<Tp, UpperBound, Alloc>& rosidl_runtime_cpp::BoundedVector<Tp, UpperBound, Alloc>::operator=(std::initializer_list<typename std::vector<Tp, Alloc>::value_type>) [with Tp = double; long unsigned int UpperBound = 3; Alloc = std::allocator<double>; typename std::vector<Tp, Alloc>::value_type = double][m[K’
  238 |   [01;36m[Koperator[m[K=(std::initializer_list<typename Base::value_type> l)
      |   [01;36m[K^~~~~~~~[m[K
[01m[K/opt/ros/humble/include/rosidl_runtime_cpp/rosidl_runtime_cpp/bounded_vector.hpp:238:62:[m[K [01;36m[Knote: [m[K  no known conversion for argument 1 from ‘[01m[K__gnu_cxx::__alloc_traits<std::allocator<std::vector<double, std::allocator<double> > >, std::vector<double, std::allocator<double> > >::value_type[m[K’ {aka ‘[01m[Kstd::vector<double, std::allocator<double> >[m[K’} to ‘[01m[Kstd::initializer_list<double>[m[K’
  238 |   operator=([01;36m[Kstd::initializer_list<typename Base::value_type> l[m[K)
      |             [01;36m[K~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^[m[K
gmake[2]: *** [CMakeFiles/RB_builder_node.dir/build.make:79: CMakeFiles/RB_builder_node.dir/src/RB_builder_node.cpp.o] Error 1
gmake[1]: *** [CMakeFiles/Makefile2:188: CMakeFiles/RB_builder_node.dir/all] Error 2
gmake: *** [Makefile:146: all] Error 2
