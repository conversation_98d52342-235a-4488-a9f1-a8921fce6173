[33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ConfigExtras.cmake:9 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
  CMakeLists.txt:16 (find_package)
This warning is for project developers.  Use -Wno-dev to suppress it.
[0m
[33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_core/share/moveit_core/cmake/ConfigExtras.cmake:3 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  /home/<USER>/moveit2_ws/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)
  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
  CMakeLists.txt:16 (find_package)
This warning is for project developers.  Use -Wno-dev to suppress it.
[0m
[33mCMake Warning (dev) at /opt/ros/humble/share/geometric_shapes/cmake/ConfigExtras.cmake:31 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  /opt/ros/humble/share/geometric_shapes/cmake/geometric_shapesConfig.cmake:41 (include)
  /home/<USER>/moveit2_ws/install/moveit_core/share/moveit_core/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/moveit2_ws/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)
  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
  CMakeLists.txt:16 (find_package)
This warning is for project developers.  Use -Wno-dev to suppress it.
[0m
[33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_ros_planning/share/moveit_ros_planning/cmake/ConfigExtras.cmake:3 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  /home/<USER>/moveit2_ws/install/moveit_ros_planning/share/moveit_ros_planning/cmake/moveit_ros_planningConfig.cmake:41 (include)
  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
  CMakeLists.txt:16 (find_package)
This warning is for project developers.  Use -Wno-dev to suppress it.
[0m
[33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_ros_warehouse/share/moveit_ros_warehouse/cmake/ConfigExtras.cmake:3 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  /home/<USER>/moveit2_ws/install/moveit_ros_warehouse/share/moveit_ros_warehouse/cmake/moveit_ros_warehouseConfig.cmake:41 (include)
  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
  CMakeLists.txt:16 (find_package)
This warning is for project developers.  Use -Wno-dev to suppress it.
[0m
[33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_ros_move_group/share/moveit_ros_move_group/cmake/ConfigExtras.cmake:3 (find_package):
  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
  --help-policy CMP0167" for policy details.  Use the cmake_policy command to
  set the policy and suppress this warning.

Call Stack (most recent call first):
  /home/<USER>/moveit2_ws/install/moveit_ros_move_group/share/moveit_ros_move_group/cmake/moveit_ros_move_groupConfig.cmake:41 (include)
  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
  CMakeLists.txt:16 (find_package)
This warning is for project developers.  Use -Wno-dev to suppress it.
[0m
/usr/bin/ld: CMakeFiles/RB_builder_node.dir/src/RB_builder_node.cpp.o: in function `tf2::impl::toQuaternion(geometry_msgs::msg::Quaternion_<std::allocator<void> > const&)':
RB_builder_node.cpp:(.text._ZN3tf24impl12toQuaternionERKN13geometry_msgs3msg11Quaternion_ISaIvEEE[_ZN3tf24impl12toQuaternionERKN13geometry_msgs3msg11Quaternion_ISaIvEEE]+0x34): undefined reference to `tf2::fromMsg(geometry_msgs::msg::Quaternion_<std::allocator<void> > const&, tf2::Quaternion&)'
collect2: error: ld returned 1 exit status
gmake[2]: *** [CMakeFiles/RB_builder_node.dir/build.make:359: RB_builder_node] Error 1
gmake[1]: *** [CMakeFiles/Makefile2:188: CMakeFiles/RB_builder_node.dir/all] Error 2
gmake: *** [Makefile:146: all] Error 2
