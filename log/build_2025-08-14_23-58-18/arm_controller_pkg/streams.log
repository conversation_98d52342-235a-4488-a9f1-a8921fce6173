[0.012s] Invoking command in '/home/<USER>/Code/ws_0/build/arm_controller_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/arm_controller_pkg -- -j8 -l8
[0.104s] -- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)
[0.396s] -- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)
[0.453s] -- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)
[0.456s] -- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)
[0.468s] -- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)
[0.489s] -- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c
[0.513s] -- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp
[0.581s] -- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)
[0.583s] -- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)
[0.902s] -- Using RMW implementation 'rmw_fastrtps_cpp' as default
[0.968s] -- Found tf2: 0.25.15 (/opt/ros/humble/share/tf2/cmake)
[1.009s] -- Found tf2_geometry_msgs: 0.25.15 (/opt/ros/humble/share/tf2_geometry_msgs/cmake)
[1.015s] -- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)
[1.015s] -- Ensuring Eigen3 include directory is part of orocos-kdl CMake target
[1.117s] -- Found moveit_ros_planning_interface: 2.5.9 (/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake)
[1.118s] [33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ConfigExtras.cmake:9 (find_package):
[1.118s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.118s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.118s]   set the policy and suppress this warning.
[1.118s] 
[1.118s] Call Stack (most recent call first):
[1.119s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
[1.119s]   CMakeLists.txt:16 (find_package)
[1.119s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.119s] [0m
[1.131s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: date_time filesystem program_options system thread
[1.267s] [33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_core/share/moveit_core/cmake/ConfigExtras.cmake:3 (find_package):
[1.267s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.267s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.267s]   set the policy and suppress this warning.
[1.267s] 
[1.267s] Call Stack (most recent call first):
[1.267s]   /home/<USER>/moveit2_ws/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)
[1.267s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.268s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
[1.268s]   CMakeLists.txt:16 (find_package)
[1.268s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.268s] [0m
[1.276s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: chrono date_time filesystem iostreams program_options regex serialization system thread
[1.307s] -- Found parameter_traits: 0.5.0 (/opt/ros/humble/share/parameter_traits/cmake)
[1.313s] -- Found rclcpp_lifecycle: 16.0.14 (/opt/ros/humble/share/rclcpp_lifecycle/cmake)
[1.354s] [33mCMake Warning (dev) at /opt/ros/humble/share/geometric_shapes/cmake/ConfigExtras.cmake:31 (find_package):
[1.354s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.354s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.354s]   set the policy and suppress this warning.
[1.354s] 
[1.355s] Call Stack (most recent call first):
[1.355s]   /opt/ros/humble/share/geometric_shapes/cmake/geometric_shapesConfig.cmake:41 (include)
[1.355s]   /home/<USER>/moveit2_ws/install/moveit_core/share/moveit_core/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.355s]   /home/<USER>/moveit2_ws/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)
[1.355s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.355s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
[1.355s]   CMakeLists.txt:16 (find_package)
[1.355s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.355s] [0m
[1.361s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: filesystem
[1.451s] -- library: /usr/lib/aarch64-linux-gnu/libcurl.so
[1.549s] [33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_ros_planning/share/moveit_ros_planning/cmake/ConfigExtras.cmake:3 (find_package):
[1.553s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.553s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.553s]   set the policy and suppress this warning.
[1.553s] 
[1.553s] Call Stack (most recent call first):
[1.554s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning/share/moveit_ros_planning/cmake/moveit_ros_planningConfig.cmake:41 (include)
[1.554s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.554s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
[1.554s]   CMakeLists.txt:16 (find_package)
[1.554s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.554s] [0m
[1.564s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem date_time program_options thread chrono
[1.592s] [33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_ros_warehouse/share/moveit_ros_warehouse/cmake/ConfigExtras.cmake:3 (find_package):
[1.592s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.592s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.592s]   set the policy and suppress this warning.
[1.592s] 
[1.592s] Call Stack (most recent call first):
[1.592s]   /home/<USER>/moveit2_ws/install/moveit_ros_warehouse/share/moveit_ros_warehouse/cmake/moveit_ros_warehouseConfig.cmake:41 (include)
[1.593s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.593s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
[1.594s]   CMakeLists.txt:16 (find_package)
[1.594s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.594s] [0m
[1.600s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: thread system filesystem regex date_time program_options
[1.617s] [33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_ros_move_group/share/moveit_ros_move_group/cmake/ConfigExtras.cmake:3 (find_package):
[1.617s]   Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake
[1.617s]   --help-policy CMP0167" for policy details.  Use the cmake_policy command to
[1.617s]   set the policy and suppress this warning.
[1.617s] 
[1.617s] Call Stack (most recent call first):
[1.617s]   /home/<USER>/moveit2_ws/install/moveit_ros_move_group/share/moveit_ros_move_group/cmake/moveit_ros_move_groupConfig.cmake:41 (include)
[1.617s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)
[1.617s]   /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)
[1.617s]   CMakeLists.txt:16 (find_package)
[1.617s] This warning is for project developers.  Use -Wno-dev to suppress it.
[1.619s] [0m
[1.624s] -- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem date_time program_options thread
[1.682s] -- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)
[1.738s] -- Added test 'cppcheck' to perform static code analysis on C / C++ code
[1.738s] -- Configured cppcheck include dirs: /home/<USER>/Code/ws_0/src/arm_controller_pkg/include
[1.739s] -- Configured cppcheck exclude dirs and/or files: 
[1.740s] -- Added test 'lint_cmake' to check CMake code style
[1.740s] -- Added test 'uncrustify' to check C / C++ code style
[1.740s] -- Configured uncrustify additional arguments: 
[1.741s] -- Added test 'xmllint' to check XML markup files
[1.742s] -- Configuring done (1.6s)
[1.817s] -- Generating done (0.1s)
[1.830s] -- Build files have been written to: /home/<USER>/Code/ws_0/build/arm_controller_pkg
[1.859s] [ 25%] [32mBuilding CXX object CMakeFiles/RB_builder_node.dir/src/RB_builder_node.cpp.o[0m
[1.896s] [ 75%] Built target path_planning_node
[15.094s] [100%] [32m[1mLinking CXX executable RB_builder_node[0m
[16.213s] /usr/bin/ld: CMakeFiles/RB_builder_node.dir/src/RB_builder_node.cpp.o: in function `tf2::impl::toQuaternion(geometry_msgs::msg::Quaternion_<std::allocator<void> > const&)':
[16.213s] RB_builder_node.cpp:(.text._ZN3tf24impl12toQuaternionERKN13geometry_msgs3msg11Quaternion_ISaIvEEE[_ZN3tf24impl12toQuaternionERKN13geometry_msgs3msg11Quaternion_ISaIvEEE]+0x34): undefined reference to `tf2::fromMsg(geometry_msgs::msg::Quaternion_<std::allocator<void> > const&, tf2::Quaternion&)'
[16.263s] collect2: error: ld returned 1 exit status
[16.266s] gmake[2]: *** [CMakeFiles/RB_builder_node.dir/build.make:359: RB_builder_node] Error 1
[16.275s] gmake[1]: *** [CMakeFiles/Makefile2:188: CMakeFiles/RB_builder_node.dir/all] Error 2
[16.277s] gmake: *** [Makefile:146: all] Error 2
[16.287s] Invoked command in '/home/<USER>/Code/ws_0/build/arm_controller_pkg' returned '2': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/arm_controller_pkg -- -j8 -l8
