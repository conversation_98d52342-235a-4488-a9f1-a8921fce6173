[0.000000] (-) TimerEvent: {}
[0.000122] (-) JobUnselected: {'identifier': 'engineer_vision_pkg'}
[0.000151] (-) JobUnselected: {'identifier': 'mindvision_camera'}
[0.000160] (-) JobUnselected: {'identifier': 'rm_vision'}
[0.000168] (-) JobUnselected: {'identifier': 'robot_description'}
[0.000178] (arm_controller_pkg) JobQueued: {'identifier': 'arm_controller_pkg', 'dependencies': OrderedDict()}
[0.000188] (arm_controller_pkg) JobStarted: {'identifier': 'arm_controller_pkg'}
[0.008714] (arm_controller_pkg) JobProgress: {'identifier': 'arm_controller_pkg', 'progress': 'cmake'}
[0.009478] (arm_controller_pkg) JobProgress: {'identifier': 'arm_controller_pkg', 'progress': 'build'}
[0.009989] (arm_controller_pkg) Command: {'cmd': ['/usr/local/cmake/bin/cmake', '--build', '/home/<USER>/Code/ws_0/build/arm_controller_pkg', '--', '-j8', '-l8'], 'cwd': '/home/<USER>/Code/ws_0/build/arm_controller_pkg', 'env': OrderedDict([('USER', 'mac'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner_testutils/lib:/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner/lib:/home/<USER>/moveit2_ws/install/moveit_visual_tools/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_visualization/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_demo/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_capabilities/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_core/lib:/home/<USER>/moveit2_ws/install/moveit_ros_control_interface/lib:/home/<USER>/moveit2_ws/install/moveit_simple_controller_manager/lib:/home/<USER>/moveit2_ws/install/moveit_setup_assistant/lib:/home/<USER>/moveit2_ws/install/moveit_setup_srdf_plugins/lib:/home/<USER>/moveit2_ws/install/moveit_setup_core_plugins/lib:/home/<USER>/moveit2_ws/install/moveit_setup_controllers/lib:/home/<USER>/moveit2_ws/install/moveit_setup_app_plugins/lib:/home/<USER>/moveit2_ws/install/moveit_setup_framework/lib:/home/<USER>/moveit2_ws/install/moveit_servo/lib:/home/<USER>/moveit2_ws/install/moveit_ros_visualization/lib:/home/<USER>/moveit2_ws/install/moveit_hybrid_planning/lib:/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/lib:/home/<USER>/moveit2_ws/install/moveit_ros_benchmarks/lib:/home/<USER>/moveit2_ws/install/moveit_ros_warehouse/lib:/home/<USER>/moveit2_ws/install/moveit_ros_robot_interaction/lib:/home/<USER>/moveit2_ws/install/moveit_ros_perception/lib:/home/<USER>/moveit2_ws/install/moveit_ros_move_group/lib:/home/<USER>/moveit2_ws/install/moveit_planners_ompl/lib:/home/<USER>/moveit2_ws/install/moveit_ros_planning/lib:/home/<USER>/moveit2_ws/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/moveit2_ws/install/moveit_planners_chomp/lib:/home/<USER>/moveit2_ws/install/moveit_kinematics/lib:/home/<USER>/moveit2_ws/install/moveit_chomp_optimizer_adapter/lib:/home/<USER>/moveit2_ws/install/chomp_motion_planner/lib:/home/<USER>/moveit2_ws/install/moveit_core/lib:/home/<USER>/moveit2_ws/install/srdfdom/lib:/home/<USER>/moveit2_ws/install/rviz_marker_tools/lib:/home/<USER>/moveit2_ws/install/rosparam_shortcuts/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_msgs/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/bin/helpers/browser.sh'), ('LESS', '-FX'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/Code/ws_0'), ('TERM_PROGRAM_VERSION', '1.103.1'), ('VSCODE_IPC_HOOK_CLI', '/run/user/502/vscode-ipc-89356d9e-2569-497e-8a14-9200925310d6.sock'), ('ROS_PYTHON_VERSION', '3'), ('PAGER', 'cat'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/502/bus'), ('COLORTERM', 'truecolor'), ('COLCON_PREFIX_PATH', '/home/<USER>/moveit2_ws/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mac'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/home/<USER>/moveit2_ws/install/moveit_core/bin:/opt/ros/humble/bin:/usr/local/cmake/bin:/opt/orbstack-guest/bin-hiprio:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/opt/orbstack-guest/bin:/opt/orbstack-guest/data/bin/cmdlinks'), ('PYTHON_EXECUTABLE', '/usr/bin/python3.10'), ('XDG_RUNTIME_DIR', '/run/user/502'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-ec2f90e73ca2d53c.txt'), ('LANG', 'C.UTF-8'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/502/vscode-git-4bcffef955.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AUTH_SOCK', '/run/user/502/vscode-ssh-auth-sock-589875342'), ('AMENT_PREFIX_PATH', '/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner_testutils:/home/<USER>/moveit2_ws/install/moveit_runtime:/home/<USER>/moveit2_ws/install/moveit2_tutorials:/home/<USER>/moveit2_ws/install/moveit:/home/<USER>/moveit2_ws/install/moveit_planners:/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner:/home/<USER>/moveit2_ws/install/moveit_test:/home/<USER>/moveit2_ws/install/moveit_visual_tools:/home/<USER>/moveit2_ws/install/moveit_task_constructor_visualization:/home/<USER>/moveit2_ws/install/moveit_task_constructor_demo:/home/<USER>/moveit2_ws/install/moveit_task_constructor_capabilities:/home/<USER>/moveit2_ws/install/moveit_task_constructor_core:/home/<USER>/moveit2_ws/install/moveit_ros_control_interface:/home/<USER>/moveit2_ws/install/moveit_plugins:/home/<USER>/moveit2_ws/install/moveit_simple_controller_manager:/home/<USER>/moveit2_ws/install/moveit_setup_assistant:/home/<USER>/moveit2_ws/install/moveit_setup_srdf_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_core_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_controllers:/home/<USER>/moveit2_ws/install/moveit_setup_app_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_framework:/home/<USER>/moveit2_ws/install/moveit_servo:/home/<USER>/moveit2_ws/install/moveit_ros:/home/<USER>/moveit2_ws/install/moveit_ros_visualization:/home/<USER>/moveit2_ws/install/moveit_hybrid_planning:/home/<USER>/moveit2_ws/install/move_program:/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface:/home/<USER>/moveit2_ws/install/moveit_ros_benchmarks:/home/<USER>/moveit2_ws/install/moveit_ros_warehouse:/home/<USER>/moveit2_ws/install/moveit_ros_robot_interaction:/home/<USER>/moveit2_ws/install/moveit_ros_perception:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_pg70_support:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_moveit_config:/home/<USER>/moveit2_ws/install/moveit_ros_move_group:/home/<USER>/moveit2_ws/install/moveit_planners_ompl:/home/<USER>/moveit2_ws/install/moveit_ros_planning:/home/<USER>/moveit2_ws/install/moveit_ros_occupancy_map_monitor:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/moveit2_ws/install/moveit_planners_chomp:/home/<USER>/moveit2_ws/install/moveit_kinematics:/home/<USER>/moveit2_ws/install/moveit_chomp_optimizer_adapter:/home/<USER>/moveit2_ws/install/chomp_motion_planner:/home/<USER>/moveit2_ws/install/moveit_core:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/srdfdom:/home/<USER>/moveit2_ws/install/rviz_marker_tools:/home/<USER>/moveit2_ws/install/rosparam_shortcuts:/home/<USER>/moveit2_ws/install/moveit_task_constructor_msgs:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_support:/home/<USER>/moveit2_ws/install/moveit_resources:/home/<USER>/moveit2_ws/install/moveit_resources_pr2_description:/home/<USER>/moveit2_ws/install/moveit_resources_panda_moveit_config:/home/<USER>/moveit2_ws/install/moveit_resources_panda_description:/home/<USER>/moveit2_ws/install/moveit_resources_fanuc_moveit_config:/home/<USER>/moveit2_ws/install/moveit_resources_fanuc_description:/home/<USER>/moveit2_ws/install/moveit_common:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('CMAKE_ARGS', '-DPYTHON_EXECUTABLE=/usr/bin/python3.10 -DPython3_EXECUTABLE=/usr/bin/python3.10'), ('CMAKE_MODULE_PATH', '/home/<USER>/moveit2_ws/install/moveit_task_constructor_core/share/moveit_task_constructor_core/cmake'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('PWD', '/home/<USER>/Code/ws_0/build/arm_controller_pkg'), ('SSH_CONNECTION', '::1 0 ::1 22'), ('XDG_DATA_DIRS', '/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/moveit2_ws/install/moveit_task_constructor_core/local/lib/python3.10/dist-packages:/home/<USER>/moveit2_ws/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/moveit2_ws/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/moveit2_ws/install/moveit_task_constructor_msgs/local/lib/python3.10/dist-packages:/home/<USER>/moveit2_ws/install/launch_param_builder/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('Python3_EXECUTABLE', '/usr/bin/python3.10'), ('CMAKE_PREFIX_PATH', '/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner_testutils:/home/<USER>/moveit2_ws/install/moveit_runtime:/home/<USER>/moveit2_ws/install/moveit2_tutorials:/home/<USER>/moveit2_ws/install/moveit:/home/<USER>/moveit2_ws/install/moveit_planners:/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner:/home/<USER>/moveit2_ws/install/moveit_test:/home/<USER>/moveit2_ws/install/moveit_visual_tools:/home/<USER>/moveit2_ws/install/moveit_task_constructor_visualization:/home/<USER>/moveit2_ws/install/moveit_task_constructor_demo:/home/<USER>/moveit2_ws/install/moveit_task_constructor_capabilities:/home/<USER>/moveit2_ws/install/moveit_task_constructor_core:/home/<USER>/moveit2_ws/install/moveit_ros_control_interface:/home/<USER>/moveit2_ws/install/moveit_plugins:/home/<USER>/moveit2_ws/install/moveit_simple_controller_manager:/home/<USER>/moveit2_ws/install/moveit_setup_assistant:/home/<USER>/moveit2_ws/install/moveit_setup_srdf_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_core_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_controllers:/home/<USER>/moveit2_ws/install/moveit_setup_app_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_framework:/home/<USER>/moveit2_ws/install/moveit_servo:/home/<USER>/moveit2_ws/install/moveit_ros:/home/<USER>/moveit2_ws/install/moveit_ros_visualization:/home/<USER>/moveit2_ws/install/moveit_hybrid_planning:/home/<USER>/moveit2_ws/install/move_program:/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface:/home/<USER>/moveit2_ws/install/moveit_ros_benchmarks:/home/<USER>/moveit2_ws/install/moveit_ros_warehouse:/home/<USER>/moveit2_ws/install/moveit_ros_robot_interaction:/home/<USER>/moveit2_ws/install/moveit_ros_perception:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_pg70_support:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_moveit_config:/home/<USER>/moveit2_ws/install/moveit_ros_move_group:/home/<USER>/moveit2_ws/install/moveit_planners_ompl:/home/<USER>/moveit2_ws/install/moveit_ros_planning:/home/<USER>/moveit2_ws/install/moveit_ros_occupancy_map_monitor:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/moveit2_ws/install/moveit_planners_chomp:/home/<USER>/moveit2_ws/install/moveit_kinematics:/home/<USER>/moveit2_ws/install/moveit_chomp_optimizer_adapter:/home/<USER>/moveit2_ws/install/chomp_motion_planner:/home/<USER>/moveit2_ws/install/moveit_core:/home/<USER>/moveit2_ws/install/srdfdom:/home/<USER>/moveit2_ws/install/rviz_marker_tools:/home/<USER>/moveit2_ws/install/rosparam_shortcuts:/home/<USER>/moveit2_ws/install/moveit_task_constructor_msgs:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_support:/home/<USER>/moveit2_ws/install/moveit_resources:/home/<USER>/moveit2_ws/install/moveit_resources_pr2_description:/home/<USER>/moveit2_ws/install/moveit_resources_panda_moveit_config:/home/<USER>/moveit2_ws/install/moveit_resources_panda_description:/home/<USER>/moveit2_ws/install/moveit_resources_fanuc_moveit_config:/home/<USER>/moveit2_ws/install/moveit_resources_fanuc_description:/home/<USER>/moveit2_ws/install/moveit_common:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble')]), 'shell': False}
[0.099126] (-) TimerEvent: {}
[0.103043] (arm_controller_pkg) StdoutLine: {'line': b'-- Found ament_cmake: 1.3.12 (/opt/ros/humble/share/ament_cmake/cmake)\n'}
[0.199863] (-) TimerEvent: {}
[0.309249] (-) TimerEvent: {}
[0.395855] (arm_controller_pkg) StdoutLine: {'line': b'-- Found rclcpp: 16.0.14 (/opt/ros/humble/share/rclcpp/cmake)\n'}
[0.410175] (-) TimerEvent: {}
[0.452947] (arm_controller_pkg) StdoutLine: {'line': b'-- Found rosidl_generator_c: 3.1.7 (/opt/ros/humble/share/rosidl_generator_c/cmake)\n'}
[0.455341] (arm_controller_pkg) StdoutLine: {'line': b'-- Found rosidl_adapter: 3.1.7 (/opt/ros/humble/share/rosidl_adapter/cmake)\n'}
[0.468169] (arm_controller_pkg) StdoutLine: {'line': b'-- Found rosidl_generator_cpp: 3.1.7 (/opt/ros/humble/share/rosidl_generator_cpp/cmake)\n'}
[0.488771] (arm_controller_pkg) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_c: rosidl_typesupport_fastrtps_c;rosidl_typesupport_introspection_c\n'}
[0.511024] (-) TimerEvent: {}
[0.512840] (arm_controller_pkg) StdoutLine: {'line': b'-- Using all available rosidl_typesupport_cpp: rosidl_typesupport_fastrtps_cpp;rosidl_typesupport_introspection_cpp\n'}
[0.581484] (arm_controller_pkg) StdoutLine: {'line': b'-- Found rmw_implementation_cmake: 6.1.2 (/opt/ros/humble/share/rmw_implementation_cmake/cmake)\n'}
[0.583127] (arm_controller_pkg) StdoutLine: {'line': b'-- Found rmw_fastrtps_cpp: 6.2.8 (/opt/ros/humble/share/rmw_fastrtps_cpp/cmake)\n'}
[0.611106] (-) TimerEvent: {}
[0.715161] (-) TimerEvent: {}
[0.815890] (-) TimerEvent: {}
[0.902030] (arm_controller_pkg) StdoutLine: {'line': b"-- Using RMW implementation 'rmw_fastrtps_cpp' as default\n"}
[0.917183] (-) TimerEvent: {}
[0.968151] (arm_controller_pkg) StdoutLine: {'line': b'-- Found tf2: 0.25.15 (/opt/ros/humble/share/tf2/cmake)\n'}
[1.009120] (arm_controller_pkg) StdoutLine: {'line': b'-- Found tf2_geometry_msgs: 0.25.15 (/opt/ros/humble/share/tf2_geometry_msgs/cmake)\n'}
[1.015186] (arm_controller_pkg) StdoutLine: {'line': b'-- Found eigen3_cmake_module: 0.1.1 (/opt/ros/humble/share/eigen3_cmake_module/cmake)\n'}
[1.015360] (arm_controller_pkg) StdoutLine: {'line': b'-- Ensuring Eigen3 include directory is part of orocos-kdl CMake target\n'}
[1.017233] (-) TimerEvent: {}
[1.116784] (arm_controller_pkg) StdoutLine: {'line': b'-- Found moveit_ros_planning_interface: 2.5.9 (/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake)\n'}
[1.117299] (-) TimerEvent: {}
[1.117578] (arm_controller_pkg) StderrLine: {'line': b'\x1b[33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ConfigExtras.cmake:9 (find_package):\n'}
[1.117776] (arm_controller_pkg) StderrLine: {'line': b'  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake\n'}
[1.117831] (arm_controller_pkg) StderrLine: {'line': b'  --help-policy CMP0167" for policy details.  Use the cmake_policy command to\n'}
[1.117930] (arm_controller_pkg) StderrLine: {'line': b'  set the policy and suppress this warning.\n'}
[1.117964] (arm_controller_pkg) StderrLine: {'line': b'\n'}
[1.117994] (arm_controller_pkg) StderrLine: {'line': b'Call Stack (most recent call first):\n'}
[1.119025] (arm_controller_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)\n'}
[1.119139] (arm_controller_pkg) StderrLine: {'line': b'  CMakeLists.txt:16 (find_package)\n'}
[1.119179] (arm_controller_pkg) StderrLine: {'line': b'This warning is for project developers.  Use -Wno-dev to suppress it.\n'}
[1.119211] (arm_controller_pkg) StderrLine: {'line': b'\x1b[0m\n'}
[1.130452] (arm_controller_pkg) StdoutLine: {'line': b'-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: date_time filesystem program_options system thread\n'}
[1.218173] (-) TimerEvent: {}
[1.266692] (arm_controller_pkg) StderrLine: {'line': b'\x1b[33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_core/share/moveit_core/cmake/ConfigExtras.cmake:3 (find_package):\n'}
[1.266875] (arm_controller_pkg) StderrLine: {'line': b'  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake\n'}
[1.267352] (arm_controller_pkg) StderrLine: {'line': b'  --help-policy CMP0167" for policy details.  Use the cmake_policy command to\n'}
[1.267427] (arm_controller_pkg) StderrLine: {'line': b'  set the policy and suppress this warning.\n'}
[1.267475] (arm_controller_pkg) StderrLine: {'line': b'\n'}
[1.267523] (arm_controller_pkg) StderrLine: {'line': b'Call Stack (most recent call first):\n'}
[1.267570] (arm_controller_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)\n'}
[1.267616] (arm_controller_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)\n'}
[1.267668] (arm_controller_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)\n'}
[1.267699] (arm_controller_pkg) StderrLine: {'line': b'  CMakeLists.txt:16 (find_package)\n'}
[1.267738] (arm_controller_pkg) StderrLine: {'line': b'This warning is for project developers.  Use -Wno-dev to suppress it.\n'}
[1.267768] (arm_controller_pkg) StderrLine: {'line': b'\x1b[0m\n'}
[1.276499] (arm_controller_pkg) StdoutLine: {'line': b'-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: chrono date_time filesystem iostreams program_options regex serialization system thread\n'}
[1.306592] (arm_controller_pkg) StdoutLine: {'line': b'-- Found parameter_traits: 0.5.0 (/opt/ros/humble/share/parameter_traits/cmake)\n'}
[1.312862] (arm_controller_pkg) StdoutLine: {'line': b'-- Found rclcpp_lifecycle: 16.0.14 (/opt/ros/humble/share/rclcpp_lifecycle/cmake)\n'}
[1.323753] (-) TimerEvent: {}
[1.353745] (arm_controller_pkg) StderrLine: {'line': b'\x1b[33mCMake Warning (dev) at /opt/ros/humble/share/geometric_shapes/cmake/ConfigExtras.cmake:31 (find_package):\n'}
[1.354138] (arm_controller_pkg) StderrLine: {'line': b'  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake\n'}
[1.354327] (arm_controller_pkg) StderrLine: {'line': b'  --help-policy CMP0167" for policy details.  Use the cmake_policy command to\n'}
[1.354438] (arm_controller_pkg) StderrLine: {'line': b'  set the policy and suppress this warning.\n'}
[1.354542] (arm_controller_pkg) StderrLine: {'line': b'\n'}
[1.354663] (arm_controller_pkg) StderrLine: {'line': b'Call Stack (most recent call first):\n'}
[1.354825] (arm_controller_pkg) StderrLine: {'line': b'  /opt/ros/humble/share/geometric_shapes/cmake/geometric_shapesConfig.cmake:41 (include)\n'}
[1.354949] (arm_controller_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_core/share/moveit_core/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)\n'}
[1.355061] (arm_controller_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_core/share/moveit_core/cmake/moveit_coreConfig.cmake:41 (include)\n'}
[1.355181] (arm_controller_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)\n'}
[1.355394] (arm_controller_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)\n'}
[1.355516] (arm_controller_pkg) StderrLine: {'line': b'  CMakeLists.txt:16 (find_package)\n'}
[1.355578] (arm_controller_pkg) StderrLine: {'line': b'This warning is for project developers.  Use -Wno-dev to suppress it.\n'}
[1.355640] (arm_controller_pkg) StderrLine: {'line': b'\x1b[0m\n'}
[1.360963] (arm_controller_pkg) StdoutLine: {'line': b'-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: filesystem\n'}
[1.423683] (-) TimerEvent: {}
[1.450964] (arm_controller_pkg) StdoutLine: {'line': b'-- library: /usr/lib/aarch64-linux-gnu/libcurl.so\n'}
[1.523777] (-) TimerEvent: {}
[1.549019] (arm_controller_pkg) StderrLine: {'line': b'\x1b[33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_ros_planning/share/moveit_ros_planning/cmake/ConfigExtras.cmake:3 (find_package):\n'}
[1.549160] (arm_controller_pkg) StderrLine: {'line': b'  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake\n'}
[1.553549] (arm_controller_pkg) StderrLine: {'line': b'  --help-policy CMP0167" for policy details.  Use the cmake_policy command to\n'}
[1.553610] (arm_controller_pkg) StderrLine: {'line': b'  set the policy and suppress this warning.\n'}
[1.553639] (arm_controller_pkg) StderrLine: {'line': b'\n'}
[1.553666] (arm_controller_pkg) StderrLine: {'line': b'Call Stack (most recent call first):\n'}
[1.553703] (arm_controller_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_planning/share/moveit_ros_planning/cmake/moveit_ros_planningConfig.cmake:41 (include)\n'}
[1.553767] (arm_controller_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)\n'}
[1.553799] (arm_controller_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)\n'}
[1.553827] (arm_controller_pkg) StderrLine: {'line': b'  CMakeLists.txt:16 (find_package)\n'}
[1.553882] (arm_controller_pkg) StderrLine: {'line': b'This warning is for project developers.  Use -Wno-dev to suppress it.\n'}
[1.553927] (arm_controller_pkg) StderrLine: {'line': b'\x1b[0m\n'}
[1.563904] (arm_controller_pkg) StdoutLine: {'line': b'-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem date_time program_options thread chrono\n'}
[1.592265] (arm_controller_pkg) StderrLine: {'line': b'\x1b[33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_ros_warehouse/share/moveit_ros_warehouse/cmake/ConfigExtras.cmake:3 (find_package):\n'}
[1.592407] (arm_controller_pkg) StderrLine: {'line': b'  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake\n'}
[1.592442] (arm_controller_pkg) StderrLine: {'line': b'  --help-policy CMP0167" for policy details.  Use the cmake_policy command to\n'}
[1.592471] (arm_controller_pkg) StderrLine: {'line': b'  set the policy and suppress this warning.\n'}
[1.592498] (arm_controller_pkg) StderrLine: {'line': b'\n'}
[1.592524] (arm_controller_pkg) StderrLine: {'line': b'Call Stack (most recent call first):\n'}
[1.592549] (arm_controller_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_warehouse/share/moveit_ros_warehouse/cmake/moveit_ros_warehouseConfig.cmake:41 (include)\n'}
[1.593582] (arm_controller_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)\n'}
[1.593643] (arm_controller_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)\n'}
[1.593683] (arm_controller_pkg) StderrLine: {'line': b'  CMakeLists.txt:16 (find_package)\n'}
[1.593711] (arm_controller_pkg) StderrLine: {'line': b'This warning is for project developers.  Use -Wno-dev to suppress it.\n'}
[1.593736] (arm_controller_pkg) StderrLine: {'line': b'\x1b[0m\n'}
[1.600119] (arm_controller_pkg) StdoutLine: {'line': b'-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: thread system filesystem regex date_time program_options\n'}
[1.616921] (arm_controller_pkg) StderrLine: {'line': b'\x1b[33mCMake Warning (dev) at /home/<USER>/moveit2_ws/install/moveit_ros_move_group/share/moveit_ros_move_group/cmake/ConfigExtras.cmake:3 (find_package):\n'}
[1.617091] (arm_controller_pkg) StderrLine: {'line': b'  Policy CMP0167 is not set: The FindBoost module is removed.  Run "cmake\n'}
[1.617137] (arm_controller_pkg) StderrLine: {'line': b'  --help-policy CMP0167" for policy details.  Use the cmake_policy command to\n'}
[1.617172] (arm_controller_pkg) StderrLine: {'line': b'  set the policy and suppress this warning.\n'}
[1.617198] (arm_controller_pkg) StderrLine: {'line': b'\n'}
[1.617225] (arm_controller_pkg) StderrLine: {'line': b'Call Stack (most recent call first):\n'}
[1.617250] (arm_controller_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_move_group/share/moveit_ros_move_group/cmake/moveit_ros_move_groupConfig.cmake:41 (include)\n'}
[1.617277] (arm_controller_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/ament_cmake_export_dependencies-extras.cmake:21 (find_package)\n'}
[1.617303] (arm_controller_pkg) StderrLine: {'line': b'  /home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/share/moveit_ros_planning_interface/cmake/moveit_ros_planning_interfaceConfig.cmake:41 (include)\n'}
[1.617328] (arm_controller_pkg) StderrLine: {'line': b'  CMakeLists.txt:16 (find_package)\n'}
[1.617354] (arm_controller_pkg) StderrLine: {'line': b'This warning is for project developers.  Use -Wno-dev to suppress it.\n'}
[1.619566] (arm_controller_pkg) StderrLine: {'line': b'\x1b[0m\n'}
[1.623878] (-) TimerEvent: {}
[1.624327] (arm_controller_pkg) StdoutLine: {'line': b'-- Found Boost: /usr/lib/aarch64-linux-gnu/cmake/Boost-1.74.0/BoostConfig.cmake (found version "1.74.0") found components: system filesystem date_time program_options thread\n'}
[1.682196] (arm_controller_pkg) StdoutLine: {'line': b'-- Found ament_lint_auto: 0.12.14 (/opt/ros/humble/share/ament_lint_auto/cmake)\n'}
[1.724499] (-) TimerEvent: {}
[1.738307] (arm_controller_pkg) StdoutLine: {'line': b"-- Added test 'cppcheck' to perform static code analysis on C / C++ code\n"}
[1.738585] (arm_controller_pkg) StdoutLine: {'line': b'-- Configured cppcheck include dirs: /home/<USER>/Code/ws_0/src/arm_controller_pkg/include\n'}
[1.738696] (arm_controller_pkg) StdoutLine: {'line': b'-- Configured cppcheck exclude dirs and/or files: \n'}
[1.739593] (arm_controller_pkg) StdoutLine: {'line': b"-- Added test 'lint_cmake' to check CMake code style\n"}
[1.739768] (arm_controller_pkg) StdoutLine: {'line': b"-- Added test 'uncrustify' to check C / C++ code style\n"}
[1.739804] (arm_controller_pkg) StdoutLine: {'line': b'-- Configured uncrustify additional arguments: \n'}
[1.740789] (arm_controller_pkg) StdoutLine: {'line': b"-- Added test 'xmllint' to check XML markup files\n"}
[1.742160] (arm_controller_pkg) StdoutLine: {'line': b'-- Configuring done (1.6s)\n'}
[1.817228] (arm_controller_pkg) StdoutLine: {'line': b'-- Generating done (0.1s)\n'}
[1.825025] (-) TimerEvent: {}
[1.829647] (arm_controller_pkg) StdoutLine: {'line': b'-- Build files have been written to: /home/<USER>/Code/ws_0/build/arm_controller_pkg\n'}
[1.858832] (arm_controller_pkg) StdoutLine: {'line': b'[ 25%] \x1b[32mBuilding CXX object CMakeFiles/RB_builder_node.dir/src/RB_builder_node.cpp.o\x1b[0m\n'}
[1.896161] (arm_controller_pkg) StdoutLine: {'line': b'[ 75%] Built target path_planning_node\n'}
[1.926268] (-) TimerEvent: {}
[2.028179] (-) TimerEvent: {}
[2.128458] (-) TimerEvent: {}
[2.230745] (-) TimerEvent: {}
[2.332145] (-) TimerEvent: {}
[2.432410] (-) TimerEvent: {}
[2.533597] (-) TimerEvent: {}
[2.635426] (-) TimerEvent: {}
[2.736043] (-) TimerEvent: {}
[2.836545] (-) TimerEvent: {}
[2.938861] (-) TimerEvent: {}
[3.039403] (-) TimerEvent: {}
[3.142527] (-) TimerEvent: {}
[3.243552] (-) TimerEvent: {}
[3.344512] (-) TimerEvent: {}
[3.445594] (-) TimerEvent: {}
[3.547470] (-) TimerEvent: {}
[3.649581] (-) TimerEvent: {}
[3.750917] (-) TimerEvent: {}
[3.852654] (-) TimerEvent: {}
[3.957024] (-) TimerEvent: {}
[4.057348] (-) TimerEvent: {}
[4.159165] (-) TimerEvent: {}
[4.259673] (-) TimerEvent: {}
[4.362513] (-) TimerEvent: {}
[4.464355] (-) TimerEvent: {}
[4.565256] (-) TimerEvent: {}
[4.666191] (-) TimerEvent: {}
[4.790211] (-) TimerEvent: {}
[4.896206] (-) TimerEvent: {}
[4.998824] (-) TimerEvent: {}
[5.100220] (-) TimerEvent: {}
[5.200535] (-) TimerEvent: {}
[5.302100] (-) TimerEvent: {}
[5.403775] (-) TimerEvent: {}
[5.505292] (-) TimerEvent: {}
[5.607105] (-) TimerEvent: {}
[5.712137] (-) TimerEvent: {}
[5.812805] (-) TimerEvent: {}
[5.914947] (-) TimerEvent: {}
[6.017139] (-) TimerEvent: {}
[6.118628] (-) TimerEvent: {}
[6.220243] (-) TimerEvent: {}
[6.321329] (-) TimerEvent: {}
[6.422394] (-) TimerEvent: {}
[6.523534] (-) TimerEvent: {}
[6.625023] (-) TimerEvent: {}
[6.732098] (-) TimerEvent: {}
[6.839133] (-) TimerEvent: {}
[6.939931] (-) TimerEvent: {}
[7.042147] (-) TimerEvent: {}
[7.142457] (-) TimerEvent: {}
[7.242849] (-) TimerEvent: {}
[7.343539] (-) TimerEvent: {}
[7.446358] (-) TimerEvent: {}
[7.550227] (-) TimerEvent: {}
[7.650687] (-) TimerEvent: {}
[7.751522] (-) TimerEvent: {}
[7.853368] (-) TimerEvent: {}
[7.953969] (-) TimerEvent: {}
[8.056250] (-) TimerEvent: {}
[8.157747] (-) TimerEvent: {}
[8.260611] (-) TimerEvent: {}
[8.361046] (-) TimerEvent: {}
[8.461691] (-) TimerEvent: {}
[8.563122] (-) TimerEvent: {}
[8.663381] (-) TimerEvent: {}
[8.764483] (-) TimerEvent: {}
[8.882593] (-) TimerEvent: {}
[8.988500] (-) TimerEvent: {}
[9.089838] (-) TimerEvent: {}
[9.191432] (-) TimerEvent: {}
[9.292614] (-) TimerEvent: {}
[9.396567] (-) TimerEvent: {}
[9.503842] (-) TimerEvent: {}
[9.605272] (-) TimerEvent: {}
[9.705648] (-) TimerEvent: {}
[9.806477] (-) TimerEvent: {}
[9.908343] (-) TimerEvent: {}
[10.008865] (-) TimerEvent: {}
[10.112207] (-) TimerEvent: {}
[10.213465] (-) TimerEvent: {}
[10.314521] (-) TimerEvent: {}
[10.416439] (-) TimerEvent: {}
[10.518347] (-) TimerEvent: {}
[10.619129] (-) TimerEvent: {}
[10.719600] (-) TimerEvent: {}
[10.820329] (-) TimerEvent: {}
[10.920804] (-) TimerEvent: {}
[11.022303] (-) TimerEvent: {}
[11.125149] (-) TimerEvent: {}
[11.225496] (-) TimerEvent: {}
[11.333104] (-) TimerEvent: {}
[11.436157] (-) TimerEvent: {}
[11.537882] (-) TimerEvent: {}
[11.638427] (-) TimerEvent: {}
[11.749505] (-) TimerEvent: {}
[11.849867] (-) TimerEvent: {}
[11.950742] (-) TimerEvent: {}
[12.052135] (-) TimerEvent: {}
[12.152541] (-) TimerEvent: {}
[12.257055] (-) TimerEvent: {}
[12.358294] (-) TimerEvent: {}
[12.461376] (-) TimerEvent: {}
[12.562386] (-) TimerEvent: {}
[12.662916] (-) TimerEvent: {}
[12.763699] (-) TimerEvent: {}
[12.867518] (-) TimerEvent: {}
[12.972144] (-) TimerEvent: {}
[13.078976] (-) TimerEvent: {}
[13.186974] (-) TimerEvent: {}
[13.290125] (-) TimerEvent: {}
[13.390633] (-) TimerEvent: {}
[13.491231] (-) TimerEvent: {}
[13.592887] (-) TimerEvent: {}
[13.775057] (-) TimerEvent: {}
[13.880579] (-) TimerEvent: {}
[13.983391] (-) TimerEvent: {}
[14.084020] (-) TimerEvent: {}
[14.188196] (-) TimerEvent: {}
[14.294270] (-) TimerEvent: {}
[14.396466] (-) TimerEvent: {}
[14.497038] (-) TimerEvent: {}
[14.597525] (-) TimerEvent: {}
[14.699481] (-) TimerEvent: {}
[14.804154] (-) TimerEvent: {}
[14.905950] (-) TimerEvent: {}
[15.007348] (-) TimerEvent: {}
[15.092936] (arm_controller_pkg) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX executable RB_builder_node\x1b[0m\n'}
[15.107785] (-) TimerEvent: {}
[15.210300] (-) TimerEvent: {}
[15.312969] (-) TimerEvent: {}
[15.413990] (-) TimerEvent: {}
[15.514487] (-) TimerEvent: {}
[15.621125] (-) TimerEvent: {}
[15.721612] (-) TimerEvent: {}
[15.821916] (-) TimerEvent: {}
[15.923118] (-) TimerEvent: {}
[16.024373] (-) TimerEvent: {}
[16.125217] (-) TimerEvent: {}
[16.213207] (arm_controller_pkg) StderrLine: {'line': b"/usr/bin/ld: CMakeFiles/RB_builder_node.dir/src/RB_builder_node.cpp.o: in function `tf2::impl::toQuaternion(geometry_msgs::msg::Quaternion_<std::allocator<void> > const&)':\n"}
[16.213485] (arm_controller_pkg) StderrLine: {'line': b"RB_builder_node.cpp:(.text._ZN3tf24impl12toQuaternionERKN13geometry_msgs3msg11Quaternion_ISaIvEEE[_ZN3tf24impl12toQuaternionERKN13geometry_msgs3msg11Quaternion_ISaIvEEE]+0x34): undefined reference to `tf2::fromMsg(geometry_msgs::msg::Quaternion_<std::allocator<void> > const&, tf2::Quaternion&)'\n"}
[16.226112] (-) TimerEvent: {}
[16.263263] (arm_controller_pkg) StderrLine: {'line': b'collect2: error: ld returned 1 exit status\n'}
[16.264811] (arm_controller_pkg) StderrLine: {'line': b'gmake[2]: *** [CMakeFiles/RB_builder_node.dir/build.make:359: RB_builder_node] Error 1\n'}
[16.275092] (arm_controller_pkg) StderrLine: {'line': b'gmake[1]: *** [CMakeFiles/Makefile2:188: CMakeFiles/RB_builder_node.dir/all] Error 2\n'}
[16.277250] (arm_controller_pkg) StderrLine: {'line': b'gmake: *** [Makefile:146: all] Error 2\n'}
[16.286183] (arm_controller_pkg) CommandEnded: {'returncode': 2}
[16.315608] (arm_controller_pkg) JobEnded: {'identifier': 'arm_controller_pkg', 'rc': 2}
[16.328086] (-) EventReactorShutdown: {}
