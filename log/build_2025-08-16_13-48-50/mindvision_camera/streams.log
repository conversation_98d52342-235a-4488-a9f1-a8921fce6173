[0.081s] Invoking command in '/home/<USER>/Code/ws_0/build/mindvision_camera': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/mindvision_camera -- -j8 -l8
[0.185s] [ 50%] Built target mindvision_camera_node
[0.185s] [100%] Built target mindvision_camera
[0.224s] Invoked command in '/home/<USER>/Code/ws_0/build/mindvision_camera' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/mindvision_camera -- -j8 -l8
[0.227s] Invoking command in '/home/<USER>/Code/ws_0/build/mindvision_camera': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/mindvision_camera
[0.236s] -- Install configuration: ""
[0.236s] -- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/lib/mindvision_camera/mindvision_camera_node
[0.237s] -- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/environment/library_path.sh
[0.237s] -- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/environment/library_path.dsv
[0.242s] -- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/lib/libmindvision_camera.so
[0.243s] -- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/config
[0.243s] -- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/config/camera_info.yaml
[0.243s] -- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/config/camera_params.yaml
[0.243s] -- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/launch
[0.243s] -- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/launch/mv_launch.py
[0.243s] -- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/ament_index/resource_index/package_run_dependencies/mindvision_camera
[0.243s] -- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/ament_index/resource_index/parent_prefix_path/mindvision_camera
[0.243s] -- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/environment/ament_prefix_path.sh
[0.243s] -- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/environment/ament_prefix_path.dsv
[0.243s] -- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/environment/path.sh
[0.243s] -- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/environment/path.dsv
[0.243s] -- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/local_setup.bash
[0.246s] -- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/local_setup.sh
[0.246s] -- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/local_setup.zsh
[0.246s] -- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/local_setup.dsv
[0.247s] -- Installing: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.dsv
[0.247s] -- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/ament_index/resource_index/packages/mindvision_camera
[0.247s] -- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/ament_index/resource_index/rclcpp_components/mindvision_camera
[0.247s] -- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/cmake/ament_cmake_export_dependencies-extras.cmake
[0.247s] -- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/cmake/ament_cmake_export_libraries-extras.cmake
[0.247s] -- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/cmake/mindvision_cameraConfig.cmake
[0.247s] -- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/cmake/mindvision_cameraConfig-version.cmake
[0.248s] -- Up-to-date: /home/<USER>/Code/ws_0/install/mindvision_camera/share/mindvision_camera/package.xml
[0.248s] Invoked command in '/home/<USER>/Code/ws_0/build/mindvision_camera' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/mindvision_camera
