[0.018s] Invoking command in '/home/<USER>/Code/ws_0/build/arm_controller_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/arm_controller_pkg -- -j8 -l8
[0.083s] [ 50%] Built target path_planning_node
[0.101s] [ 75%] [32mBuilding CXX object CMakeFiles/RB_builder_node.dir/src/RB_builder_node.cpp.o[0m
[12.100s] [100%] [32m[1mLinking CXX executable RB_builder_node[0m
[12.955s] [100%] Built target RB_builder_node
[12.968s] Invoked command in '/home/<USER>/Code/ws_0/build/arm_controller_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/arm_controller_pkg -- -j8 -l8
[12.974s] Invoking command in '/home/<USER>/Code/ws_0/build/arm_controller_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/arm_controller_pkg
[12.977s] -- Install configuration: ""
[12.981s] -- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/lib/arm_controller_pkg/path_planning_node
[12.987s] -- Installing: /home/<USER>/Code/ws_0/install/arm_controller_pkg/lib/arm_controller_pkg/RB_builder_node
[12.996s] -- Set non-toolchain portion of runtime path of "/home/<USER>/Code/ws_0/install/arm_controller_pkg/lib/arm_controller_pkg/RB_builder_node" to ""
[12.996s] -- Installing: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/ament_index/resource_index/package_run_dependencies/arm_controller_pkg
[12.997s] -- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/ament_index/resource_index/parent_prefix_path/arm_controller_pkg
[12.997s] -- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/environment/ament_prefix_path.sh
[12.997s] -- Installing: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/environment/ament_prefix_path.dsv
[12.997s] -- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/environment/path.sh
[12.997s] -- Installing: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/environment/path.dsv
[12.997s] -- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/local_setup.bash
[12.998s] -- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/local_setup.sh
[13.000s] -- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/local_setup.zsh
[13.000s] -- Installing: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/local_setup.dsv
[13.000s] -- Installing: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.dsv
[13.001s] -- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/ament_index/resource_index/packages/arm_controller_pkg
[13.002s] -- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/cmake/arm_controller_pkgConfig.cmake
[13.002s] -- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/cmake/arm_controller_pkgConfig-version.cmake
[13.002s] -- Installing: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.xml
[13.010s] Invoked command in '/home/<USER>/Code/ws_0/build/arm_controller_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/arm_controller_pkg
