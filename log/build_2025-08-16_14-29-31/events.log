[0.000000] (-) TimerEvent: {}
[0.000995] (-) JobUnselected: {'identifier': 'arm_controller_pkg'}
[0.001076] (-) JobUnselected: {'identifier': 'mindvision_camera'}
[0.001105] (-) JobUnselected: {'identifier': 'rm_vision'}
[0.001116] (-) JobUnselected: {'identifier': 'robot_description'}
[0.001130] (engineer_vision_pkg) JobQueued: {'identifier': 'engineer_vision_pkg', 'dependencies': OrderedDict()}
[0.001155] (engineer_vision_pkg) JobStarted: {'identifier': 'engineer_vision_pkg'}
[0.014963] (engineer_vision_pkg) JobProgress: {'identifier': 'engineer_vision_pkg', 'progress': 'cmake'}
[0.015482] (engineer_vision_pkg) JobProgress: {'identifier': 'engineer_vision_pkg', 'progress': 'build'}
[0.015940] (engineer_vision_pkg) Command: {'cmd': ['/usr/local/cmake/bin/cmake', '--build', '/home/<USER>/Code/ws_0/build/engineer_vision_pkg', '--', '-j8', '-l8'], 'cwd': '/home/<USER>/Code/ws_0/build/engineer_vision_pkg', 'env': OrderedDict([('USER', 'mac'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner_testutils/lib:/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner/lib:/home/<USER>/moveit2_ws/install/moveit_visual_tools/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_visualization/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_demo/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_capabilities/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_core/lib:/home/<USER>/moveit2_ws/install/moveit_ros_control_interface/lib:/home/<USER>/moveit2_ws/install/moveit_simple_controller_manager/lib:/home/<USER>/moveit2_ws/install/moveit_setup_assistant/lib:/home/<USER>/moveit2_ws/install/moveit_setup_srdf_plugins/lib:/home/<USER>/moveit2_ws/install/moveit_setup_core_plugins/lib:/home/<USER>/moveit2_ws/install/moveit_setup_controllers/lib:/home/<USER>/moveit2_ws/install/moveit_setup_app_plugins/lib:/home/<USER>/moveit2_ws/install/moveit_setup_framework/lib:/home/<USER>/moveit2_ws/install/moveit_servo/lib:/home/<USER>/moveit2_ws/install/moveit_ros_visualization/lib:/home/<USER>/moveit2_ws/install/moveit_hybrid_planning/lib:/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/lib:/home/<USER>/moveit2_ws/install/moveit_ros_benchmarks/lib:/home/<USER>/moveit2_ws/install/moveit_ros_warehouse/lib:/home/<USER>/moveit2_ws/install/moveit_ros_robot_interaction/lib:/home/<USER>/moveit2_ws/install/moveit_ros_perception/lib:/home/<USER>/moveit2_ws/install/moveit_ros_move_group/lib:/home/<USER>/moveit2_ws/install/moveit_planners_ompl/lib:/home/<USER>/moveit2_ws/install/moveit_ros_planning/lib:/home/<USER>/moveit2_ws/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/moveit2_ws/install/moveit_planners_chomp/lib:/home/<USER>/moveit2_ws/install/moveit_kinematics/lib:/home/<USER>/moveit2_ws/install/moveit_chomp_optimizer_adapter/lib:/home/<USER>/moveit2_ws/install/chomp_motion_planner/lib:/home/<USER>/moveit2_ws/install/moveit_core/lib:/home/<USER>/moveit2_ws/install/srdfdom/lib:/home/<USER>/moveit2_ws/install/rviz_marker_tools/lib:/home/<USER>/moveit2_ws/install/rosparam_shortcuts/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_msgs/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/bin/helpers/browser.sh'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/.vscode-server'), ('TERM_PROGRAM_VERSION', '1.103.1'), ('VSCODE_IPC_HOOK_CLI', '/run/user/502/vscode-ipc-0880d7a0-0ba2-4e86-ad44-b7a50499f90c.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/502/bus'), ('COLORTERM', 'truecolor'), ('COLCON_PREFIX_PATH', '/home/<USER>/moveit2_ws/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mac'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/home/<USER>/moveit2_ws/install/moveit_core/bin:/opt/ros/humble/bin:/usr/local/cmake/bin:/opt/orbstack-guest/bin-hiprio:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/opt/orbstack-guest/bin:/opt/orbstack-guest/data/bin/cmdlinks'), ('PYTHON_EXECUTABLE', '/usr/bin/python3.10'), ('XDG_RUNTIME_DIR', '/run/user/502'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-ec2f90e73ca2d53c.txt'), ('LANG', 'C.UTF-8'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/502/vscode-git-4bcffef955.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AUTH_SOCK', '/run/user/502/vscode-ssh-auth-sock-93286558'), ('AMENT_PREFIX_PATH', '/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner_testutils:/home/<USER>/moveit2_ws/install/moveit_runtime:/home/<USER>/moveit2_ws/install/moveit2_tutorials:/home/<USER>/moveit2_ws/install/moveit:/home/<USER>/moveit2_ws/install/moveit_planners:/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner:/home/<USER>/moveit2_ws/install/moveit_test:/home/<USER>/moveit2_ws/install/moveit_visual_tools:/home/<USER>/moveit2_ws/install/moveit_task_constructor_visualization:/home/<USER>/moveit2_ws/install/moveit_task_constructor_demo:/home/<USER>/moveit2_ws/install/moveit_task_constructor_capabilities:/home/<USER>/moveit2_ws/install/moveit_task_constructor_core:/home/<USER>/moveit2_ws/install/moveit_ros_control_interface:/home/<USER>/moveit2_ws/install/moveit_plugins:/home/<USER>/moveit2_ws/install/moveit_simple_controller_manager:/home/<USER>/moveit2_ws/install/moveit_setup_assistant:/home/<USER>/moveit2_ws/install/moveit_setup_srdf_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_core_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_controllers:/home/<USER>/moveit2_ws/install/moveit_setup_app_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_framework:/home/<USER>/moveit2_ws/install/moveit_servo:/home/<USER>/moveit2_ws/install/moveit_ros:/home/<USER>/moveit2_ws/install/moveit_ros_visualization:/home/<USER>/moveit2_ws/install/moveit_hybrid_planning:/home/<USER>/moveit2_ws/install/move_program:/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface:/home/<USER>/moveit2_ws/install/moveit_ros_benchmarks:/home/<USER>/moveit2_ws/install/moveit_ros_warehouse:/home/<USER>/moveit2_ws/install/moveit_ros_robot_interaction:/home/<USER>/moveit2_ws/install/moveit_ros_perception:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_pg70_support:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_moveit_config:/home/<USER>/moveit2_ws/install/moveit_ros_move_group:/home/<USER>/moveit2_ws/install/moveit_planners_ompl:/home/<USER>/moveit2_ws/install/moveit_ros_planning:/home/<USER>/moveit2_ws/install/moveit_ros_occupancy_map_monitor:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/moveit2_ws/install/moveit_planners_chomp:/home/<USER>/moveit2_ws/install/moveit_kinematics:/home/<USER>/moveit2_ws/install/moveit_chomp_optimizer_adapter:/home/<USER>/moveit2_ws/install/chomp_motion_planner:/home/<USER>/moveit2_ws/install/moveit_core:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/srdfdom:/home/<USER>/moveit2_ws/install/rviz_marker_tools:/home/<USER>/moveit2_ws/install/rosparam_shortcuts:/home/<USER>/moveit2_ws/install/moveit_task_constructor_msgs:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_support:/home/<USER>/moveit2_ws/install/moveit_resources:/home/<USER>/moveit2_ws/install/moveit_resources_pr2_description:/home/<USER>/moveit2_ws/install/moveit_resources_panda_moveit_config:/home/<USER>/moveit2_ws/install/moveit_resources_panda_description:/home/<USER>/moveit2_ws/install/moveit_resources_fanuc_moveit_config:/home/<USER>/moveit2_ws/install/moveit_resources_fanuc_description:/home/<USER>/moveit2_ws/install/moveit_common:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('CMAKE_ARGS', '-DPYTHON_EXECUTABLE=/usr/bin/python3.10 -DPython3_EXECUTABLE=/usr/bin/python3.10'), ('CMAKE_MODULE_PATH', '/home/<USER>/moveit2_ws/install/moveit_task_constructor_core/share/moveit_task_constructor_core/cmake'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('PWD', '/home/<USER>/Code/ws_0/build/engineer_vision_pkg'), ('SSH_CONNECTION', '::1 0 ::1 22'), ('XDG_DATA_DIRS', '/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/moveit2_ws/install/moveit_task_constructor_core/local/lib/python3.10/dist-packages:/home/<USER>/moveit2_ws/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/moveit2_ws/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/moveit2_ws/install/moveit_task_constructor_msgs/local/lib/python3.10/dist-packages:/home/<USER>/moveit2_ws/install/launch_param_builder/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('Python3_EXECUTABLE', '/usr/bin/python3.10'), ('CMAKE_PREFIX_PATH', '/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner_testutils:/home/<USER>/moveit2_ws/install/moveit_runtime:/home/<USER>/moveit2_ws/install/moveit2_tutorials:/home/<USER>/moveit2_ws/install/moveit:/home/<USER>/moveit2_ws/install/moveit_planners:/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner:/home/<USER>/moveit2_ws/install/moveit_test:/home/<USER>/moveit2_ws/install/moveit_visual_tools:/home/<USER>/moveit2_ws/install/moveit_task_constructor_visualization:/home/<USER>/moveit2_ws/install/moveit_task_constructor_demo:/home/<USER>/moveit2_ws/install/moveit_task_constructor_capabilities:/home/<USER>/moveit2_ws/install/moveit_task_constructor_core:/home/<USER>/moveit2_ws/install/moveit_ros_control_interface:/home/<USER>/moveit2_ws/install/moveit_plugins:/home/<USER>/moveit2_ws/install/moveit_simple_controller_manager:/home/<USER>/moveit2_ws/install/moveit_setup_assistant:/home/<USER>/moveit2_ws/install/moveit_setup_srdf_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_core_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_controllers:/home/<USER>/moveit2_ws/install/moveit_setup_app_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_framework:/home/<USER>/moveit2_ws/install/moveit_servo:/home/<USER>/moveit2_ws/install/moveit_ros:/home/<USER>/moveit2_ws/install/moveit_ros_visualization:/home/<USER>/moveit2_ws/install/moveit_hybrid_planning:/home/<USER>/moveit2_ws/install/move_program:/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface:/home/<USER>/moveit2_ws/install/moveit_ros_benchmarks:/home/<USER>/moveit2_ws/install/moveit_ros_warehouse:/home/<USER>/moveit2_ws/install/moveit_ros_robot_interaction:/home/<USER>/moveit2_ws/install/moveit_ros_perception:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_pg70_support:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_moveit_config:/home/<USER>/moveit2_ws/install/moveit_ros_move_group:/home/<USER>/moveit2_ws/install/moveit_planners_ompl:/home/<USER>/moveit2_ws/install/moveit_ros_planning:/home/<USER>/moveit2_ws/install/moveit_ros_occupancy_map_monitor:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/moveit2_ws/install/moveit_planners_chomp:/home/<USER>/moveit2_ws/install/moveit_kinematics:/home/<USER>/moveit2_ws/install/moveit_chomp_optimizer_adapter:/home/<USER>/moveit2_ws/install/chomp_motion_planner:/home/<USER>/moveit2_ws/install/moveit_core:/home/<USER>/moveit2_ws/install/srdfdom:/home/<USER>/moveit2_ws/install/rviz_marker_tools:/home/<USER>/moveit2_ws/install/rosparam_shortcuts:/home/<USER>/moveit2_ws/install/moveit_task_constructor_msgs:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_support:/home/<USER>/moveit2_ws/install/moveit_resources:/home/<USER>/moveit2_ws/install/moveit_resources_pr2_description:/home/<USER>/moveit2_ws/install/moveit_resources_panda_moveit_config:/home/<USER>/moveit2_ws/install/moveit_resources_panda_description:/home/<USER>/moveit2_ws/install/moveit_resources_fanuc_moveit_config:/home/<USER>/moveit2_ws/install/moveit_resources_fanuc_description:/home/<USER>/moveit2_ws/install/moveit_common:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble')]), 'shell': False}
[0.099943] (-) TimerEvent: {}
[0.184284] (engineer_vision_pkg) StdoutLine: {'line': b'[ 25%] Built target video_capturer_node\n'}
[0.200054] (-) TimerEvent: {}
[0.201792] (engineer_vision_pkg) StdoutLine: {'line': b'[ 50%] Built target video_saver_node\n'}
[0.202064] (engineer_vision_pkg) StdoutLine: {'line': b'[ 62%] \x1b[32mBuilding CXX object CMakeFiles/RB_detector_node.dir/src/RB_detector_node.cpp.o\x1b[0m\n'}
[0.206982] (engineer_vision_pkg) StdoutLine: {'line': b'[ 87%] Built target serial_node\n'}
[0.306101] (-) TimerEvent: {}
[0.410857] (-) TimerEvent: {}
[0.511347] (-) TimerEvent: {}
[0.611824] (-) TimerEvent: {}
[0.713429] (-) TimerEvent: {}
[0.814659] (-) TimerEvent: {}
[0.915273] (-) TimerEvent: {}
[1.019127] (-) TimerEvent: {}
[1.120957] (-) TimerEvent: {}
[1.224097] (-) TimerEvent: {}
[1.324397] (-) TimerEvent: {}
[1.426058] (-) TimerEvent: {}
[1.531488] (-) TimerEvent: {}
[1.633787] (-) TimerEvent: {}
[1.734410] (-) TimerEvent: {}
[1.834980] (-) TimerEvent: {}
[1.943061] (-) TimerEvent: {}
[2.043562] (-) TimerEvent: {}
[2.143911] (-) TimerEvent: {}
[2.245023] (-) TimerEvent: {}
[2.345591] (-) TimerEvent: {}
[2.448219] (-) TimerEvent: {}
[2.548823] (-) TimerEvent: {}
[2.650194] (-) TimerEvent: {}
[2.750820] (-) TimerEvent: {}
[2.858010] (-) TimerEvent: {}
[2.958863] (-) TimerEvent: {}
[3.061667] (-) TimerEvent: {}
[3.163698] (-) TimerEvent: {}
[3.266100] (-) TimerEvent: {}
[3.366953] (-) TimerEvent: {}
[3.469122] (-) TimerEvent: {}
[3.569437] (-) TimerEvent: {}
[3.674982] (-) TimerEvent: {}
[3.775936] (-) TimerEvent: {}
[3.880012] (-) TimerEvent: {}
[3.980901] (-) TimerEvent: {}
[4.084639] (-) TimerEvent: {}
[4.186563] (-) TimerEvent: {}
[4.287315] (-) TimerEvent: {}
[4.390937] (-) TimerEvent: {}
[4.491841] (-) TimerEvent: {}
[4.592833] (-) TimerEvent: {}
[4.695350] (-) TimerEvent: {}
[4.796622] (-) TimerEvent: {}
[4.898750] (-) TimerEvent: {}
[4.999969] (-) TimerEvent: {}
[5.100898] (-) TimerEvent: {}
[5.201559] (-) TimerEvent: {}
[5.303318] (-) TimerEvent: {}
[5.403644] (-) TimerEvent: {}
[5.506106] (-) TimerEvent: {}
[5.607069] (-) TimerEvent: {}
[5.708964] (-) TimerEvent: {}
[5.815369] (-) TimerEvent: {}
[5.915729] (-) TimerEvent: {}
[6.016390] (-) TimerEvent: {}
[6.117594] (-) TimerEvent: {}
[6.218595] (-) TimerEvent: {}
[6.319547] (-) TimerEvent: {}
[6.420384] (-) TimerEvent: {}
[6.524389] (-) TimerEvent: {}
[6.626415] (-) TimerEvent: {}
[6.727202] (-) TimerEvent: {}
[6.830560] (-) TimerEvent: {}
[6.934123] (-) TimerEvent: {}
[7.035295] (-) TimerEvent: {}
[7.139894] (-) TimerEvent: {}
[7.240159] (-) TimerEvent: {}
[7.340462] (-) TimerEvent: {}
[7.441346] (-) TimerEvent: {}
[7.543487] (-) TimerEvent: {}
[7.681825] (-) TimerEvent: {}
[7.784924] (-) TimerEvent: {}
[7.885261] (-) TimerEvent: {}
[7.986002] (-) TimerEvent: {}
[8.086832] (-) TimerEvent: {}
[8.193979] (-) TimerEvent: {}
[8.299450] (-) TimerEvent: {}
[8.400113] (-) TimerEvent: {}
[8.500842] (-) TimerEvent: {}
[8.604230] (-) TimerEvent: {}
[8.705716] (-) TimerEvent: {}
[8.806308] (-) TimerEvent: {}
[8.908484] (-) TimerEvent: {}
[9.009260] (-) TimerEvent: {}
[9.114331] (-) TimerEvent: {}
[9.214840] (-) TimerEvent: {}
[9.318101] (-) TimerEvent: {}
[9.418601] (-) TimerEvent: {}
[9.520980] (-) TimerEvent: {}
[9.624994] (-) TimerEvent: {}
[9.725513] (-) TimerEvent: {}
[9.829987] (-) TimerEvent: {}
[9.930686] (-) TimerEvent: {}
[10.032101] (-) TimerEvent: {}
[10.133722] (-) TimerEvent: {}
[10.234879] (-) TimerEvent: {}
[10.336526] (-) TimerEvent: {}
[10.437659] (-) TimerEvent: {}
[10.440624] (engineer_vision_pkg) StdoutLine: {'line': b'[100%] \x1b[32m\x1b[1mLinking CXX executable RB_detector_node\x1b[0m\n'}
[10.539392] (-) TimerEvent: {}
[10.639990] (-) TimerEvent: {}
[10.725967] (engineer_vision_pkg) StderrLine: {'line': b'/usr/bin/ld: warning: libopencv_imgcodecs.so.4.5d, needed by /opt/ros/humble/lib/libcv_bridge.so, may conflict with libopencv_imgcodecs.so.411\n'}
[10.730563] (engineer_vision_pkg) StderrLine: {'line': b'/usr/bin/ld: warning: libopencv_imgproc.so.4.5d, needed by /opt/ros/humble/lib/libcv_bridge.so, may conflict with libopencv_imgproc.so.411\n'}
[10.736061] (engineer_vision_pkg) StderrLine: {'line': b'/usr/bin/ld: warning: libopencv_core.so.4.5d, needed by /opt/ros/humble/lib/libcv_bridge.so, may conflict with libopencv_core.so.411\n'}
[10.740134] (-) TimerEvent: {}
[10.841046] (-) TimerEvent: {}
[10.941634] (-) TimerEvent: {}
[11.042975] (-) TimerEvent: {}
[11.148157] (-) TimerEvent: {}
[11.250597] (-) TimerEvent: {}
[11.351570] (-) TimerEvent: {}
[11.353971] (engineer_vision_pkg) StdoutLine: {'line': b'[100%] Built target RB_detector_node\n'}
[11.371052] (engineer_vision_pkg) CommandEnded: {'returncode': 0}
[11.374462] (engineer_vision_pkg) JobProgress: {'identifier': 'engineer_vision_pkg', 'progress': 'install'}
[11.379758] (engineer_vision_pkg) Command: {'cmd': ['/usr/local/cmake/bin/cmake', '--install', '/home/<USER>/Code/ws_0/build/engineer_vision_pkg'], 'cwd': '/home/<USER>/Code/ws_0/build/engineer_vision_pkg', 'env': OrderedDict([('USER', 'mac'), ('XDG_SESSION_TYPE', 'tty'), ('GIT_ASKPASS', '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/extensions/git/dist/askpass.sh'), ('SHLVL', '2'), ('LD_LIBRARY_PATH', '/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner_testutils/lib:/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner/lib:/home/<USER>/moveit2_ws/install/moveit_visual_tools/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_visualization/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_demo/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_capabilities/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_core/lib:/home/<USER>/moveit2_ws/install/moveit_ros_control_interface/lib:/home/<USER>/moveit2_ws/install/moveit_simple_controller_manager/lib:/home/<USER>/moveit2_ws/install/moveit_setup_assistant/lib:/home/<USER>/moveit2_ws/install/moveit_setup_srdf_plugins/lib:/home/<USER>/moveit2_ws/install/moveit_setup_core_plugins/lib:/home/<USER>/moveit2_ws/install/moveit_setup_controllers/lib:/home/<USER>/moveit2_ws/install/moveit_setup_app_plugins/lib:/home/<USER>/moveit2_ws/install/moveit_setup_framework/lib:/home/<USER>/moveit2_ws/install/moveit_servo/lib:/home/<USER>/moveit2_ws/install/moveit_ros_visualization/lib:/home/<USER>/moveit2_ws/install/moveit_hybrid_planning/lib:/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface/lib:/home/<USER>/moveit2_ws/install/moveit_ros_benchmarks/lib:/home/<USER>/moveit2_ws/install/moveit_ros_warehouse/lib:/home/<USER>/moveit2_ws/install/moveit_ros_robot_interaction/lib:/home/<USER>/moveit2_ws/install/moveit_ros_perception/lib:/home/<USER>/moveit2_ws/install/moveit_ros_move_group/lib:/home/<USER>/moveit2_ws/install/moveit_planners_ompl/lib:/home/<USER>/moveit2_ws/install/moveit_ros_planning/lib:/home/<USER>/moveit2_ws/install/moveit_ros_occupancy_map_monitor/lib:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_ikfast_manipulator_plugin/lib:/home/<USER>/moveit2_ws/install/moveit_planners_chomp/lib:/home/<USER>/moveit2_ws/install/moveit_kinematics/lib:/home/<USER>/moveit2_ws/install/moveit_chomp_optimizer_adapter/lib:/home/<USER>/moveit2_ws/install/chomp_motion_planner/lib:/home/<USER>/moveit2_ws/install/moveit_core/lib:/home/<USER>/moveit2_ws/install/srdfdom/lib:/home/<USER>/moveit2_ws/install/rviz_marker_tools/lib:/home/<USER>/moveit2_ws/install/rosparam_shortcuts/lib:/home/<USER>/moveit2_ws/install/moveit_task_constructor_msgs/lib:/opt/ros/humble/opt/rviz_ogre_vendor/lib:/opt/ros/humble/lib/aarch64-linux-gnu:/opt/ros/humble/lib'), ('BROWSER', '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/bin/helpers/browser.sh'), ('HOME', '/home/<USER>'), ('OLDPWD', '/home/<USER>/.vscode-server'), ('TERM_PROGRAM_VERSION', '1.103.1'), ('VSCODE_IPC_HOOK_CLI', '/run/user/502/vscode-ipc-0880d7a0-0ba2-4e86-ad44-b7a50499f90c.sock'), ('ROS_PYTHON_VERSION', '3'), ('VSCODE_GIT_ASKPASS_MAIN', '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/extensions/git/dist/askpass-main.js'), ('VSCODE_GIT_ASKPASS_NODE', '/home/<USER>/.vscode-server/cli/servers/Stable-360a4e4fd251bfce169a4ddf857c7d25d1ad40da/server/node'), ('SSL_CERT_FILE', '/usr/lib/ssl/certs/ca-certificates.crt'), ('PYDEVD_DISABLE_FILE_VALIDATION', '1'), ('BUNDLED_DEBUGPY_PATH', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/bundled/libs/debugpy'), ('DBUS_SESSION_BUS_ADDRESS', 'unix:path=/run/user/502/bus'), ('COLORTERM', 'truecolor'), ('COLCON_PREFIX_PATH', '/home/<USER>/moveit2_ws/install'), ('ROS_DISTRO', 'humble'), ('LOGNAME', 'mac'), ('_', '/usr/bin/colcon'), ('ROS_VERSION', '2'), ('PKG_CONFIG_PATH', '/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:/usr/local/lib/pkgconfig:'), ('XDG_SESSION_CLASS', 'user'), ('TERM', 'xterm-256color'), ('ROS_LOCALHOST_ONLY', '0'), ('PATH', '/home/<USER>/moveit2_ws/install/moveit_core/bin:/opt/ros/humble/bin:/usr/local/cmake/bin:/opt/orbstack-guest/bin-hiprio:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/opt/orbstack-guest/bin:/opt/orbstack-guest/data/bin/cmdlinks'), ('PYTHON_EXECUTABLE', '/usr/bin/python3.10'), ('XDG_RUNTIME_DIR', '/run/user/502'), ('SSL_CERT_DIR', '/usr/lib/ssl/certs'), ('DISPLAY', ':1'), ('VSCODE_DEBUGPY_ADAPTER_ENDPOINTS', '/home/<USER>/.vscode-server/extensions/ms-python.debugpy-2025.10.0-linux-arm64/.noConfigDebugAdapterEndpoints/endpoint-ec2f90e73ca2d53c.txt'), ('LANG', 'C.UTF-8'), ('VSCODE_GIT_IPC_HANDLE', '/run/user/502/vscode-git-4bcffef955.sock'), ('TERM_PROGRAM', 'vscode'), ('SSH_AUTH_SOCK', '/run/user/502/vscode-ssh-auth-sock-93286558'), ('AMENT_PREFIX_PATH', '/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner_testutils:/home/<USER>/moveit2_ws/install/moveit_runtime:/home/<USER>/moveit2_ws/install/moveit2_tutorials:/home/<USER>/moveit2_ws/install/moveit:/home/<USER>/moveit2_ws/install/moveit_planners:/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner:/home/<USER>/moveit2_ws/install/moveit_test:/home/<USER>/moveit2_ws/install/moveit_visual_tools:/home/<USER>/moveit2_ws/install/moveit_task_constructor_visualization:/home/<USER>/moveit2_ws/install/moveit_task_constructor_demo:/home/<USER>/moveit2_ws/install/moveit_task_constructor_capabilities:/home/<USER>/moveit2_ws/install/moveit_task_constructor_core:/home/<USER>/moveit2_ws/install/moveit_ros_control_interface:/home/<USER>/moveit2_ws/install/moveit_plugins:/home/<USER>/moveit2_ws/install/moveit_simple_controller_manager:/home/<USER>/moveit2_ws/install/moveit_setup_assistant:/home/<USER>/moveit2_ws/install/moveit_setup_srdf_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_core_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_controllers:/home/<USER>/moveit2_ws/install/moveit_setup_app_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_framework:/home/<USER>/moveit2_ws/install/moveit_servo:/home/<USER>/moveit2_ws/install/moveit_ros:/home/<USER>/moveit2_ws/install/moveit_ros_visualization:/home/<USER>/moveit2_ws/install/moveit_hybrid_planning:/home/<USER>/moveit2_ws/install/move_program:/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface:/home/<USER>/moveit2_ws/install/moveit_ros_benchmarks:/home/<USER>/moveit2_ws/install/moveit_ros_warehouse:/home/<USER>/moveit2_ws/install/moveit_ros_robot_interaction:/home/<USER>/moveit2_ws/install/moveit_ros_perception:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_pg70_support:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_moveit_config:/home/<USER>/moveit2_ws/install/moveit_ros_move_group:/home/<USER>/moveit2_ws/install/moveit_planners_ompl:/home/<USER>/moveit2_ws/install/moveit_ros_planning:/home/<USER>/moveit2_ws/install/moveit_ros_occupancy_map_monitor:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/moveit2_ws/install/moveit_planners_chomp:/home/<USER>/moveit2_ws/install/moveit_kinematics:/home/<USER>/moveit2_ws/install/moveit_chomp_optimizer_adapter:/home/<USER>/moveit2_ws/install/chomp_motion_planner:/home/<USER>/moveit2_ws/install/moveit_core:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/srdfdom:/home/<USER>/moveit2_ws/install/rviz_marker_tools:/home/<USER>/moveit2_ws/install/rosparam_shortcuts:/home/<USER>/moveit2_ws/install/moveit_task_constructor_msgs:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_support:/home/<USER>/moveit2_ws/install/moveit_resources:/home/<USER>/moveit2_ws/install/moveit_resources_pr2_description:/home/<USER>/moveit2_ws/install/moveit_resources_panda_moveit_config:/home/<USER>/moveit2_ws/install/moveit_resources_panda_description:/home/<USER>/moveit2_ws/install/moveit_resources_fanuc_moveit_config:/home/<USER>/moveit2_ws/install/moveit_resources_fanuc_description:/home/<USER>/moveit2_ws/install/moveit_common:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble'), ('SHELL', '/bin/bash'), ('CMAKE_ARGS', '-DPYTHON_EXECUTABLE=/usr/bin/python3.10 -DPython3_EXECUTABLE=/usr/bin/python3.10'), ('CMAKE_MODULE_PATH', '/home/<USER>/moveit2_ws/install/moveit_task_constructor_core/share/moveit_task_constructor_core/cmake'), ('VSCODE_GIT_ASKPASS_EXTRA_ARGS', ''), ('GIT_PAGER', 'cat'), ('PWD', '/home/<USER>/Code/ws_0/build/engineer_vision_pkg'), ('SSH_CONNECTION', '::1 0 ::1 22'), ('XDG_DATA_DIRS', '/usr/local/share:/usr/share:/var/lib/snapd/desktop'), ('PYTHONPATH', '/home/<USER>/moveit2_ws/install/moveit_task_constructor_core/local/lib/python3.10/dist-packages:/home/<USER>/moveit2_ws/install/moveit_configs_utils/lib/python3.10/site-packages:/home/<USER>/moveit2_ws/install/srdfdom/local/lib/python3.10/dist-packages:/home/<USER>/moveit2_ws/install/moveit_task_constructor_msgs/local/lib/python3.10/dist-packages:/home/<USER>/moveit2_ws/install/launch_param_builder/lib/python3.10/site-packages:/opt/ros/humble/lib/python3.10/site-packages:/opt/ros/humble/local/lib/python3.10/dist-packages'), ('COLCON', '1'), ('Python3_EXECUTABLE', '/usr/bin/python3.10'), ('CMAKE_PREFIX_PATH', '/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner_testutils:/home/<USER>/moveit2_ws/install/moveit_runtime:/home/<USER>/moveit2_ws/install/moveit2_tutorials:/home/<USER>/moveit2_ws/install/moveit:/home/<USER>/moveit2_ws/install/moveit_planners:/home/<USER>/moveit2_ws/install/pilz_industrial_motion_planner:/home/<USER>/moveit2_ws/install/moveit_test:/home/<USER>/moveit2_ws/install/moveit_visual_tools:/home/<USER>/moveit2_ws/install/moveit_task_constructor_visualization:/home/<USER>/moveit2_ws/install/moveit_task_constructor_demo:/home/<USER>/moveit2_ws/install/moveit_task_constructor_capabilities:/home/<USER>/moveit2_ws/install/moveit_task_constructor_core:/home/<USER>/moveit2_ws/install/moveit_ros_control_interface:/home/<USER>/moveit2_ws/install/moveit_plugins:/home/<USER>/moveit2_ws/install/moveit_simple_controller_manager:/home/<USER>/moveit2_ws/install/moveit_setup_assistant:/home/<USER>/moveit2_ws/install/moveit_setup_srdf_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_core_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_controllers:/home/<USER>/moveit2_ws/install/moveit_setup_app_plugins:/home/<USER>/moveit2_ws/install/moveit_setup_framework:/home/<USER>/moveit2_ws/install/moveit_servo:/home/<USER>/moveit2_ws/install/moveit_ros:/home/<USER>/moveit2_ws/install/moveit_ros_visualization:/home/<USER>/moveit2_ws/install/moveit_hybrid_planning:/home/<USER>/moveit2_ws/install/move_program:/home/<USER>/moveit2_ws/install/moveit_ros_planning_interface:/home/<USER>/moveit2_ws/install/moveit_ros_benchmarks:/home/<USER>/moveit2_ws/install/moveit_ros_warehouse:/home/<USER>/moveit2_ws/install/moveit_ros_robot_interaction:/home/<USER>/moveit2_ws/install/moveit_ros_perception:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_pg70_support:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_moveit_config:/home/<USER>/moveit2_ws/install/moveit_ros_move_group:/home/<USER>/moveit2_ws/install/moveit_planners_ompl:/home/<USER>/moveit2_ws/install/moveit_ros_planning:/home/<USER>/moveit2_ws/install/moveit_ros_occupancy_map_monitor:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_ikfast_manipulator_plugin:/home/<USER>/moveit2_ws/install/moveit_planners_chomp:/home/<USER>/moveit2_ws/install/moveit_kinematics:/home/<USER>/moveit2_ws/install/moveit_chomp_optimizer_adapter:/home/<USER>/moveit2_ws/install/chomp_motion_planner:/home/<USER>/moveit2_ws/install/moveit_core:/home/<USER>/moveit2_ws/install/srdfdom:/home/<USER>/moveit2_ws/install/rviz_marker_tools:/home/<USER>/moveit2_ws/install/rosparam_shortcuts:/home/<USER>/moveit2_ws/install/moveit_task_constructor_msgs:/home/<USER>/moveit2_ws/install/moveit_resources_prbt_support:/home/<USER>/moveit2_ws/install/moveit_resources:/home/<USER>/moveit2_ws/install/moveit_resources_pr2_description:/home/<USER>/moveit2_ws/install/moveit_resources_panda_moveit_config:/home/<USER>/moveit2_ws/install/moveit_resources_panda_description:/home/<USER>/moveit2_ws/install/moveit_resources_fanuc_moveit_config:/home/<USER>/moveit2_ws/install/moveit_resources_fanuc_description:/home/<USER>/moveit2_ws/install/moveit_common:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble')]), 'shell': False}
[11.385279] (engineer_vision_pkg) StdoutLine: {'line': b'-- Install configuration: ""\n'}
[11.387581] (engineer_vision_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/engineer_vision_pkg/video_capturer_node\n'}
[11.388977] (engineer_vision_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/engineer_vision_pkg/serial_node\n'}
[11.389968] (engineer_vision_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/engineer_vision_pkg/video_saver_node\n'}
[11.391671] (engineer_vision_pkg) StdoutLine: {'line': b'-- Installing: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/engineer_vision_pkg/RB_detector_node\n'}
[11.394325] (engineer_vision_pkg) StdoutLine: {'line': b'-- Set non-toolchain portion of runtime path of "/home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/engineer_vision_pkg/RB_detector_node" to ""\n'}
[11.394462] (engineer_vision_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/launch\n'}
[11.395087] (engineer_vision_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/launch/launch.py\n'}
[11.395307] (engineer_vision_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/ament_index/resource_index/package_run_dependencies/engineer_vision_pkg\n'}
[11.395384] (engineer_vision_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/ament_index/resource_index/parent_prefix_path/engineer_vision_pkg\n'}
[11.395424] (engineer_vision_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/environment/ament_prefix_path.sh\n'}
[11.395451] (engineer_vision_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/environment/ament_prefix_path.dsv\n'}
[11.395478] (engineer_vision_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/environment/path.sh\n'}
[11.395504] (engineer_vision_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/environment/path.dsv\n'}
[11.395529] (engineer_vision_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/local_setup.bash\n'}
[11.395555] (engineer_vision_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/local_setup.sh\n'}
[11.395581] (engineer_vision_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/local_setup.zsh\n'}
[11.395607] (engineer_vision_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/local_setup.dsv\n'}
[11.395633] (engineer_vision_pkg) StdoutLine: {'line': b'-- Installing: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.dsv\n'}
[11.396242] (engineer_vision_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/ament_index/resource_index/packages/engineer_vision_pkg\n'}
[11.396338] (engineer_vision_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/cmake/engineer_vision_pkgConfig.cmake\n'}
[11.396388] (engineer_vision_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/cmake/engineer_vision_pkgConfig-version.cmake\n'}
[11.396437] (engineer_vision_pkg) StdoutLine: {'line': b'-- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.xml\n'}
[11.398846] (engineer_vision_pkg) CommandEnded: {'returncode': 0}
[11.417007] (engineer_vision_pkg) JobEnded: {'identifier': 'engineer_vision_pkg', 'rc': 0}
[11.418334] (-) EventReactorShutdown: {}
