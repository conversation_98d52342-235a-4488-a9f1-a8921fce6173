[0.035s] Invoking command in '/home/<USER>/Code/ws_0/build/engineer_vision_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/engineer_vision_pkg -- -j8 -l8
[0.110s] [ 25%] Built target video_capturer_node
[0.112s] [ 50%] Built target serial_node
[0.112s] [ 62%] [32mBuilding CXX object CMakeFiles/RB_detector_node.dir/src/RB_detector_node.cpp.o[0m
[0.117s] [ 87%] Built target video_saver_node
[8.188s] [100%] [32m[1mLinking CXX executable RB_detector_node[0m
[8.457s] /usr/bin/ld: warning: libopencv_imgcodecs.so.4.5d, needed by /opt/ros/humble/lib/libcv_bridge.so, may conflict with libopencv_imgcodecs.so.411
[8.461s] /usr/bin/ld: warning: libopencv_imgproc.so.4.5d, needed by /opt/ros/humble/lib/libcv_bridge.so, may conflict with libopencv_imgproc.so.411
[8.466s] /usr/bin/ld: warning: libopencv_core.so.4.5d, needed by /opt/ros/humble/lib/libcv_bridge.so, may conflict with libopencv_core.so.411
[9.060s] [100%] Built target RB_detector_node
[9.073s] Invoked command in '/home/<USER>/Code/ws_0/build/engineer_vision_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/engineer_vision_pkg -- -j8 -l8
[9.075s] Invoking command in '/home/<USER>/Code/ws_0/build/engineer_vision_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/engineer_vision_pkg
[9.090s] -- Install configuration: ""
[9.094s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/engineer_vision_pkg/video_capturer_node
[9.095s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/engineer_vision_pkg/serial_node
[9.096s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/engineer_vision_pkg/video_saver_node
[9.098s] -- Installing: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/engineer_vision_pkg/RB_detector_node
[9.100s] -- Set non-toolchain portion of runtime path of "/home/<USER>/Code/ws_0/install/engineer_vision_pkg/lib/engineer_vision_pkg/RB_detector_node" to ""
[9.100s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/launch
[9.100s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/launch/launch.py
[9.100s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/ament_index/resource_index/package_run_dependencies/engineer_vision_pkg
[9.100s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/ament_index/resource_index/parent_prefix_path/engineer_vision_pkg
[9.100s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/environment/ament_prefix_path.sh
[9.100s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/environment/ament_prefix_path.dsv
[9.100s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/environment/path.sh
[9.100s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/environment/path.dsv
[9.100s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/local_setup.bash
[9.100s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/local_setup.sh
[9.100s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/local_setup.zsh
[9.100s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/local_setup.dsv
[9.100s] -- Installing: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.dsv
[9.100s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/ament_index/resource_index/packages/engineer_vision_pkg
[9.100s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/cmake/engineer_vision_pkgConfig.cmake
[9.101s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/cmake/engineer_vision_pkgConfig-version.cmake
[9.101s] -- Up-to-date: /home/<USER>/Code/ws_0/install/engineer_vision_pkg/share/engineer_vision_pkg/package.xml
[9.103s] Invoked command in '/home/<USER>/Code/ws_0/build/engineer_vision_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/engineer_vision_pkg
