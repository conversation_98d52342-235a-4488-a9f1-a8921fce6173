[0.017s] Invoking command in '/home/<USER>/Code/ws_0/build/arm_controller_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/arm_controller_pkg -- -j8 -l8
[0.189s] [ 25%] [32mBuilding CXX object CMakeFiles/RB_builder_node.dir/src/RB_builder_node.cpp.o[0m
[0.191s] [ 75%] Built target path_planning_node
[10.595s] [100%] [32m[1mLinking CXX executable RB_builder_node[0m
[11.460s] [100%] Built target RB_builder_node
[11.478s] Invoked command in '/home/<USER>/Code/ws_0/build/arm_controller_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --build /home/<USER>/Code/ws_0/build/arm_controller_pkg -- -j8 -l8
[11.496s] Invoking command in '/home/<USER>/Code/ws_0/build/arm_controller_pkg': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/arm_controller_pkg
[11.502s] -- Install configuration: ""
[11.504s] -- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/lib/arm_controller_pkg/path_planning_node
[11.506s] -- Installing: /home/<USER>/Code/ws_0/install/arm_controller_pkg/lib/arm_controller_pkg/RB_builder_node
[11.509s] -- Set non-toolchain portion of runtime path of "/home/<USER>/Code/ws_0/install/arm_controller_pkg/lib/arm_controller_pkg/RB_builder_node" to ""
[11.509s] -- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/ament_index/resource_index/package_run_dependencies/arm_controller_pkg
[11.509s] -- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/ament_index/resource_index/parent_prefix_path/arm_controller_pkg
[11.509s] -- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/environment/ament_prefix_path.sh
[11.510s] -- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/environment/ament_prefix_path.dsv
[11.510s] -- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/environment/path.sh
[11.510s] -- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/environment/path.dsv
[11.510s] -- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/local_setup.bash
[11.510s] -- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/local_setup.sh
[11.510s] -- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/local_setup.zsh
[11.510s] -- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/local_setup.dsv
[11.510s] -- Installing: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.dsv
[11.511s] -- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/ament_index/resource_index/packages/arm_controller_pkg
[11.511s] -- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/cmake/arm_controller_pkgConfig.cmake
[11.511s] -- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/cmake/arm_controller_pkgConfig-version.cmake
[11.511s] -- Up-to-date: /home/<USER>/Code/ws_0/install/arm_controller_pkg/share/arm_controller_pkg/package.xml
[11.514s] Invoked command in '/home/<USER>/Code/ws_0/build/arm_controller_pkg' returned '0': CMAKE_PREFIX_PATH=${CMAKE_PREFIX_PATH}:/home/<USER>/moveit2_ws/install/moveit_configs_utils:/home/<USER>/moveit2_ws/install/launch_param_builder:/opt/ros/humble /usr/local/cmake/bin/cmake --install /home/<USER>/Code/ws_0/build/arm_controller_pkg
