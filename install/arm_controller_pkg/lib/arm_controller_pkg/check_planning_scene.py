#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from moveit_msgs.msg import PlanningScene
from moveit_msgs.srv import GetPlanningScene
import time

class PlanningSceneChecker(Node):
    def __init__(self):
        super().__init__('planning_scene_checker')
        
        # 订阅planning scene话题
        self.scene_subscriber = self.create_subscription(
            PlanningScene,
            '/monitored_planning_scene',
            self.scene_callback,
            10
        )
        
        # 创建服务客户端
        self.get_scene_client = self.create_client(
            GetPlanningScene,
            '/get_planning_scene'
        )
        
        self.get_logger().info("Planning Scene Checker started")
        self.get_logger().info("Listening for planning scene updates...")
        
        # 定时检查
        self.timer = self.create_timer(5.0, self.check_scene_periodically)
        
    def scene_callback(self, msg):
        """处理planning scene更新"""
        collision_objects = msg.world.collision_objects
        
        if collision_objects:
            self.get_logger().info(f"Planning scene updated! Found {len(collision_objects)} collision objects:")
            for obj in collision_objects:
                self.get_logger().info(f"  - Object ID: {obj.id}")
                self.get_logger().info(f"    Frame: {obj.header.frame_id}")
                self.get_logger().info(f"    Primitives: {len(obj.primitives)}")
                if obj.id == "cube_slot":
                    self.get_logger().info("  *** CUBE SLOT FOUND! ***")
                    for i, primitive in enumerate(obj.primitives):
                        dims = primitive.dimensions
                        self.get_logger().info(f"    Box {i}: [{dims[0]:.3f}, {dims[1]:.3f}, {dims[2]:.3f}]")
        else:
            self.get_logger().info("Planning scene updated - no collision objects found")
    
    def check_scene_periodically(self):
        """定期检查planning scene状态"""
        if self.get_scene_client.service_is_ready():
            request = GetPlanningScene.Request()
            request.components.components = request.components.WORLD_OBJECT_NAMES
            
            future = self.get_scene_client.call_async(request)
            future.add_done_callback(self.scene_response_callback)
        else:
            self.get_logger().warn("GetPlanningScene service not available")
    
    def scene_response_callback(self, future):
        """处理planning scene服务响应"""
        try:
            response = future.result()
            objects = response.scene.world.collision_objects
            
            self.get_logger().info(f"[Periodic Check] Total objects in scene: {len(objects)}")
            
            cube_slot_found = False
            for obj in objects:
                if obj.id == "cube_slot":
                    cube_slot_found = True
                    self.get_logger().info(f"[Periodic Check] ✓ Cube slot found with {len(obj.primitives)} primitives")
                    break
            
            if not cube_slot_found and len(objects) > 0:
                object_names = [obj.id for obj in objects]
                self.get_logger().info(f"[Periodic Check] Objects found: {object_names}")
            elif not cube_slot_found:
                self.get_logger().warn("[Periodic Check] ✗ No cube slot found in planning scene")
                
        except Exception as e:
            self.get_logger().error(f"Failed to get planning scene: {e}")

def main(args=None):
    rclpy.init(args=args)
    
    checker = PlanningSceneChecker()
    
    try:
        rclpy.spin(checker)
    except KeyboardInterrupt:
        pass
    finally:
        checker.destroy_node()
        rclpy.shutdown()

if __name__ == '__main__':
    main()
