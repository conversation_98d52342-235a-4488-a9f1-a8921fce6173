#!/usr/bin/env python3

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, ExecuteProcess
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch.conditions import IfCondition
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare
from ament_index_python.packages import get_package_share_directory

def generate_launch_description():
    # 获取包路径
    pkg_share = FindPackageShare('arm_controller_pkg').find('arm_controller_pkg')
    
    # RViz配置文件路径
    rviz_config_file = PathJoinSubstitution([
        FindPackageShare('arm_controller_pkg'),
        'config',
        'cube_slot_visualization.rviz'
    ])
    
    # 声明启动参数
    use_rviz_arg = DeclareLaunchArgument(
        'use_rviz',
        default_value='true',
        description='Whether to start RViz'
    )
    
    # RB_builder_node节点
    rb_builder_node = Node(
        package='arm_controller_pkg',
        executable='RB_builder_node',
        name='rb_builder_node',
        output='screen',
        parameters=[],
        remappings=[]
    )
    
    # RViz节点
    rviz_node = Node(
        package='rviz2',
        executable='rviz2',
        name='rviz2',
        arguments=['-d', rviz_config_file],
        output='screen',
        condition=IfCondition(LaunchConfiguration('use_rviz'))
    )
    
    # MoveIt planning scene monitor (确保planning scene正常工作)
    planning_scene_monitor = Node(
        package='moveit_ros_planning',
        executable='planning_scene_monitor',
        name='planning_scene_monitor',
        output='screen',
        parameters=[
            {'publish_planning_scene': True},
            {'publish_geometry_updates': True},
            {'publish_state_updates': True},
            {'publish_transforms_updates': True}
        ]
    )

    return LaunchDescription([
        use_rviz_arg,
        rb_builder_node,
        planning_scene_monitor,
        rviz_node,
    ])
